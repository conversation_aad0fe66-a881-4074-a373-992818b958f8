<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE TS>
<TS version="2.1">
<context>
    <name></name>
    <message>
        <location filename="../../views/labeling/utils/shape.py" line="28"/>
        <source>Warning</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/shape.py" line="29"/>
        <source>Current annotation will be changed</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/shape.py" line="30"/>
        <source>Are you sure you want to perform this conversion?</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/shape.py" line="41"/>
        <source>Converting...</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/crop.py" line="343"/>
        <source>Cancel</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/crop.py" line="351"/>
        <source>Progress</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/shape.py" line="121"/>
        <source>Conversion completed successfully!</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/shape.py" line="133"/>
        <source>Error occurred while converting shapes!</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/crop.py" line="203"/>
        <source>Please load an image folder before proceeding!</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/crop.py" line="213"/>
        <source>Cropped Image Options</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/crop.py" line="222"/>
        <source>Save Path</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/crop.py" line="235"/>
        <source>Select Save Directory</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/crop.py" line="244"/>
        <source>Browse</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/crop.py" line="254"/>
        <source>Minimum width:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/crop.py" line="270"/>
        <source>Minimum height:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/crop.py" line="293"/>
        <source>OK</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/crop.py" line="313"/>
        <source>Output Directory Exists!</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/crop.py" line="314"/>
        <source>Directory already exists. Choose an action:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/crop.py" line="315"/>
        <source>• Overwrite - Overwrite existing directory
• Cancel - Abort export</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/crop.py" line="322"/>
        <source>Overwrite</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/crop.py" line="343"/>
        <source>Processing...</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/crop.py" line="411"/>
        <source>Cropped images successfully!
Results have been saved to:
{save_path}</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/crop.py" line="423"/>
        <source>Error occurred while exporting cropped images!</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>AboutDialog</name>
    <message>
        <location filename="../../views/labeling/widgets/about_dialog.py" line="114"/>
        <source>Website</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/about_dialog.py" line="118"/>
        <source>Copy App Info</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/about_dialog.py" line="122"/>
        <source>Report Issue</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/about_dialog.py" line="180"/>
        <source>Changelog</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/about_dialog.py" line="184"/>
        <source>Check for Updates</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/about_dialog.py" line="374"/>
        <source>Copied!</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/about_dialog.py" line="265"/>
        <source>No Updates Available</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/about_dialog.py" line="272"/>
        <source>GitHub API error: {response.status_code}</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/about_dialog.py" line="280"/>
        <source>Check update error: {str(e)}</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/about_dialog.py" line="317"/>
        <source>Update Available</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/about_dialog.py" line="352"/>
        <source>Cancel</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/about_dialog.py" line="355"/>
        <source>Download</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>ApiTokenDialog</name>
    <message>
        <location filename="../../views/labeling/widgets/api_token_dialog.py" line="29"/>
        <source>Set API Token</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/api_token_dialog.py" line="34"/>
        <source>Enter your API Token:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/api_token_dialog.py" line="41"/>
        <source>Enter API key</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/api_token_dialog.py" line="85"/>
        <source>Show</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/api_token_dialog.py" line="86"/>
        <source>Hide</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>AutoLabelingWidget</name>
    <message>
        <location filename="../../views/labeling/widgets/auto_labeling/auto_labeling.py" line="430"/>
        <source>Coarse Grained</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/auto_labeling/auto_labeling.py" line="431"/>
        <source>Fine Grained</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/auto_labeling/auto_labeling.py" line="456"/>
        <source>Caption</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/auto_labeling/auto_labeling.py" line="457"/>
        <source>Detailed Caption</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/auto_labeling/auto_labeling.py" line="458"/>
        <source>More Detailed Caption</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/auto_labeling/auto_labeling.py" line="459"/>
        <source>Object Detection</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/auto_labeling/auto_labeling.py" line="460"/>
        <source>Region Proposal</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/auto_labeling/auto_labeling.py" line="461"/>
        <source>Dense Region Caption</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/auto_labeling/auto_labeling.py" line="462"/>
        <source>Refer-Exp Segmentation</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/auto_labeling/auto_labeling.py" line="463"/>
        <source>Region to Segmentation</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/auto_labeling/auto_labeling.py" line="464"/>
        <source>OVD</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/auto_labeling/auto_labeling.py" line="465"/>
        <source>Caption to Parse Grounding</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/auto_labeling/auto_labeling.py" line="466"/>
        <source>Region to Category</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/auto_labeling/auto_labeling.py" line="467"/>
        <source>Region to Description</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/auto_labeling/auto_labeling.py" line="468"/>
        <source>OCR</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/auto_labeling/auto_labeling.py" line="469"/>
        <source>OCR with Region</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/auto_labeling/auto_labeling.py" line="205"/>
        <source>Existing shapes will be preserved during updates. Click to switch to overwriting.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/auto_labeling/auto_labeling.py" line="208"/>
        <source>Existing shapes will be overwritten by new shapes during updates. Click to switch to preserving.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/auto_labeling/auto_labeling.py" line="138"/>
        <source>You can set the API token via the GROUNDING_DINO_API_TOKEN environment variable</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/auto_labeling/auto_labeling.py" line="212"/>
        <source>Replace (Off)</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/auto_labeling/auto_labeling.py" line="212"/>
        <source>Replace (On)</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>BatchProcessDialog</name>
    <message>
        <location filename="../../views/labeling/chatbot/general.py" line="23"/>
        <source>Batch Process All Images</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/chatbot/general.py" line="44"/>
        <source>Enter the prompt to apply to all images:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/chatbot/general.py" line="60"/>
        <source>Type your prompt here and use `@image` to reference the image.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/chatbot/general.py" line="108"/>
        <source>Cancel</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/chatbot/general.py" line="135"/>
        <source>Confirm</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>BrightnessContrastDialog</name>
    <message>
        <location filename="../../views/labeling/widgets/brightness_contrast_dialog.py" line="17"/>
        <source>Brightness/Contrast</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/brightness_contrast_dialog.py" line="112"/>
        <source>Reset</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/brightness_contrast_dialog.py" line="134"/>
        <source>Confirm</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/brightness_contrast_dialog.py" line="64"/>
        <source>Brightness:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/brightness_contrast_dialog.py" line="86"/>
        <source>Contrast:</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>Canvas</name>
    <message>
        <location filename="../../views/labeling/widgets/canvas.py" line="122"/>
        <source>Loading...</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/canvas.py" line="250"/>
        <source>Auto Labeling</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/canvas.py" line="252"/>
        <source>Drawing</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/canvas.py" line="254"/>
        <source>Editing</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/canvas.py" line="256"/>
        <source>Unknown</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/canvas.py" line="409"/>
        <source>Image</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/canvas.py" line="460"/>
        <source>Click &amp; drag to move shape &apos;%s&apos;</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/canvas.py" line="424"/>
        <source>Click &amp; drag to move point of shape &apos;%s&apos;</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/canvas.py" line="439"/>
        <source>Click to create point of shape &apos;%s&apos;</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>ChatMessage</name>
    <message>
        <location filename="../../views/labeling/chatbot/chat.py" line="223"/>
        <source>Resend</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/chatbot/chat.py" line="232"/>
        <source>Cancel</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/chatbot/chat.py" line="239"/>
        <source>Save</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/chatbot/chat.py" line="661"/>
        <source>Are you sure to delete this message forever?</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/chatbot/chat.py" line="746"/>
        <source>Copy message</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/chatbot/chat.py" line="752"/>
        <source>Edit message</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/chatbot/chat.py" line="757"/>
        <source>Regenerate response</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/chatbot/chat.py" line="763"/>
        <source>Delete message</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>ChatbotDialog</name>
    <message>
        <location filename="../../views/labeling/widgets/chatbot_dialog.py" line="89"/>
        <source>Clear Chat</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/chatbot_dialog.py" line="90"/>
        <source>Open Image Folder</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/chatbot_dialog.py" line="93"/>
        <source>Open Image File</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/chatbot_dialog.py" line="96"/>
        <source>Previous Image</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/chatbot_dialog.py" line="99"/>
        <source>Next Image</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/chatbot_dialog.py" line="100"/>
        <source>Run All Images</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/chatbot_dialog.py" line="103"/>
        <source>Import/Export Dataset</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/chatbot_dialog.py" line="238"/>
        <source>Type something and Ctrl+↩︎ to send. Use @image to add an image.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/chatbot_dialog.py" line="459"/>
        <source>API Address</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/chatbot_dialog.py" line="500"/>
        <source>API Key</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/chatbot_dialog.py" line="533"/>
        <source>Enter API key</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/chatbot_dialog.py" line="558"/>
        <source>Model Name</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/chatbot_dialog.py" line="605"/>
        <source>System instruction</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/chatbot_dialog.py" line="623"/>
        <source>Temperature</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/chatbot_dialog.py" line="659"/>
        <source>Precise</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/chatbot_dialog.py" line="665"/>
        <source>Neutral</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/chatbot_dialog.py" line="671"/>
        <source>Creative</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/chatbot_dialog.py" line="685"/>
        <source>Max output tokens</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/chatbot_dialog.py" line="711"/>
        <source>Backend</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/chatbot_dialog.py" line="712"/>
        <source>Generation</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/chatbot_dialog.py" line="965"/>
        <source>Image not available</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/chatbot_dialog.py" line="1367"/>
        <source>Inferencing...</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/chatbot_dialog.py" line="1367"/>
        <source>Cancel</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/chatbot_dialog.py" line="1375"/>
        <source>Progress</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/chatbot_dialog.py" line="1488"/>
        <source>Dataset Operations</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/chatbot_dialog.py" line="1499"/>
        <source>Import Dataset</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/chatbot_dialog.py" line="1502"/>
        <source>Export Dataset</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/chatbot_dialog.py" line="1643"/>
        <source>Export Error</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/chatbot_dialog.py" line="1655"/>
        <source>No file is currently open.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/chatbot_dialog.py" line="1521"/>
        <source>Select Export Directory</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/chatbot_dialog.py" line="1552"/>
        <source>No labeling files found in the current directory.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/chatbot_dialog.py" line="1603"/>
        <source>Error processing {json_file}: {str(e)}</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/chatbot_dialog.py" line="1608"/>
        <source>No valid chat data found to export.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/chatbot_dialog.py" line="1633"/>
        <source>Export Successful</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/chatbot_dialog.py" line="1633"/>
        <source>Dataset exported successfully to:
{zip_path}</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/chatbot_dialog.py" line="1643"/>
        <source>An error occurred during export:
{str(e)}</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/chatbot_dialog.py" line="1802"/>
        <source>Import Error</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/chatbot_dialog.py" line="1663"/>
        <source>Select Dataset File</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/chatbot_dialog.py" line="1678"/>
        <source>Invalid dataset format. Expected a list of records.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/chatbot_dialog.py" line="1781"/>
        <source>Import Successful</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/chatbot_dialog.py" line="1790"/>
        <source>Import Notice</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/chatbot_dialog.py" line="1790"/>
        <source>No valid items were found to import. Make sure images are available.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/chatbot_dialog.py" line="1802"/>
        <source>An error occurred during import:
{str(e)}</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/chatbot_dialog.py" line="2257"/>
        <source>Are you sure you want to clear the entire conversation?</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/chatbot_dialog.py" line="1401"/>
        <source>Processing image %d/%d...</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/chatbot_dialog.py" line="1777"/>
        <source>Successfully imported {0} items to:
{1}</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>CrosshairSettingsDialog</name>
    <message>
        <location filename="../../views/labeling/widgets/crosshair_settings_dialog.py" line="17"/>
        <source>Crosshair Settings</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/crosshair_settings_dialog.py" line="118"/>
        <source>Show Crosshair:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/crosshair_settings_dialog.py" line="129"/>
        <source>Line width:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/crosshair_settings_dialog.py" line="151"/>
        <source>Line Opacity:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/crosshair_settings_dialog.py" line="174"/>
        <source>Line Color:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/crosshair_settings_dialog.py" line="192"/>
        <source>Choose Color</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/crosshair_settings_dialog.py" line="221"/>
        <source>Reset</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/crosshair_settings_dialog.py" line="242"/>
        <source>OK</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/crosshair_settings_dialog.py" line="263"/>
        <source>Cancel</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>DigitShortcutDialog</name>
    <message>
        <location filename="../../views/labeling/widgets/label_dialog.py" line="110"/>
        <source>Digit Shortcut Manager</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/label_dialog.py" line="173"/>
        <source>Configure digit keys (0-9) for quick shape creation:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/label_dialog.py" line="183"/>
        <source>Digit</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/label_dialog.py" line="183"/>
        <source>Drawing Mode</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/label_dialog.py" line="183"/>
        <source>Label</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/label_dialog.py" line="208"/>
        <source>None</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/label_dialog.py" line="252"/>
        <source>Reset</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/label_dialog.py" line="273"/>
        <source>OK</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/label_dialog.py" line="294"/>
        <source>Cancel</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/label_dialog.py" line="344"/>
        <source>Required</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/label_dialog.py" line="349"/>
        <source>Confirm Reset</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/label_dialog.py" line="349"/>
        <source>Are you sure you want to reset all shortcuts? This cannot be undone.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/label_dialog.py" line="398"/>
        <source>Validation Error</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/label_dialog.py" line="398"/>
        <source>Please provide a label for each enabled drawing mode.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/label_dialog.py" line="414"/>
        <source>Digit shortcuts saved successfully</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>ExportThread</name>
    <message>
        <location filename="../../views/labeling/utils/export.py" line="83"/>
        <source>Please load an image folder before proceeding!</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/export.py" line="101"/>
        <source>Select a specific yolo-pose config file</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/export.py" line="1445"/>
        <source>Select a specific classes file</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/export.py" line="1165"/>
        <source>Export options</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/export.py" line="1174"/>
        <source>Export path</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/export.py" line="1191"/>
        <source>Select Export Directory</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/export.py" line="1379"/>
        <source>Browse</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/export.py" line="345"/>
        <source>Export Options</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/export.py" line="348"/>
        <source>Save with images?</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/export.py" line="352"/>
        <source>Skip empty labels?</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/export.py" line="1549"/>
        <source>Cancel</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/export.py" line="1485"/>
        <source>OK</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/export.py" line="1236"/>
        <source>Output Directory Exists!</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/export.py" line="1237"/>
        <source>Directory already exists. Choose an action:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/export.py" line="741"/>
        <source>• Yes    - Merge with existing files
• No     - Delete existing directory
• Cancel - Abort export</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/export.py" line="749"/>
        <source>Yes</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/export.py" line="750"/>
        <source>No</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/export.py" line="1549"/>
        <source>Exporting...</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/export.py" line="1553"/>
        <source>Progress</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/export.py" line="1604"/>
        <source>Error occurred while exporting annotations!</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/export.py" line="489"/>
        <source>Select a specific coco-pose config file</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/export.py" line="1238"/>
        <source>• Overwrite - Overwrite existing directory
• Cancel - Abort export</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/export.py" line="1514"/>
        <source>Overwrite</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/export.py" line="833"/>
        <source>Select a specific color_map file</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/export.py" line="1572"/>
        <source>Exporting annotations successfully!
Results have been saved to:
%s</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/export.py" line="1338"/>
        <source>Export VLM-R1 OVD Annotation</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/export.py" line="1348"/>
        <source>Export to</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/export.py" line="1367"/>
        <source>Select Export File</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/export.py" line="1395"/>
        <source>Prefix:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/export.py" line="1406"/>
        <source>Optional prefix for image filenames (e.g., &apos;path/to/images/&apos;)</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/export.py" line="1421"/>
        <source>{}</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/export.py" line="1431"/>
        <source>Use specific classes? (Optional)</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/export.py" line="1453"/>
        <source>Upload</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/export.py" line="1465"/>
        <source>Hint: If you don&apos;t upload a specific classes file, all unique labels found in one of the annotations will be used for the export.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/export.py" line="1506"/>
        <source>File Exists!</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/export.py" line="1507"/>
        <source>File already exists. Choose an action:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/export.py" line="1508"/>
        <source>• Overwrite - Replace existing file
• Cancel - Abort export</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/export.py" line="1539"/>
        <source>Error initializing export: %s</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>FrameExtractionDialog</name>
    <message>
        <location filename="../../views/labeling/utils/video.py" line="43"/>
        <source>Frame Extraction Settings</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/video.py" line="49"/>
        <source>Extract every N frames (fps: %.2f):</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/video.py" line="66"/>
        <source>Filename prefix:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/video.py" line="77"/>
        <source>Number sequence length:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/video.py" line="102"/>
        <source>OK</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/video.py" line="374"/>
        <source>Cancel</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/video.py" line="123"/>
        <source>Example output: {example}</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/video.py" line="226"/>
        <source>Extracting frames using ffmpeg...</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/video.py" line="382"/>
        <source>Progress</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/video.py" line="308"/>
        <source>ffmpeg failed. Check logs.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/video.py" line="342"/>
        <source>ffmpeg not found.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/video.py" line="374"/>
        <source>Extracting frames (OpenCV)... Please wait...</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/video.py" line="500"/>
        <source>Open Video file</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/video.py" line="520"/>
        <source>Warning</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/video.py" line="521"/>
        <source>Directory Already Exists</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>GroupIDFilterComboBox</name>
    <message>
        <location filename="../../views/labeling/widgets/filter_label_widget.py" line="9"/>
        <source>Group ID Filter</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>GroupIDModifyDialog</name>
    <message>
        <location filename="../../views/labeling/widgets/label_dialog.py" line="473"/>
        <source>Group ID Change Manager</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/label_dialog.py" line="577"/>
        <source>Cancel</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/label_dialog.py" line="599"/>
        <source>Confirm</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/label_dialog.py" line="701"/>
        <source>Group IDs modified successfully!</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/label_dialog.py" line="709"/>
        <source>An error occurred while updating the Group IDs.</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>LabelDialog</name>
    <message>
        <location filename="../../views/labeling/widgets/label_dialog.py" line="1153"/>
        <source>Enter object label</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/label_dialog.py" line="1169"/>
        <source>Group ID</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/label_dialog.py" line="1178"/>
        <source>useDifficult</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/label_dialog.py" line="1183"/>
        <source>Enter linking, e.g., [0,1]</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/label_dialog.py" line="1197"/>
        <source>Add</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/label_dialog.py" line="1228"/>
        <source>Label description</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/label_dialog.py" line="1296"/>
        <source>Duplicate Entry</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/label_dialog.py" line="1296"/>
        <source>This linking pair already exists.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/label_dialog.py" line="1310"/>
        <source>Invalid Input</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/label_dialog.py" line="1310"/>
        <source>Please enter a valid list of linking pairs like [1,2].</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>LabelFilterComboBox</name>
    <message>
        <location filename="../../views/labeling/widgets/filter_label_widget.py" line="28"/>
        <source>Label Filter</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>LabelModifyDialog</name>
    <message>
        <location filename="../../views/labeling/widgets/label_dialog.py" line="802"/>
        <source>Label Change Manager</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/label_dialog.py" line="1112"/>
        <source>Invalid Range</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/label_dialog.py" line="1112"/>
        <source>Please enter a valid range.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/label_dialog.py" line="992"/>
        <source>Labels modified successfully!</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/label_dialog.py" line="1000"/>
        <source>An error occurred while updating the labels.</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>LabelingWidget</name>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="164"/>
        <source>Flags</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="188"/>
        <source>Objects</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="196"/>
        <source>Select label to start annotating for it. Press &apos;Esc&apos; to deselect.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="210"/>
        <source>Labels</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="218"/>
        <source>Search Filename</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="291"/>
        <source>Open image or label file</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="305"/>
        <source>&amp;Open Dir</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="305"/>
        <source>Open Dir</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="312"/>
        <source>&amp;Next Image</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="320"/>
        <source>&amp;Prev Image</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="344"/>
        <source>&amp;Save</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="344"/>
        <source>Save labels to file</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="352"/>
        <source>&amp;Save As</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="352"/>
        <source>Save labels to a different file</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="369"/>
        <source>&amp;Delete File</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="369"/>
        <source>Delete current label file</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="386"/>
        <source>&amp;Change Output Dir</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="386"/>
        <source>Change where annotations are loaded/saved</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="394"/>
        <source>Save &amp;Automatically</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="394"/>
        <source>Save automatically</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="404"/>
        <source>Save With Image Data</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="404"/>
        <source>Save image data in label file</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="413"/>
        <source>&amp;Close</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="413"/>
        <source>Close current file</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="421"/>
        <source>Keep Previous Annotation</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="460"/>
        <source>Create Polygons</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="460"/>
        <source>Start drawing polygons</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="468"/>
        <source>Create Rectangle</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="468"/>
        <source>Start drawing rectangles</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="484"/>
        <source>Create Circle</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="484"/>
        <source>Start drawing circles</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="492"/>
        <source>Create Line</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="492"/>
        <source>Start drawing lines</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="500"/>
        <source>Create Point</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="500"/>
        <source>Start drawing points</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="508"/>
        <source>Create LineStrip</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="508"/>
        <source>Start drawing linestrip. Ctrl+LeftClick ends creation.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="576"/>
        <source>Edit Object</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="576"/>
        <source>Move and edit the selected polygons</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="584"/>
        <source>Group Selected Shapes</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="584"/>
        <source>Group shapes by assigning a same group_id</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="592"/>
        <source>Ungroup Selected Shapes</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="592"/>
        <source>Ungroup shapes</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="601"/>
        <source>Delete</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="601"/>
        <source>Delete the selected polygons</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="609"/>
        <source>Duplicate Polygons</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="609"/>
        <source>Create a duplicate of the selected polygons</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="617"/>
        <source>Copy Object</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="617"/>
        <source>Copy selected polygons to clipboard</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="625"/>
        <source>Paste Object</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="625"/>
        <source>Paste copied polygons</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="633"/>
        <source>Undo last point</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="633"/>
        <source>Undo last drawn point</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="641"/>
        <source>Remove Selected Point</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="641"/>
        <source>Remove selected point from polygon</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="650"/>
        <source>Undo</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="650"/>
        <source>Undo last add and edit of shape</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="760"/>
        <source>&amp;Documentation</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="760"/>
        <source>Show documentation</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="784"/>
        <source>Zoom in or out of the image. Also accessible with {} and {} from the canvas.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="784"/>
        <source>Ctrl+Wheel</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="799"/>
        <source>Zoom &amp;In</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="799"/>
        <source>Increase zoom level</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="807"/>
        <source>&amp;Zoom Out</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="807"/>
        <source>Decrease zoom level</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="815"/>
        <source>&amp;Original size</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="815"/>
        <source>Zoom to original size</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="823"/>
        <source>&amp;Keep Previous Scale</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="823"/>
        <source>Keep previous zoom scale</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="847"/>
        <source>&amp;Fit Window</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="847"/>
        <source>Zoom follows window size</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="856"/>
        <source>Fit &amp;Width</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="856"/>
        <source>Zoom follows window width</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="879"/>
        <source>&amp;Show Groups</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="879"/>
        <source>Show shape groups</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="889"/>
        <source>&amp;Show Texts</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="889"/>
        <source>Show text above shapes</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1257"/>
        <source>&amp;Edit Label</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1257"/>
        <source>Modify the label of the selected polygon</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1266"/>
        <source>Fill Drawing Polygon</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1266"/>
        <source>Fill polygon while drawing</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1278"/>
        <source>&amp;Auto Labeling</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1278"/>
        <source>Auto Labeling</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1478"/>
        <source>&amp;File</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1478"/>
        <source>&amp;Edit</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1478"/>
        <source>&amp;View</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1478"/>
        <source>&amp;Language</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1478"/>
        <source>&amp;Help</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1478"/>
        <source>Open &amp;Recent</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1713"/>
        <source>Please wait...</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1877"/>
        <source>Please restart the application to apply changes.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1884"/>
        <source>Mode:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1885"/>
        <source>Shortcuts:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="4207"/>
        <source>Invalid label</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="4207"/>
        <source>Invalid label &apos;{}&apos; with validation type &apos;{}&apos;</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="2910"/>
        <source>Error saving label data</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="2910"/>
        <source>&lt;b&gt;%s&lt;/b&gt;</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="3375"/>
        <source>Error opening file</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="3313"/>
        <source>No such file: &lt;b&gt;%s&lt;/b&gt;</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="3320"/>
        <source>Loading %s...</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="3335"/>
        <source>&lt;p&gt;&lt;b&gt;%s&lt;/b&gt;&lt;/p&gt;&lt;p&gt;Make sure &lt;i&gt;%s&lt;/i&gt; is a valid label file.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="3382"/>
        <source>Error reading %s</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="3375"/>
        <source>&lt;p&gt;Make sure &lt;i&gt;{0}&lt;/i&gt; is a valid image file.&lt;br/&gt;Supported image formats: {1}&lt;/p&gt;</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="3463"/>
        <source>Loaded %s</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="3648"/>
        <source>Image &amp; Label files (%s)</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="3654"/>
        <source>%s - Choose Image or Label file</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="3672"/>
        <source>%s - Save/Load Annotations in Directory</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="3686"/>
        <source>%s . Annotations will be saved/loaded in %s</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="3718"/>
        <source>%s - Choose File</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="3743"/>
        <source>Label files (*%s)</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="3743"/>
        <source>Choose File</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="3785"/>
        <source>You are about to permanently delete this label file, proceed anyway?</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="3816"/>
        <source>Attention</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="3880"/>
        <source>Save annotations to &quot;{self.filename!r}&quot; before closing?</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="3883"/>
        <source>Save annotations?</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="3953"/>
        <source>%s - Open Directory</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="421"/>
        <source>Toggle &quot;Keep Previous Annotation&quot; mode</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="431"/>
        <source>Auto Use Last Label</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="431"/>
        <source>Toggle &quot;Auto Use Last Label&quot; mode</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="298"/>
        <source>&amp;Open Video</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="298"/>
        <source>Open video file</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="360"/>
        <source>&amp;Auto Run</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="360"/>
        <source>Auto run all images at once</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="291"/>
        <source>&amp;Open File</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="476"/>
        <source>Create Rotation</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="476"/>
        <source>Start drawing rotations</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="921"/>
        <source>&amp;Show Degress</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="921"/>
        <source>Show degrees above rotated shapes</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="831"/>
        <source>&amp;Keep Previous Brightness</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="831"/>
        <source>Keep previous brightness</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="839"/>
        <source>&amp;Keep Previous Contrast</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="839"/>
        <source>Keep previous contrast</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1743"/>
        <source>Attributes</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1887"/>
        <source>Previous</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1888"/>
        <source>Next</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1889"/>
        <source>Rectangle</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1890"/>
        <source>Polygon</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1891"/>
        <source>Rotation</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="450"/>
        <source>Visibility Shapes</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="450"/>
        <source>Toggle &quot;Visibility Shapes&quot; mode</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="658"/>
        <source>Hide Selected Polygons</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="658"/>
        <source>Hide selected polygons</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="976"/>
        <source>&amp;Upload Attributes File</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="976"/>
        <source>Upload Custom Attributes File</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1478"/>
        <source>&amp;Upload</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="2049"/>
        <source>Invalid label &apos;{}&apos; with validation type: {}!
Reset the label as {}.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="3105"/>
        <source>X: %d, Y: %d | H: %d, W: %d</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="3123"/>
        <source>X: %d, Y: %d</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="4290"/>
        <source>Object Description</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="4299"/>
        <source>Image Description</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="4310"/>
        <source>Switch to Edit mode for description editing</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="666"/>
        <source>Show Hidden Polygons</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="666"/>
        <source>Show hidden polygons</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="675"/>
        <source>&amp;Overview</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1052"/>
        <source>&amp;Upload DOTA Annotations</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1052"/>
        <source>Upload Custom DOTA Annotations</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1059"/>
        <source>&amp;Upload MASK Annotations</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1059"/>
        <source>Upload Custom MASK Annotations</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1066"/>
        <source>&amp;Upload MOT Annotations</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1066"/>
        <source>Upload Custom Multi-Object-Tracking Annotations</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1176"/>
        <source>&amp;Export DOTA Annotations</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1176"/>
        <source>Export Custom DOTA Annotations</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1183"/>
        <source>&amp;Export MASK Annotations</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1190"/>
        <source>&amp;Export MOT Annotations</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1190"/>
        <source>Export Custom Multi-Object-Tracking Annotations</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1478"/>
        <source>&amp;Export</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="3092"/>
        <source>X: %d, Y: %d | H: %d, W: %d [%s: %d/%d]</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="3112"/>
        <source>X: %d, Y: %d [%s: %d/%d]</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="377"/>
        <source>&amp;Delete Image File</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="377"/>
        <source>Delete current image file</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1478"/>
        <source>&amp;Tool</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="3812"/>
        <source>You are about to permanently delete this image file, proceed anyway?</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="720"/>
        <source>&amp;Convert HBB to OBB</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="720"/>
        <source>Perform conversion from horizontal bounding box to oriented bounding box</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="675"/>
        <source>Show annotations statistics</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="682"/>
        <source>&amp;Save Cropped Image</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="983"/>
        <source>&amp;Upload YOLO-Hbb Annotations</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="983"/>
        <source>Upload Custom YOLO Horizontal Bounding Boxes Annotations</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="992"/>
        <source>&amp;Upload YOLO-Obb Annotations</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="992"/>
        <source>Upload Custom YOLO Oriented Bounding Boxes Annotations</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1001"/>
        <source>&amp;Upload YOLO-Seg Annotations</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1001"/>
        <source>Upload Custom YOLO Segmentation Annotations</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="900"/>
        <source>&amp;Show Labels</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="900"/>
        <source>Show label inside shapes</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="728"/>
        <source>&amp;Convert OBB to HBB</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="728"/>
        <source>Perform conversion from oriented bounding box to horizontal bounding box</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="441"/>
        <source>Use System Clipboard</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="441"/>
        <source>Use system clipboard for copy and paste</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="682"/>
        <source>Save cropped image. (Support rectangle/rotation/polygon shape_type)</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="699"/>
        <source>&amp;Label Manager</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="712"/>
        <source>&amp;Union Selection</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="712"/>
        <source>Union multiple selected rectangle shapes</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="736"/>
        <source>&amp;Convert Polygon to HBB</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="736"/>
        <source>Perform conversion from polygon to horizontal bounding box</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="865"/>
        <source>&amp;Set Brightness Contrast</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="873"/>
        <source>&amp;Set Cross Line</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="873"/>
        <source>Adjust cross line for mouse position</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="911"/>
        <source>&amp;Show Scores</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="911"/>
        <source>Show score inside shapes</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="931"/>
        <source>&amp;Show KIE Linking</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="931"/>
        <source>Show KIE linking between key and value</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="962"/>
        <source>&amp;Upload Image Flags File</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="962"/>
        <source>Upload Custom Image Flags File</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="969"/>
        <source>&amp;Upload Label Flags File</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="969"/>
        <source>Upload Custom Label Flags File</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1008"/>
        <source>&amp;Upload YOLO-Pose Annotations</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1008"/>
        <source>Upload Custom YOLO Pose Annotations</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1015"/>
        <source>&amp;Upload VOC Detection Annotations</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1015"/>
        <source>Upload Custom Pascal VOC Detection Annotations</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1022"/>
        <source>&amp;Upload VOC Segmentation Annotations</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1022"/>
        <source>Upload Custom Pascal VOC Segmentation Annotations</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1029"/>
        <source>&amp;Upload COCO Detection Annotations</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1029"/>
        <source>Upload Custom COCO Detection Annotations</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1073"/>
        <source>&amp;Upload ODVG Annotations</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1073"/>
        <source>Upload Custom Object Detection Visual Grounding Annotations</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1082"/>
        <source>&amp;Upload PPOCR-Rec Annotations</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1082"/>
        <source>Upload Custom PPOCR Recognition Annotations</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1089"/>
        <source>&amp;Upload PPOCR-KIE Annotations</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1089"/>
        <source>Upload Custom PPOCR Key Information Extraction (KIE - Semantic Entity Recognition &amp; Relation Extraction) Annotations</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1107"/>
        <source>&amp;Export YOLO-Hbb Annotations</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1107"/>
        <source>Export Custom YOLO Horizontal Bounding Boxes Annotations</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1116"/>
        <source>&amp;Export YOLO-Obb Annotations</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1116"/>
        <source>Export Custom YOLO Oriented Bounding Boxes Annotations</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1125"/>
        <source>&amp;Export YOLO-Seg Annotations</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1125"/>
        <source>Export Custom YOLO Segmentation Annotations</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1132"/>
        <source>&amp;Export YOLO-Pose Annotations</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1132"/>
        <source>Export Custom YOLO Pose Annotations</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1139"/>
        <source>&amp;Export VOC Detection Annotations</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1139"/>
        <source>Export Custom PASCAL VOC Detection Annotations</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1146"/>
        <source>&amp;Export VOC Segmentation Annotations</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1146"/>
        <source>Export Custom PASCAL VOC Segmentation Annotations</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1153"/>
        <source>&amp;Export COCO Detection Annotations</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1153"/>
        <source>Export Custom COCO Rectangle Annotations</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1183"/>
        <source>Export Custom MASK Annotations - RGB/Gray</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1206"/>
        <source>&amp;Export ODVG Annotations</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1206"/>
        <source>Export Custom Object Detection Visual Grounding Annotations</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1215"/>
        <source>&amp;Export PPOCR-Rec Annotations</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1215"/>
        <source>Export Custom PPOCR Recognition Annotations</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1222"/>
        <source>&amp;Export PPOCR-KIE Annotations</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1222"/>
        <source>Export Custom PPOCR Key Information Extraction (KIE - Semantic Entity Recognition &amp; Relation Extraction) Annotations</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="2932"/>
        <source>Error pasting shapes</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="2932"/>
        <source>Error decoding shapes: %s</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="2285"/>
        <source>Copied</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="2285"/>
        <source>The information has been copied to the clipboard.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="699"/>
        <source>Manage Labels: Rename, Delete, Adjust Color</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="705"/>
        <source>&amp;Group ID Manager</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="705"/>
        <source>Manage Group ID</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1197"/>
        <source>&amp;Export MOTS Annotations</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1197"/>
        <source>Export Custom Multi-Object-Tracking-Segmentation Annotations</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="312"/>
        <source>Open next image</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="320"/>
        <source>Open prev image</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="328"/>
        <source>&amp;Next Unchecked Image</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="328"/>
        <source>Open next unchecked image</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="336"/>
        <source>&amp;Prev Unchecked Image</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="336"/>
        <source>Open previous unchecked image</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="773"/>
        <source>&amp;Loop through labels</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="773"/>
        <source>Loop through labels</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1045"/>
        <source>&amp;Upload COCO Keypoint Annotations</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1045"/>
        <source>Upload Custom COCO Keypoint Annotations</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1169"/>
        <source>&amp;Export COCO Keypoint Annotations</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1169"/>
        <source>Export Custom COCO Keypoint Annotations</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="752"/>
        <source>ChatBot</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="752"/>
        <source>Open chatbot dialog</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1886"/>
        <source>Chatbot</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="744"/>
        <source>&amp;Convert Polygon to OBB</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="744"/>
        <source>Perform conversion from polygon to oriented bounding box</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="766"/>
        <source>&amp;About</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="766"/>
        <source>Open about dialog</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1098"/>
        <source>&amp;Upload VLM-R1 OVD Annotations</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1098"/>
        <source>Upload Custom VLM-R1 OVD Annotations</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1231"/>
        <source>&amp;Export VLM-R1 OVD Annotations</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1231"/>
        <source>Export Custom VLM-R1 OVD Annotations</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1036"/>
        <source>&amp;Upload COCO Instance Segmentation Annotations</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1036"/>
        <source>Upload Custom COCO Instance Segmentation Annotations</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1160"/>
        <source>&amp;Export COCO Instance Segmentation Annotations</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1160"/>
        <source>Export Custom COCO Instance Segmentation Annotations</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="690"/>
        <source>&amp;Digit Shortcut Manager</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="690"/>
        <source>Manage Digit Shortcuts: Assign Drawing Modes and Labels to Number Keys</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="516"/>
        <source>Digit Shortcut 0</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="522"/>
        <source>Digit Shortcut 1</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="528"/>
        <source>Digit Shortcut 2</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="534"/>
        <source>Digit Shortcut 3</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="540"/>
        <source>Digit Shortcut 4</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="546"/>
        <source>Digit Shortcut 5</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="552"/>
        <source>Digit Shortcut 6</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="558"/>
        <source>Digit Shortcut 7</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="564"/>
        <source>Digit Shortcut 8</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="570"/>
        <source>Digit Shortcut 9</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>Model</name>
    <message>
        <location filename="../../services/auto_labeling/__base__/yolo.py" line="52"/>
        <source>Rectangle</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../services/auto_labeling/__base__/yolo.py" line="50"/>
        <source>Point</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../services/auto_labeling/model.py" line="155"/>
        <source>Downloading model from registry...</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../services/auto_labeling/model.py" line="237"/>
        <source>Downloading {download_url}: {percent}%</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../services/auto_labeling/__base__/yolo.py" line="51"/>
        <source>Polygon</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../services/auto_labeling/model.py" line="150"/>
        <source>Model path not found: {model_path}</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../services/auto_labeling/yolov8_sahi.py" line="46"/>
        <source>Could not download or initialize YOLOv8 model.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../services/auto_labeling/model.py" line="54"/>
        <source>Config file not found: {model_config}</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../services/auto_labeling/model.py" line="64"/>
        <source>Unknown config type: {type}</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../services/auto_labeling/clrnet.py" line="50"/>
        <source>Could not download or initialize CLRNet model.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../services/auto_labeling/yolox_dwpose.py" line="54"/>
        <source>Could not download or initialize YOLOX-L model.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../services/auto_labeling/yolox_dwpose.py" line="63"/>
        <source>Could not download or initialize DWPose model.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../services/auto_labeling/clrnet.py" line="40"/>
        <source>Line</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../services/auto_labeling/grounding_sam2.py" line="527"/>
        <source>Invalid model_type in GroundingDINO model.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../services/auto_labeling/grounding_sam2.py" line="60"/>
        <source>Rotation</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../services/auto_labeling/yolov5_sam.py" line="59"/>
        <source>Could not download or initialize YOLOv5 model.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../services/auto_labeling/rtmdet_pose.py" line="41"/>
        <source>Could not download or initialize RTMDet model.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../services/auto_labeling/rtmdet_pose.py" line="50"/>
        <source>Could not download or initialize Pose model.</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>ModelManager</name>
    <message>
        <location filename="../../services/auto_labeling/model_manager.py" line="135"/>
        <source>Model loaded. Ready for labeling.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../services/auto_labeling/model_manager.py" line="270"/>
        <source>No model selected.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../services/auto_labeling/model_manager.py" line="1991"/>
        <source>Model is not loaded. Choose a mode to continue.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../services/auto_labeling/model_manager.py" line="1970"/>
        <source>Finished inferencing AI model. Check the result.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../services/auto_labeling/model_manager.py" line="1995"/>
        <source>Inferencing AI model. Please wait...</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../services/auto_labeling/model_manager.py" line="2005"/>
        <source>Another model is being executed. Please wait for it to finish.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../services/auto_labeling/model_manager.py" line="164"/>
        <source>Error in loading custom model: Invalid path.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../services/auto_labeling/model_manager.py" line="180"/>
        <source>Error in loading custom model: Invalid config file.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../services/auto_labeling/model_manager.py" line="211"/>
        <source>Error in loading custom model: Invalid config file format.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../services/auto_labeling/model_manager.py" line="284"/>
        <source>Error in loading model: Invalid model name.</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>OverviewDialog</name>
    <message>
        <location filename="../../views/labeling/widgets/overview_dialog.py" line="109"/>
        <source>Overview</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/overview_dialog.py" line="160"/>
        <source>Export</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/overview_dialog.py" line="491"/>
        <source>Show Shape Infos</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/overview_dialog.py" line="211"/>
        <source>Loading...</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/overview_dialog.py" line="211"/>
        <source>Cancel</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/overview_dialog.py" line="219"/>
        <source>Progress</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/overview_dialog.py" line="405"/>
        <source>Select Directory</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/overview_dialog.py" line="476"/>
        <source>Error occurred while exporting annotations statistics file.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/overview_dialog.py" line="493"/>
        <source>Show Label Infos</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/overview_dialog.py" line="459"/>
        <source>Exporting annotations successfully!
Results have been saved to:
%s</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>TextInputDialog</name>
    <message>
        <location filename="../../views/labeling/utils/batch.py" line="311"/>
        <source>Cancel</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/batch.py" line="57"/>
        <source>Enter Text Prompt</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/batch.py" line="65"/>
        <source>Please enter your text prompt:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/batch.py" line="72"/>
        <source>Enter prompt here...</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/batch.py" line="168"/>
        <source>Processing completed successfully!</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/batch.py" line="300"/>
        <source>Error occurred while processing images!</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/batch.py" line="311"/>
        <source>Processing...</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/batch.py" line="319"/>
        <source>Batch Processing</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/batch.py" line="402"/>
        <source>Model is not loaded. Choose a mode to continue.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/batch.py" line="416"/>
        <source>Invalid model type, please choose a valid model_type to run.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/batch.py" line="425"/>
        <source>Confirmation</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/batch.py" line="426"/>
        <source>Do you want to process all images?</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>UploadCocoThread</name>
    <message>
        <location filename="../../views/labeling/utils/upload.py" line="234"/>
        <source>Select a custom annotation file (Label.txt)</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/upload.py" line="246"/>
        <source>Select a custom annotation file (ppocr_kie.json)</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/upload.py" line="1186"/>
        <source>Warning</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/upload.py" line="1187"/>
        <source>Current annotation will be lost</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/upload.py" line="1188"/>
        <source>You are going to upload new annotations to this task. Continue?</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/upload.py" line="1201"/>
        <source>Uploading...</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/upload.py" line="1201"/>
        <source>Cancel</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/upload.py" line="1209"/>
        <source>Progress</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/upload.py" line="901"/>
        <source>Uploading annotations successfully!</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/upload.py" line="1274"/>
        <source>Error occurred while uploading annotations!</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/upload.py" line="328"/>
        <source>Select a specific OD file</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/upload.py" line="1102"/>
        <source>Select a specific classes file</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/upload.py" line="418"/>
        <source>Select a specific gt file</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/upload.py" line="426"/>
        <source>Please select a specific gt file!</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/upload.py" line="518"/>
        <source>Select a specific color_map file</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/upload.py" line="1116"/>
        <source>Upload Options</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/upload.py" line="1135"/>
        <source>Select Upload Folder</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/upload.py" line="1146"/>
        <source>Browse</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/upload.py" line="1163"/>
        <source>OK</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/upload.py" line="850"/>
        <source>Select a custom coco annotation file</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/upload.py" line="1083"/>
        <source>Select a specific yolo-pose config file</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/upload.py" line="1254"/>
        <source>Upload completed successfully!</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/upload.py" line="1284"/>
        <source>Select a specific shape attributes file</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/upload.py" line="1311"/>
        <source>Uploading shape attributes file successfully!</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/upload.py" line="1322"/>
        <source>Error occurred while uploading shape attributes file!</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/upload.py" line="1376"/>
        <source>Select a specific flags file</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/upload.py" line="1395"/>
        <source>Uploading flags file successfully!</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/upload.py" line="1404"/>
        <source>Error occurred while uploading flags file!</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/upload.py" line="1051"/>
        <source>Uploading annotations successfully!
Results have been saved to:
%s</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/upload.py" line="133"/>
        <source>Select a custom vlm_r1_ovd annotation file</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>ZoomWidget</name>
    <message>
        <location filename="../../views/labeling/widgets/zoom_widget.py" line="11"/>
        <source>Zoom Level</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>auto_labeling_form</name>
    <message>
        <location filename="../../views/labeling/widgets/auto_labeling/auto_labeling_ui.py" line="169"/>
        <source>Form</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/auto_labeling/auto_labeling_ui.py" line="170"/>
        <source>No Model</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/auto_labeling/auto_labeling_ui.py" line="171"/>
        <source>Output</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/auto_labeling/auto_labeling_ui.py" line="172"/>
        <source>polygon</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/auto_labeling/auto_labeling_ui.py" line="173"/>
        <source>rectangle</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/auto_labeling/auto_labeling_ui.py" line="195"/>
        <source>Run (i)</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/auto_labeling/auto_labeling_ui.py" line="201"/>
        <source>Point (q)</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/auto_labeling/auto_labeling_ui.py" line="202"/>
        <source>Point (e)</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/auto_labeling/auto_labeling_ui.py" line="203"/>
        <source>+Rect</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/auto_labeling/auto_labeling_ui.py" line="209"/>
        <source>Ready!</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/auto_labeling/auto_labeling_ui.py" line="174"/>
        <source>rotation</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/auto_labeling/auto_labeling_ui.py" line="204"/>
        <source>Clear (b)</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/auto_labeling/auto_labeling_ui.py" line="196"/>
        <source>Enter text prompt here, e.g., person.car.bicycle</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/auto_labeling/auto_labeling_ui.py" line="197"/>
        <source>Send</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/auto_labeling/auto_labeling_ui.py" line="198"/>
        <source>Box threshold</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/auto_labeling/auto_labeling_ui.py" line="199"/>
        <source>Confidence</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/auto_labeling/auto_labeling_ui.py" line="200"/>
        <source>IoU</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/auto_labeling/auto_labeling_ui.py" line="208"/>
        <source>Reset Tracker</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/auto_labeling/auto_labeling_ui.py" line="175"/>
        <source>coarse_grained_prompt</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/auto_labeling/auto_labeling_ui.py" line="176"/>
        <source>fine_grained_prompt</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/auto_labeling/auto_labeling_ui.py" line="177"/>
        <source>caption</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/auto_labeling/auto_labeling_ui.py" line="178"/>
        <source>detailed_cap</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/auto_labeling/auto_labeling_ui.py" line="179"/>
        <source>more_detailed_cap</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/auto_labeling/auto_labeling_ui.py" line="180"/>
        <source>od</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/auto_labeling/auto_labeling_ui.py" line="181"/>
        <source>region_proposal</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/auto_labeling/auto_labeling_ui.py" line="182"/>
        <source>dense_region_cap</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/auto_labeling/auto_labeling_ui.py" line="183"/>
        <source>cap_to_pg</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/auto_labeling/auto_labeling_ui.py" line="184"/>
        <source>refer_exp_seg</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/auto_labeling/auto_labeling_ui.py" line="185"/>
        <source>region_to_seg</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/auto_labeling/auto_labeling_ui.py" line="186"/>
        <source>ovd</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/auto_labeling/auto_labeling_ui.py" line="187"/>
        <source>region_to_cat</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/auto_labeling/auto_labeling_ui.py" line="188"/>
        <source>region_to_desc</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/auto_labeling/auto_labeling_ui.py" line="189"/>
        <source>ocr</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/auto_labeling/auto_labeling_ui.py" line="190"/>
        <source>ocr_with_region</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/auto_labeling/auto_labeling_ui.py" line="205"/>
        <source>Finish (f)</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/auto_labeling/auto_labeling_ui.py" line="191"/>
        <source>GroundingDino_1_6_Pro</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/auto_labeling/auto_labeling_ui.py" line="192"/>
        <source>GroundingDino_1_6_Edge</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/auto_labeling/auto_labeling_ui.py" line="193"/>
        <source>GroundingDino_1_5_Pro</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/auto_labeling/auto_labeling_ui.py" line="194"/>
        <source>GroundingDino_1_5_Edge</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/auto_labeling/auto_labeling_ui.py" line="206"/>
        <source>Replace (On)</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/auto_labeling/auto_labeling_ui.py" line="207"/>
        <source>Set API Token</source>
        <translation type="unfinished"></translation>
    </message>
</context>
</TS>
