import traceback
from anylabeling.customize_ui.layout.layout import Ui_Form
import global_tools.utils as global_tools_utils
from PyQt5.QtWidgets import QLineEdit, QLabel

from anylabeling.customize_ui.helper.const import CONFIG_PATH, X_LABEL_LOG
from .helper import check_lineedit, clear_layout_widgets, get_training_parameters, set_labels_copy_on_click,\
     QuerySyntaxHighlighter, TrainModelProcess, BestPtFinder
from anylabeling.customize_ui.helper.helper import evaluate_condition_string, LineEditMutexManager, TextEditManager
import threading
import os
from global_tools.ui_tools import LineEditManager, QLabelManager, get_widgets_from_layout,QPushButtonManager,TextEditMemory, InputCompleterCache,LogOutput



class TrainModel:

    train_model_process: TrainModelProcess

    def __init__( self, ui_form: Ui_Form, log_output: LogOutput ):
        self.ui_form = ui_form
        self.log_output = log_output

        self.logger: global_tools_utils.Logger = global_tools_utils.ClassInstanceManager.get_instance(
            X_LABEL_LOG
        )    # type: ignore

        # ===========================================================================
        # ------------------------- QLineEdit -------------------------
        # ===========================================================================
        self.line_edit_manager = LineEditManager.get_instance()
        self.line_edit_mutex_manager = LineEditMutexManager( layouts = [ self.ui_form.horizontalLayout_183 ] )
        

        # ===========================================================================
        # ------------------------- QPushButton -------------------------
        # ===========================================================================
        # self.push_button_list = [
        #     self.ui_form.pushButton_24,
        #     self.ui_form.pushButton_33,
        #     self.ui_form.pushButton_32,
        # ]
        self.push_button_manager = QPushButtonManager.get_instance()

        # 启动训练
        self.push_button_manager.connect_clicked_signal(
            button_name = "pushButton_24", slot = self.start_training_model_button_function
        )
        # 停止训练
        self.push_button_manager.connect_clicked_signal(
            button_name = "pushButton_33", slot = self.stop_training_model_button_function
        )
        # 搜索最新模型
        self.push_button_manager.connect_clicked_signal(
            button_name = "pushButton_32", slot = self.search_latest_model
        )

        # ===========================================================================
        # ------------------------- QLabel -------------------------
        # ===========================================================================
        # self.label_list = [
        #     *global_tools_ui_tools
        #     .get_widgets_from_layout( layout = self.ui_form.verticalLayout_31, widget_type = QLabel ),
        # ]
        # self.label_manager = global_tools_ui_tools.QLabelManager( *self.label_list )
        self.label_manager = QLabelManager.get_instance()


        # 点击 停止训练条件 QLabel 复制文本到 停止训练条件 QLineEdit
        copy_map = {
            "label_535": "metrics/mAP50(B)",
            "label_537": "metrics/mAP75(B)",
            "label_536": "metrics/mAP50-95(B)",
            "label_540": "metrics/mAP50(M)",
            "label_538": "metrics/mAP75(M)",
            "label_539": "metrics/mAP50-95(M)",
            "label_541": "cur_ecpohs",
        }
        set_labels_copy_on_click(
            layouts = get_widgets_from_layout(
                layout = self.ui_form.verticalLayout_7, widget_type = QLabel
            ),
            copy_map = copy_map,
            log_output = self.log_output,
            logger = self.logger
        )
        # ===========================================================================
        # ------------------------- QTextEdit -------------------------
        # ===========================================================================
        self.text_edit_list = [
            self.ui_form.textEdit_8,
        ]
        self.text_edit_memory = TextEditMemory(
            text_edits = self.text_edit_list,
            log_output = self.log_output,
            logger = self.logger,
            config_filename = "textEdit_memory.json",
            config_path = CONFIG_PATH,
        )
        self.text_edit_manager = TextEditManager( text_edits = self.text_edit_list )

    def __call__( self ):
        pass
        # 语法高亮
        QuerySyntaxHighlighter( text_edit = self.ui_form.textEdit_8 )

    def clear_all_status_stop_training_status( self ):
        # 清空停止训练条件全部状态

        # 禁用按钮
        self.push_button_manager.set_enabled( button_name = "pushButton_24", enabled = False )
        self.log_output.append( message = "清空全部状态停止训练状态.......", color = global_tools_utils.Colors.INFO )

        def thread_func():
            clear_layout_widgets( self.ui_form.verticalLayout_119 )
            # 启用按钮
            self.push_button_manager.set_enabled( button_name = "pushButton_24", enabled = True )
            self.log_output.append( message = "清空全部状态停止训练状态完成", color = global_tools_utils.Colors.SUCCESS )

        thread = threading.Thread( target = thread_func )
        thread.start()

    def start_training_model_button_function( self ):

        try:
            self.push_button_manager.set_enabled( button_name = "pushButton_24", enabled = False )
            self.log_output.append( message = "等待启动训练模型进程.......", color = global_tools_utils.Colors.PRIMARY )
            train_parameters = get_training_parameters( line_edit_manager = self.line_edit_manager )
            condition_string = self.text_edit_manager.get_text( object_name = "textEdit_8" )

            def thread_func():
                self.train_model_process = TrainModelProcess(
                    log_output = self.log_output,
                    logger = self.logger,
                    label_manager = self.label_manager,
                )
                self.train_model_process()
                self.train_model_process.start(
                    train_parameters = train_parameters, condition_string = condition_string
                )
                self.train_model_process.wait_for_completion()
                self.push_button_manager.set_enabled( button_name = "pushButton_24", enabled = True )
                best_model_path = self.train_model_process.set_save_best_model_path
                if best_model_path:
                    self.line_edit_manager.set_text( name = 'lineEdit_47', text = best_model_path )

            thread = threading.Thread( target = thread_func )
            thread.start()
        except Exception as e:
            traceback.print_exc()
            self.log_output.append( message = str( traceback.format_exc() ), color = global_tools_utils.Colors.ERROR )
            # raise RuntimeError(e)

    def stop_training_model_button_function( self ):
        if self.train_model_process:
            self.train_model_process.stop()
            self.push_button_manager.set_enabled( button_name = "pushButton_24", enabled = True )
        else:
            self.log_output.append( message = "训练模型进程未启动", color = global_tools_utils.Colors.ERROR )

    def search_latest_model( self ):
        # 搜索最新模型
        def thread_func():
            best_pt_finder = BestPtFinder(
                line_edit_manager = self.line_edit_manager,
                log_output = self.log_output,
                logger = self.logger,
            )
            best_pt_path = best_pt_finder.find_best_pt_path()
            self.line_edit_manager.set_text( name = 'lineEdit_47', text = best_pt_path )

        thread = threading.Thread( target = thread_func )
        thread.start()
