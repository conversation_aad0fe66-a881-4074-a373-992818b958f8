language: en_US
model_hub: github  # github, modelscope
auto_save: true
display_label_popup: true
store_data: false
keep_prev: false
keep_prev_scale: false
keep_prev_brightness: false
keep_prev_contrast: false
auto_use_last_label: false
auto_highlight_shape: true
auto_switch_to_edit_mode: true
show_groups: false
show_texts: true
show_labels: true
show_scores: true
show_degrees: false
show_shapes: true
show_linking: true
logger_level: info
system_clipboard: false
switch_to_checked: false

flags: null
label_flags: null
labels: null
file_search: null
sort_labels: true
validate_label: null

default_shape_color: [0, 255, 0]
shape_color: auto  # null, 'auto', 'manual'
shift_auto_shape_color: 0
label_colors: null

shape:
  # drawing
  line_color: [0, 255, 0, 128]
  fill_color: [220, 220, 220, 150]
  vertex_fill_color: [0, 255, 0, 255]
  # selecting / hovering
  select_line_color: [255, 255, 255, 255]
  select_fill_color: [0, 255, 0, 155]
  hvertex_fill_color: [255, 255, 255, 255]
  point_size: 10
  line_width: 4

# main
flag_dock:
  show: true
  closable: false
  movable: false
  floatable: false
label_dock:
  show: true
  closable: false
  movable: false
  floatable: false
shape_dock:
  show: true
  closable: false
  movable: false
  floatable: false
file_dock:
  show: true
  closable: false
  movable: false
  floatable: false

# label_dialog
show_label_text_field: true
label_completion: startswith
move_mode: auto  # 'auto', 'center'
fit_to_content:
  column: true
  row: false

# canvas
epsilon: 10.0
canvas:
  # None: do nothing
  # close: close polygon
  double_click: close
  # change mode
  # The max number of edits we can undo
  num_backups: 10
  crosshair:
    show: true
    width: 2.0
    color: "#00FF00"
    opacity: 0.5

shortcuts:
  close: Ctrl+W
  open: Ctrl+I
  open_video: Ctrl+O
  open_dir: Ctrl+U
  open_chatbot: Ctrl+B
  quit: Ctrl+Q
  save: Ctrl+S
  save_as: Ctrl+Shift+S
  save_to: null
  delete_file: Ctrl+Delete
  delete_image_file: Ctrl+Shift+Delete

  open_next: D
  open_prev: A
  open_next_unchecked: Ctrl+Shift+D
  open_prev_unchecked: Ctrl+Shift+A

  zoom_in: [Ctrl++, Ctrl+=]
  zoom_out: Ctrl+-
  zoom_to_original: Ctrl+0
  fit_window: Ctrl+F
  fit_width: Ctrl+Shift+F

  create_polygon: [P, Ctrl+N]
  create_rectangle: [R, Ctrl+R]
  create_rotation: O
  create_circle: null
  create_line: null
  create_point: null
  create_linestrip: null
  edit_polygon: Ctrl+J
  delete_polygon: Delete
  duplicate_polygon: Ctrl+D
  copy_polygon: Ctrl+C
  paste_polygon: Ctrl+V
  undo: Ctrl+Z
  undo_last_point: Ctrl+Z
  add_point_to_edge: Ctrl+Shift+P
  edit_label: Ctrl+E
  edit_group_id: Alt+G
  edit_digit_shortcut: Alt+D
  toggle_keep_prev_mode: Ctrl+P
  remove_selected_point: Backspace
  group_selected_shapes: G
  ungroup_selected_shapes: U
  hide_selected_polygons: S
  show_hidden_polygons: W
  show_overview: Ctrl+G
  show_texts: Ctrl+T
  show_labels: Ctrl+L
  show_linking: Ctrl+K
  union_selected_shapes: Ctrl+Shift+M
  toggle_auto_use_last_label: Ctrl+Y
  toggle_visibility_shapes: Ctrl+H

  auto_label: Ctrl+A
  auto_run: Ctrl+M
  loop_thru_labels: Ctrl+Shift+N

# Auto labeling
custom_models: []

# Digit shortcuts
digit_shortcuts: null