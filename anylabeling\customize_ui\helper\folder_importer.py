from __future__ import annotations
import os
import sys
import time
import traceback
from typing import TYPE_CHECKING, Optional, Callable, List, Union, Any

from PyQt5 import QtWidgets, QtCore
from PyQt5.QtCore import QObject, QThread, QCoreApplication, QTimer, QEventLoop, pyqtSignal

# 为了避免循环导入，同时提供类型提示
if TYPE_CHECKING:
    from anylabeling.views.labeling.label_widget import LabelingWidget

# 尝试注册元类型
try:
    # 在 PyQt5 中，注册类型可以通过不同的函数实现，尝试可能的函数
    from PyQt5.QtCore import pyqtRemoveInputHook
    if hasattr( QtCore, 'qRegisterMetaType' ):
        register_func = getattr( QtCore, 'qRegisterMetaType' )
    else:
        # 尝试从 PyQt5.sip 导入
        try:
            from PyQt5 import sip
            register_func = lambda x: None    # 兼容性空函数
            # print( "警告: 无法找到合适的元类型注册函数，使用空函数" )
        except ImportError:
            register_func = lambda x: None
            print( "警告: 无法找到元类型注册功能，可能导致跨线程信号问题" )

    # 注册常见的Qt类型，用于跨线程信号-槽连接
    for type_name in [ 'QTextBlock', 'QList<QPersistentModelIndex>', 'QVector<int>' ]:
        try:
            register_func( type_name )
        except Exception as e:
            print( f"警告: 无法注册元类型 {type_name}: {e}" )
except Exception as e:
    print( f"注册元类型时出错: {e}" )



class FolderImporter:
    """
    提供用于导入文件夹中图像的静态方法的工具类。
    """
    
    # 类变量，用于跟踪是否已经显示了第一张图像，防止重复加载
    _first_image_shown = False

    # 创建一个辅助类用于跨线程通信
    class _MainThreadExecutor( QObject ):
        """用于在主线程中执行函数的辅助类"""
        # 定义一个信号用于在主线程中执行函数
        executionSignal = pyqtSignal( object )

        def __init__( self ):
            super().__init__()
            # 将自己移动到主线程
            self.moveToThread( QCoreApplication.instance().thread() )
            # 连接信号到槽
            self.executionSignal.connect( self._executeInMainThread )

        def _executeInMainThread( self, func_wrapper ):
            """在主线程中执行传入的包装函数"""
            func_wrapper()

        def execute( self, func, args, kwargs ):
            """
            在主线程中执行函数并返回结果
            
            使用信号-槽机制在主线程中执行函数，并使用事件循环等待结果，
            避免使用sleep造成的性能问题
            """
            # 创建一个结果容器和事件循环
            result_holder = {
                'result': None,
                'done': False,
                'error': None
            }

            # 创建一个事件循环用于等待结果
            loop = QEventLoop()

            # 创建一个包装函数，用于存储结果并退出事件循环
            def wrapper_func():
                try:
                    result_holder[ 'result' ] = func( *args, **kwargs )
                except Exception as e:
                    result_holder[ 'error' ] = e
                    print( f"在主线程执行函数时出错: {e}" )
                    traceback.print_exc()
                finally:
                    result_holder[ 'done' ] = True
                    # 使用QTimer确保在事件循环中安全退出
                    QTimer.singleShot( 0, loop.quit )

            # 使用信号在主线程中执行包装函数
            self.executionSignal.emit( wrapper_func )

            # 启动超时计时器 (20秒)
            timer = QTimer()
            timer.setSingleShot( True )
            timer.timeout.connect( loop.quit )
            timer.start( 20000 )    # 20秒超时

            # 等待直到函数执行完成或超时
            loop.exec_()

            # 检查是否超时
            if not result_holder[ 'done' ] and not timer.isActive():
                print( "警告: 等待主线程执行操作超时" )
                return None

            # 停止计时器
            timer.stop()

            if result_holder[ 'error' ]:
                print( f"主线程执行操作出错: {result_holder['error']}" )
                return None

            return result_holder[ 'result' ]

    # 单例实例
    _executor_instance = None

    @staticmethod
    def __get_main_widget() -> Optional[ "LabelingWidget" ]:
        """
        一个私有辅助方法，用于在当前运行的 Qt 应用中查找核心的 LabelingWidget 实例。

        它通过遍历所有顶级窗口来定位主窗口，然后深入其内部结构找到 LabelingWidget。
        这种方法与应用的UI结构耦合度较高，如果主窗口结构改变，此方法可能需要更新。
        
        返回:
            LabelingWidget | None: 如果找到，则返回 LabelingWidget 实例，否则返回 None。
        """
        # 动态导入以避免在模块加载时出现循环依赖问题
        from anylabeling.views.mainwindow import MainWindow
        from anylabeling.views.labeling.label_wrapper import LabelingWrapper

        app = QtWidgets.QApplication.instance()
        if not app:
            print( "错误：QApplication 实例未运行。" )
            return None

        # 1. 查找主窗口 (MainWindow)
        main_window = next( ( w for w in app.topLevelWidgets() if isinstance( w, MainWindow ) ), None )    # type: ignore

        if not main_window:
            print( "错误：无法找到主窗口 MainWindow。" )
            return None

        # 2. 从主窗口找到 LabelingWrapper
        wrapper = main_window.labeling_widget
        if not isinstance( wrapper, LabelingWrapper ):
            print( "错误：MainWindow.labeling_widget 不是预期的 LabelingWrapper 实例。" )
            return None

        # 3. 从 LabelingWrapper 找到最终的 LabelingWidget
        # LabelingWrapper 内部使用 `self.view` 来存储 LabelingWidget 实例。
        return wrapper.view

    @staticmethod
    def __run_in_main_thread( func: Callable, *args, **kwargs ) -> Any:
        """
        确保函数在主线程(UI线程)中执行。
        
        参数:
            func: 要执行的函数
            *args: 传递给函数的位置参数
            **kwargs: 函数的关键字参数
            
        返回:
            函数的返回值，如果执行出错则返回None
        """
        # 检查当前是否在主线程中
        if QThread.currentThread() == QCoreApplication.instance().thread():
            # 已经在主线程中，直接执行
            try:
                return func( *args, **kwargs )
            except Exception as e:
                print( f"在主线程执行函数时出错: {e}" )
                traceback.print_exc()
                return None
        else:
            # 在非主线程中，使用执行器在主线程中执行
            print( "确保在主线程中执行UI操作..." )

            # 获取或创建执行器实例
            if FolderImporter._executor_instance is None:
                FolderImporter._executor_instance = FolderImporter._MainThreadExecutor()

            # 使用执行器执行函数
            return FolderImporter._executor_instance.execute( func, args, kwargs )

    @staticmethod
    def load_from_path( path_or_callback: Union[ str, Callable[ [], List[ str ] ] ] ) -> bool:
        """
        从指定路径加载图像或使用回调函数获取图像路径列表。

        此方法接受单个参数，可以是目录路径字符串，也可以是返回图像路径列表的回调函数。

        参数:
            path_or_callback (Union[str, Callable[[], List[str]]]): 
                - 如果是字符串，则被视为目录路径，将加载该目录下的所有图像
                - 如果是回调函数，则调用该函数获取图像路径列表进行加载
        
        返回:
            bool: 如果成功触发加载，则返回 True；否则返回 False。

        使用示例:
        ```python
        # 加载整个文件夹
        success = FolderImporter.load_from_path("C:/Users/<USER>/Pictures/MyDataset")
        
        # 使用回调函数返回特定图像路径
        def get_selected_images():
            # 返回用户选择的图像路径列表
            return ["C:/Users/<USER>/Pictures/image1.jpg", "C:/Users/<USER>/Pictures/image2.jpg"]
            
        success = FolderImporter.load_from_path(get_selected_images)
        ```
        """
        # 重置图像显示标志，确保每次加载新图像时都能正确显示第一张
        FolderImporter._first_image_shown = False
        
        try:
            # 获取主窗口实例
            labeling_widget = FolderImporter.__get_main_widget()
            if not labeling_widget:
                print( "错误：无法获取到核心的 labeling_widget 实例来执行操作。" )
                return False

            # 清空已加载的图像列表（无论加载何种类型的图像）
            print( "清空已加载的图像列表..." )
            # 在主线程中执行UI操作
            FolderImporter.__run_in_main_thread(
                lambda: (
                    labeling_widget.file_list_widget.clear(),
                    setattr( labeling_widget, 'fn_to_index', {} )    # 重置文件索引映射字典
                )
            )

            # 判断参数类型
            if callable( path_or_callback ):
                # 参数是回调函数
                try:
                    print( "使用回调函数获取图像路径..." )
                    image_paths = path_or_callback()

                    # 验证回调函数返回的结果
                    if not isinstance( image_paths, list ):
                        print( f"错误：回调函数返回的不是列表而是 {type(image_paths)}" )
                        return False

                    if not image_paths:
                        print( "警告：回调函数返回的图像路径列表为空" )
                        return False

                    print( f"已从回调函数获取 {len(image_paths)} 个图像路径" )

                    # 检查并记录每个路径
                    for i, path in enumerate( image_paths[ :5 ] ):    # 只记录前5个路径
                        print( f"图像路径 {i+1}: {path}" )
                    if len( image_paths ) > 5:
                        print( f"... 还有 {len(image_paths) - 5} 个路径" )

                    # 加载指定的图像路径列表
                    print( "开始导入图像文件..." )

                    # 创建事件循环，等待图像加载完成
                    load_loop = QEventLoop()

                    # 定义加载完成的回调函数
                    def on_import_done( result ):
                        nonlocal success
                        success = result
                        # 使用计时器延迟200毫秒退出事件循环，确保所有导入操作完全完成
                        # 这有助于避免导入后的图像显示被中断
                        QTimer.singleShot( 200, load_loop.quit )

                    # 创建一个加载函数，在其中调用导入并设置结果
                    def import_images():
                        result = labeling_widget.import_image_files( image_paths )
                        on_import_done( result )
                        return result

                    # 在主线程中执行导入操作
                    success = False
                    FolderImporter.__run_in_main_thread( import_images )

                    # 启动超时计时器 (10秒)
                    timer = QTimer()
                    timer.setSingleShot( True )
                    timer.timeout.connect( load_loop.quit )
                    timer.start( 10 )    # 10秒超时，确保有足够时间完成导入

                    # 等待直到加载完成或超时
                    load_loop.exec_()

                    # 停止计时器
                    timer.stop()

                    # 如果无法获取返回值，假设成功
                    if success is None:
                        success = True

                    # 确保显示第一张图像
                    try:
                        if success:
                            if labeling_widget.image_list and not FolderImporter._first_image_shown:
                                print( "确保显示第一张图像..." )
                                # 设置标志，防止重复显示
                                FolderImporter._first_image_shown = True
                                
                                # 延迟300毫秒后显示第一张图像，确保UI更新完毕
                                def delayed_show():
                                    # 在主线程中执行所有UI相关操作
                                    def show_first_image():
                                        # 强制选中第一个文件
                                        if labeling_widget.file_list_widget.count() > 0:
                                            labeling_widget.file_list_widget.setCurrentRow( 0 )
                                            labeling_widget.file_list_widget.repaint()
    
                                        # 明确指定load=True以确保加载图像
                                        labeling_widget.open_next_image( load = True )
    
                                        # 如果当前文件名已设置，显式加载该文件
                                        if labeling_widget.filename and labeling_widget.image_list:
                                            print( f"显式加载文件: {labeling_widget.filename}" )
                                            labeling_widget.load_file( labeling_widget.filename )
    
                                        return True
    
                                    # 在主线程中执行显示第一张图像的操作
                                    FolderImporter.__run_in_main_thread( show_first_image )
                                
                                # 使用计时器延迟执行显示操作
                                timer = QTimer()
                                timer.setSingleShot(True)
                                timer.timeout.connect(delayed_show)
                                timer.start(300)

                    except Exception as e:
                        print( f"尝试显示第一张图像时出错: {e}" )
                        traceback.print_exc()
                        # 不影响返回结果

                    if success:
                        print( "图像文件导入成功" )
                    else:
                        print( "图像文件导入失败" )
                    return success

                except Exception as e:
                    print( f"执行回调函数或导入图像文件时出错：{e}" )
                    traceback.print_exc()
                    return False

            elif isinstance( path_or_callback, str ):
                # 参数是目录路径
                dir_path = path_or_callback

                # 验证路径参数
                if not dir_path:
                    print( "错误：提供的路径为空" )
                    return False

                # 验证目录是否存在
                if not os.path.exists( dir_path ):
                    print( f"错误：目录不存在 -> {dir_path}" )
                    return False

                if not os.path.isdir( dir_path ):
                    print( f"错误：提供的路径不是目录 -> {dir_path}" )
                    return False

                # 检查目录中是否有文件
                try:
                    has_files = any( os.path.isfile( os.path.join( dir_path, f ) ) for f in os.listdir( dir_path ) )
                    if not has_files:
                        print( f"警告：目录 {dir_path} 中似乎没有文件" )
                except Exception as e:
                    print( f"检查目录内容时出错: {e}" )
                    # 继续尝试导入，因为这只是一个警告

                # 加载整个文件夹的图像，明确指定 load=True 确保加载图像
                print( f"开始导入文件夹 {dir_path} 中的图像..." )

                # 创建事件循环，等待图像加载完成
                load_loop = QEventLoop()

                # 定义加载完成的回调函数
                def on_folder_import_done():
                    # 使用计时器延迟200毫秒退出事件循环，确保所有导入操作完全完成
                    # 这有助于避免导入后的图像显示被中断
                    QTimer.singleShot( 200, load_loop.quit )

                # 创建一个加载函数，在其中调用导入
                def import_folder():
                    result = labeling_widget.import_image_folder( dirpath = dir_path, load = True )
                    # 导入完成后调用回调
                    on_folder_import_done()
                    return result

                # 在主线程中执行导入操作
                FolderImporter.__run_in_main_thread( import_folder )

                # 启动超时计时器 (10秒)
                timer = QTimer()
                timer.setSingleShot( True )
                timer.timeout.connect( load_loop.quit )
                timer.start( 10000 )    # 10秒超时

                # 等待直到加载完成或超时
                load_loop.exec_()

                # 停止计时器
                timer.stop()

                # 确保显示第一张图像
                try:
                    if labeling_widget.image_list and not FolderImporter._first_image_shown:
                        print( "确保显示第一张图像..." )
                        # 设置标志，防止重复显示
                        FolderImporter._first_image_shown = True
                        
                        # 延迟300毫秒后显示第一张图像，确保UI更新完毕
                        def delayed_show():
                            # 在主线程中执行所有UI相关操作
                            def show_first_image():
                                # 强制选中第一个文件
                                if labeling_widget.file_list_widget.count() > 0:
                                    labeling_widget.file_list_widget.setCurrentRow( 0 )
                                    labeling_widget.file_list_widget.repaint()

                                # 明确指定load=True以确保加载图像
                                labeling_widget.open_next_image( load = True )

                                # 如果当前文件名已设置，显式加载该文件
                                if labeling_widget.filename and labeling_widget.image_list:
                                    print( f"显式加载文件: {labeling_widget.filename}" )
                                    labeling_widget.load_file( labeling_widget.filename )

                                return True

                            # 在主线程中执行显示第一张图像的操作
                            FolderImporter.__run_in_main_thread( show_first_image )
                        
                        # 使用计时器延迟执行显示操作
                        timer = QTimer()
                        timer.setSingleShot(True)
                        timer.timeout.connect(delayed_show)
                        timer.start(300)

                except Exception as e:
                    print( f"尝试显示第一张图像时出错: {e}" )
                    traceback.print_exc()
                    # 不影响返回结果

                print( "文件夹导入指令已发送" )
                return True

            else:
                # 参数类型不正确
                print( f"错误：参数类型不支持，必须是字符串路径或回调函数，而不是 {type(path_or_callback)}" )
                return False

        except Exception as e:
            print( f"加载过程中发生未知错误：{e}" )
            traceback.print_exc()
            return False
