# -*- coding: utf-8 -*-

# Resource object code
#
# Created by: The Resource Compiler for PyQt5 (Qt v5.15.2)
#
# WARNING! All changes made in this file will be lost!

from PyQt5 import QtCore

qt_resource_data = b"\
\x00\x00\x09\x45\
\x2f\
\x2a\x0d\x0a\x20\x2a\x20\x58\x2d\x41\x6e\x79\x4c\x61\x62\x65\x6c\
\x69\x6e\x67\x20\x2d\x20\xe5\x85\xa8\xe5\xb1\x80\xe6\x8c\x89\xe9\
\x92\xae\xe6\xa0\xb7\xe5\xbc\x8f\xe8\xa1\xa8\x0d\x0a\x20\x2a\x0d\
\x0a\x20\x2a\x20\xe8\xbf\x99\xe4\xbb\xbd\x20\x51\x53\x53\x20\xe6\
\x96\x87\xe4\xbb\xb6\xe4\xb8\xba\x20\x51\x50\x75\x73\x68\x42\x75\
\x74\x74\x6f\x6e\x20\xe6\x8f\x90\xe4\xbe\x9b\xe4\xba\x86\xe7\xbb\
\x9f\xe4\xb8\x80\xe4\xb8\x94\xe7\x8e\xb0\xe4\xbb\xa3\xe7\x9a\x84\
\xe5\xa4\x96\xe8\xa7\x82\xe3\x80\x82\x0d\x0a\x20\x2a\x20\xe6\xa0\
\xb7\xe5\xbc\x8f\xe6\xb6\xb5\xe7\x9b\x96\xe4\xba\x86\xe6\x8c\x89\
\xe9\x92\xae\xe7\x9a\x84\xe5\x90\x84\xe7\xa7\x8d\xe4\xba\xa4\xe4\
\xba\x92\xe7\x8a\xb6\xe6\x80\x81\xef\xbc\x8c\xe5\xa6\x82\xe6\x82\
\xac\xe5\x81\x9c\xe3\x80\x81\xe6\x8c\x89\xe4\xb8\x8b\xe3\x80\x81\
\xe8\x81\x9a\xe7\x84\xa6\xe5\x92\x8c\xe7\xa6\x81\xe7\x94\xa8\xe3\
\x80\x82\x0d\x0a\x20\x2a\x0d\x0a\x20\x2a\x20\xe4\xb8\xbb\xe8\x89\
\xb2\xe8\xb0\x83\x3a\x0d\x0a\x20\x2a\x20\x2d\x20\xe4\xb8\xbb\xe8\
\x89\xb2\x20\x28\xe8\x93\x9d\xe8\x89\xb2\x29\x3a\x20\x23\x33\x34\
\x39\x38\x64\x31\x0d\x0a\x20\x2a\x20\x2d\x20\xe6\x82\xac\xe5\x81\
\x9c\xe8\x89\xb2\x3a\x20\x23\x35\x64\x61\x64\x65\x32\x0d\x0a\x20\
\x2a\x20\x2d\x20\xe6\x8c\x89\xe4\xb8\x8b\xe8\x89\xb2\x3a\x20\x23\
\x32\x38\x37\x34\x61\x36\x0d\x0a\x20\x2a\x20\x2d\x20\xe7\xa6\x81\
\xe7\x94\xa8\xe8\x83\x8c\xe6\x99\xaf\xe8\x89\xb2\x3a\x20\x23\x64\
\x35\x64\x38\x64\x63\x0d\x0a\x20\x2a\x20\x2d\x20\xe7\xa6\x81\xe7\
\x94\xa8\xe6\x96\x87\xe5\xad\x97\xe8\x89\xb2\x3a\x20\x23\x61\x31\
\x61\x31\x61\x31\x0d\x0a\x20\x2a\x20\x2d\x20\xe8\xbe\xb9\xe6\xa1\
\x86\xe8\x89\xb2\x3a\x20\x23\x61\x61\x62\x37\x62\x38\x0d\x0a\x20\
\x2a\x20\x2d\x20\xe8\x81\x9a\xe7\x84\xa6\xe8\xbd\xae\xe5\xbb\x93\
\xe8\x89\xb2\x3a\x20\x23\x66\x33\x39\x63\x31\x32\x0d\x0a\x20\x2a\
\x2f\x0d\x0a\x0d\x0a\x2f\x2a\x20\x2d\x2d\x2d\x20\x51\x50\x75\x73\
\x68\x42\x75\x74\x74\x6f\x6e\x20\xe5\x9f\xba\xe7\xa1\x80\xe6\xa0\
\xb7\xe5\xbc\x8f\x20\x2d\x2d\x2d\x20\x2a\x2f\x0d\x0a\x51\x50\x75\
\x73\x68\x42\x75\x74\x74\x6f\x6e\x20\x7b\x0d\x0a\x20\x20\x20\x20\
\x62\x61\x63\x6b\x67\x72\x6f\x75\x6e\x64\x2d\x63\x6f\x6c\x6f\x72\
\x3a\x20\x23\x30\x35\x33\x64\x36\x32\x20\x21\x69\x6d\x70\x6f\x72\
\x74\x61\x6e\x74\x3b\x20\x2f\x2a\x20\xe4\xb8\xbb\xe8\x83\x8c\xe6\
\x99\xaf\xe8\x89\xb2\x20\x2d\x20\xe8\x93\x9d\xe8\x89\xb2\x20\x2a\
\x2f\x0d\x0a\x20\x20\x20\x20\x63\x6f\x6c\x6f\x72\x3a\x20\x77\x68\
\x69\x74\x65\x20\x21\x69\x6d\x70\x6f\x72\x74\x61\x6e\x74\x3b\x20\
\x2f\x2a\x20\xe6\x96\x87\xe5\xad\x97\xe9\xa2\x9c\xe8\x89\xb2\x20\
\x2d\x20\xe7\x99\xbd\xe8\x89\xb2\x20\x2a\x2f\x0d\x0a\x20\x20\x20\
\x20\x62\x6f\x72\x64\x65\x72\x3a\x20\x31\x70\x78\x20\x73\x6f\x6c\
\x69\x64\x20\x23\x30\x34\x33\x33\x35\x33\x20\x21\x69\x6d\x70\x6f\
\x72\x74\x61\x6e\x74\x3b\x20\x2f\x2a\x20\xe8\xbe\xb9\xe6\xa1\x86\
\xe9\xa2\x9c\xe8\x89\xb2\x20\x2a\x2f\x0d\x0a\x20\x20\x20\x20\x62\
\x6f\x72\x64\x65\x72\x2d\x72\x61\x64\x69\x75\x73\x3a\x20\x35\x70\
\x78\x20\x21\x69\x6d\x70\x6f\x72\x74\x61\x6e\x74\x3b\x20\x2f\x2a\
\x20\xe5\x9c\x86\xe8\xa7\x92\xe5\xa4\xa7\xe5\xb0\x8f\x20\x2a\x2f\
\x0d\x0a\x20\x20\x20\x20\x70\x61\x64\x64\x69\x6e\x67\x3a\x20\x38\
\x70\x78\x20\x31\x36\x70\x78\x20\x21\x69\x6d\x70\x6f\x72\x74\x61\
\x6e\x74\x3b\x20\x2f\x2a\x20\xe5\x86\x85\xe8\xbe\xb9\xe8\xb7\x9d\
\x20\x28\xe5\x9e\x82\xe7\x9b\xb4\x38\x70\x78\x2c\x20\xe6\xb0\xb4\
\xe5\xb9\xb3\x31\x36\x70\x78\x29\x20\x2a\x2f\x0d\x0a\x20\x20\x20\
\x20\x66\x6f\x6e\x74\x2d\x73\x69\x7a\x65\x3a\x20\x32\x30\x70\x78\
\x20\x21\x69\x6d\x70\x6f\x72\x74\x61\x6e\x74\x3b\x20\x2f\x2a\x20\
\xe5\xad\x97\xe4\xbd\x93\xe5\xa4\xa7\xe5\xb0\x8f\x20\x2a\x2f\x0d\
\x0a\x20\x20\x20\x20\x66\x6f\x6e\x74\x2d\x77\x65\x69\x67\x68\x74\
\x3a\x20\x62\x6f\x6c\x64\x20\x21\x69\x6d\x70\x6f\x72\x74\x61\x6e\
\x74\x3b\x20\x2f\x2a\x20\xe5\xad\x97\xe4\xbd\x93\xe5\x8a\xa0\xe7\
\xb2\x97\x20\x2a\x2f\x0d\x0a\x20\x20\x20\x20\x6f\x75\x74\x6c\x69\
\x6e\x65\x3a\x20\x6e\x6f\x6e\x65\x20\x21\x69\x6d\x70\x6f\x72\x74\
\x61\x6e\x74\x3b\x20\x2f\x2a\x20\xe7\xa7\xbb\xe9\x99\xa4\xe9\xbb\
\x98\xe8\xae\xa4\xe8\xbd\xae\xe5\xbb\x93\x20\x2a\x2f\x0d\x0a\x7d\
\x0d\x0a\x0d\x0a\x0d\x0a\x0d\x0a\x2f\x2a\x20\x2d\x2d\x2d\x20\xe6\
\x82\xac\xe5\x81\x9c\xe7\x8a\xb6\xe6\x80\x81\x20\x2d\x2d\x2d\x20\
\x2a\x2f\x0d\x0a\x23\x70\x75\x73\x68\x42\x75\x74\x74\x6f\x6e\x5f\
\x33\x32\x3a\x68\x6f\x76\x65\x72\x20\x7b\x0d\x0a\x20\x20\x20\x20\
\x62\x61\x63\x6b\x67\x72\x6f\x75\x6e\x64\x2d\x63\x6f\x6c\x6f\x72\
\x3a\x20\x23\x66\x36\x31\x31\x63\x34\x20\x21\x69\x6d\x70\x6f\x72\
\x74\x61\x6e\x74\x3b\x20\x2f\x2a\x20\xe6\x82\xac\xe5\x81\x9c\xe6\
\x97\xb6\xe5\x8f\x98\xe4\xba\xae\xe7\x9a\x84\xe8\x83\x8c\xe6\x99\
\xaf\xe8\x89\xb2\x20\x2a\x2f\x0d\x0a\x20\x20\x20\x20\x62\x6f\x72\
\x64\x65\x72\x3a\x20\x31\x70\x78\x20\x73\x6f\x6c\x69\x64\x20\x23\
\x35\x64\x61\x64\x65\x32\x20\x21\x69\x6d\x70\x6f\x72\x74\x61\x6e\
\x74\x3b\x0d\x0a\x7d\x0d\x0a\x0d\x0a\x2f\x2a\x20\x2d\x2d\x2d\x20\
\xe6\x8c\x89\xe4\xb8\x8b\xe7\x8a\xb6\xe6\x80\x81\x20\x2d\x2d\x2d\
\x20\x2a\x2f\x0d\x0a\x51\x50\x75\x73\x68\x42\x75\x74\x74\x6f\x6e\
\x3a\x70\x72\x65\x73\x73\x65\x64\x20\x7b\x0d\x0a\x20\x20\x20\x20\
\x62\x61\x63\x6b\x67\x72\x6f\x75\x6e\x64\x2d\x63\x6f\x6c\x6f\x72\
\x3a\x20\x23\x32\x38\x37\x34\x61\x36\x3b\x20\x2f\x2a\x20\xe6\x8c\
\x89\xe4\xb8\x8b\xe6\x97\xb6\xe5\x8f\x98\xe6\xb7\xb1\xe7\x9a\x84\
\xe8\x83\x8c\xe6\x99\xaf\xe8\x89\xb2\x20\x2a\x2f\x0d\x0a\x20\x20\
\x20\x20\x62\x6f\x72\x64\x65\x72\x3a\x20\x31\x70\x78\x20\x73\x6f\
\x6c\x69\x64\x20\x23\x32\x31\x36\x31\x38\x63\x3b\x0d\x0a\x20\x20\
\x20\x20\x70\x61\x64\x64\x69\x6e\x67\x2d\x74\x6f\x70\x3a\x20\x39\
\x70\x78\x3b\x20\x2f\x2a\x20\xe5\x90\x91\xe4\xb8\x8b\xe8\xbd\xbb\
\xe5\xbe\xae\xe7\xa7\xbb\xe5\x8a\xa8\xef\xbc\x8c\xe6\xa8\xa1\xe6\
\x8b\x9f\xe6\x8c\x89\xe4\xb8\x8b\xe6\x95\x88\xe6\x9e\x9c\x20\x2a\
\x2f\x0d\x0a\x20\x20\x20\x20\x70\x61\x64\x64\x69\x6e\x67\x2d\x62\
\x6f\x74\x74\x6f\x6d\x3a\x20\x37\x70\x78\x3b\x0d\x0a\x7d\x0d\x0a\
\x0d\x0a\x2f\x2a\x20\x2d\x2d\x2d\x20\xe8\x81\x9a\xe7\x84\xa6\xe7\
\x8a\xb6\xe6\x80\x81\x20\x2d\x2d\x2d\x20\x2a\x2f\x0d\x0a\x2f\x2a\
\x20\xe5\xbd\x93\xe9\x80\x9a\xe8\xbf\x87\xe9\x94\xae\xe7\x9b\x98\
\x54\x61\x62\xe9\x94\xae\xe9\x80\x89\xe4\xb8\xad\xe6\x97\xb6\xef\
\xbc\x8c\xe6\x98\xbe\xe7\xa4\xba\xe4\xb8\x80\xe4\xb8\xaa\xe6\xa9\
\x99\xe8\x89\xb2\xe8\xbd\xae\xe5\xbb\x93\x20\x2a\x2f\x0d\x0a\x51\
\x50\x75\x73\x68\x42\x75\x74\x74\x6f\x6e\x3a\x66\x6f\x63\x75\x73\
\x20\x7b\x0d\x0a\x20\x20\x20\x20\x62\x6f\x72\x64\x65\x72\x3a\x20\
\x32\x70\x78\x20\x73\x6f\x6c\x69\x64\x20\x23\x66\x33\x39\x63\x31\
\x32\x3b\x20\x2f\x2a\x20\xe8\x81\x9a\xe7\x84\xa6\xe6\x97\xb6\xe7\
\x9a\x84\xe8\xbd\xae\xe5\xbb\x93\xe9\xa2\x9c\xe8\x89\xb2\x20\x2d\
\x20\xe6\xa9\x99\xe8\x89\xb2\x20\x2a\x2f\x0d\x0a\x7d\x0d\x0a\x0d\
\x0a\x2f\x2a\x20\x2d\x2d\x2d\x20\xe7\xa6\x81\xe7\x94\xa8\xe7\x8a\
\xb6\xe6\x80\x81\x20\x2d\x2d\x2d\x20\x2a\x2f\x0d\x0a\x51\x50\x75\
\x73\x68\x42\x75\x74\x74\x6f\x6e\x3a\x64\x69\x73\x61\x62\x6c\x65\
\x64\x20\x7b\x0d\x0a\x20\x20\x20\x20\x62\x61\x63\x6b\x67\x72\x6f\
\x75\x6e\x64\x2d\x63\x6f\x6c\x6f\x72\x3a\x20\x23\x64\x35\x64\x38\
\x64\x31\x3b\x20\x2f\x2a\x20\xe7\xa6\x81\xe7\x94\xa8\xe6\x97\xb6\
\xe7\x9a\x84\xe8\x83\x8c\xe6\x99\xaf\xe8\x89\xb2\x20\x2d\x20\xe7\
\x81\xb0\xe8\x89\xb2\x20\x2a\x2f\x0d\x0a\x20\x20\x20\x20\x63\x6f\
\x6c\x6f\x72\x3a\x20\x23\x61\x31\x61\x31\x61\x31\x3b\x20\x2f\x2a\
\x20\xe7\xa6\x81\xe7\x94\xa8\xe6\x97\xb6\xe7\x9a\x84\xe6\x96\x87\
\xe5\xad\x97\xe9\xa2\x9c\xe8\x89\xb2\x20\x2d\x20\xe6\xb7\xb1\xe7\
\x81\xb0\xe8\x89\xb2\x20\x2a\x2f\x0d\x0a\x20\x20\x20\x20\x62\x6f\
\x72\x64\x65\x72\x3a\x20\x31\x70\x78\x20\x73\x6f\x6c\x69\x64\x20\
\x23\x62\x64\x63\x33\x63\x37\x3b\x20\x2f\x2a\x20\xe7\xa6\x81\xe7\
\x94\xa8\xe6\x97\xb6\xe7\x9a\x84\xe8\xbe\xb9\xe6\xa1\x86\xe9\xa2\
\x9c\xe8\x89\xb2\x20\x2a\x2f\x0d\x0a\x20\x20\x20\x20\x63\x75\x72\
\x73\x6f\x72\x3a\x20\x6e\x6f\x74\x2d\x61\x6c\x6c\x6f\x77\x65\x64\
\x3b\x20\x2f\x2a\x20\xe9\xbc\xa0\xe6\xa0\x87\xe6\x8c\x87\xe9\x92\
\x88\xe5\x8f\x98\xe4\xb8\xba\xe7\xa6\x81\xe7\x94\xa8\xe5\x9b\xbe\
\xe6\xa0\x87\x20\x2a\x2f\x0d\x0a\x0d\x0a\x7d\x0d\x0a\x0d\x0a\x2f\
\x2a\x20\x2d\x2d\x2d\x20\xe5\xb8\xa6\xe7\x89\xb9\xe5\xae\x9a\x20\
\x6f\x62\x6a\x65\x63\x74\x4e\x61\x6d\x65\x20\xe7\x9a\x84\xe6\x8c\
\x89\xe9\x92\xae\xe7\xa4\xba\xe4\xbe\x8b\x20\x2d\x2d\x2d\x20\x2a\
\x2f\x0d\x0a\x2f\x2a\x20\x0d\x0a\x20\x20\x20\xe6\x82\xa8\xe5\x8f\
\xaf\xe4\xbb\xa5\xe4\xb8\xba\xe7\x89\xb9\xe5\xae\x9a\xe7\x9a\x84\
\xe6\x8c\x89\xe9\x92\xae\xe8\xae\xbe\xe7\xbd\xae\x20\x6f\x62\x6a\
\x65\x63\x74\x4e\x61\x6d\x65\xef\xbc\x8c\xe7\x84\xb6\xe5\x90\x8e\
\xe5\x8d\x95\xe7\x8b\xac\xe4\xb8\xba\xe5\xae\x83\xe5\xae\x9a\xe4\
\xb9\x89\xe6\xa0\xb7\xe5\xbc\x8f\xe3\x80\x82\x0d\x0a\x20\x20\x20\
\xe4\xbe\x8b\xe5\xa6\x82\x2c\x20\xe5\x9c\xa8\x20\x51\x74\x20\x44\
\x65\x73\x69\x67\x6e\x65\x72\x20\xe4\xb8\xad\xe8\xae\xbe\xe7\xbd\
\xae\xe4\xb8\x80\xe4\xb8\xaa\xe6\x8c\x89\xe9\x92\xae\xe7\x9a\x84\
\x20\x6f\x62\x6a\x65\x63\x74\x4e\x61\x6d\x65\x20\xe4\xb8\xba\x20\
\x22\x70\x72\x69\x6d\x61\x72\x79\x42\x75\x74\x74\x6f\x6e\x22\xe3\
\x80\x82\x0d\x0a\x2a\x2f\x0d\x0a\x0d\x0a\x2f\x2a\x0d\x0a\x23\x70\
\x72\x69\x6d\x61\x72\x79\x42\x75\x74\x74\x6f\x6e\x20\x7b\x0d\x0a\
\x20\x20\x20\x20\x62\x61\x63\x6b\x67\x72\x6f\x75\x6e\x64\x2d\x63\
\x6f\x6c\x6f\x72\x3a\x20\x23\x32\x37\x61\x65\x36\x30\x3b\x20\x0d\
\x0a\x20\x20\x20\x20\x62\x6f\x72\x64\x65\x72\x3a\x20\x31\x70\x78\
\x20\x73\x6f\x6c\x69\x64\x20\x23\x32\x32\x39\x39\x35\x34\x3b\x0d\
\x0a\x7d\x0d\x0a\x0d\x0a\x23\x70\x72\x69\x6d\x61\x72\x79\x42\x75\
\x74\x74\x6f\x6e\x3a\x68\x6f\x76\x65\x72\x20\x7b\x0d\x0a\x20\x20\
\x20\x20\x62\x61\x63\x6b\x67\x72\x6f\x75\x6e\x64\x2d\x63\x6f\x6c\
\x6f\x72\x3a\x20\x23\x32\x65\x63\x63\x37\x32\x3b\x0d\x0a\x7d\x0d\
\x0a\x0d\x0a\x23\x70\x72\x69\x6d\x61\x72\x79\x42\x75\x74\x74\x6f\
\x6e\x3a\x70\x72\x65\x73\x73\x65\x64\x20\x7b\x0d\x0a\x20\x20\x20\
\x20\x62\x61\x63\x6b\x67\x72\x6f\x75\x6e\x64\x2d\x63\x6f\x6c\x6f\
\x72\x3a\x20\x23\x31\x65\x38\x34\x34\x39\x3b\x0d\x0a\x7d\x0d\x0a\
\x2a\x2f\x0d\x0a\
"

qt_resource_name = b"\
\x00\x09\
\x0a\xb6\xe3\x39\
\x00\x63\
\x00\x75\x00\x73\x00\x74\x00\x6f\x00\x6d\x00\x5f\x00\x75\x00\x69\
\x00\x09\
\x00\x28\xad\x23\
\x00\x73\
\x00\x74\x00\x79\x00\x6c\x00\x65\x00\x2e\x00\x71\x00\x73\x00\x73\
"

qt_resource_struct_v1 = b"\
\x00\x00\x00\x00\x00\x02\x00\x00\x00\x01\x00\x00\x00\x01\
\x00\x00\x00\x00\x00\x02\x00\x00\x00\x01\x00\x00\x00\x02\
\x00\x00\x00\x18\x00\x00\x00\x00\x00\x01\x00\x00\x00\x00\
"

qt_resource_struct_v2 = b"\
\x00\x00\x00\x00\x00\x02\x00\x00\x00\x01\x00\x00\x00\x01\
\x00\x00\x00\x00\x00\x00\x00\x00\
\x00\x00\x00\x00\x00\x02\x00\x00\x00\x01\x00\x00\x00\x02\
\x00\x00\x00\x00\x00\x00\x00\x00\
\x00\x00\x00\x18\x00\x00\x00\x00\x00\x01\x00\x00\x00\x00\
\x00\x00\x01\x97\xe8\x0e\xe0\xab\
"

qt_version = [int(v) for v in QtCore.qVersion().split('.')]
if qt_version < [5, 8, 0]:
    rcc_version = 1
    qt_resource_struct = qt_resource_struct_v1
else:
    rcc_version = 2
    qt_resource_struct = qt_resource_struct_v2

def qInitResources():
    QtCore.qRegisterResourceData(rcc_version, qt_resource_struct, qt_resource_name, qt_resource_data)

def qCleanupResources():
    QtCore.qUnregisterResourceData(rcc_version, qt_resource_struct, qt_resource_name, qt_resource_data)

qInitResources()
