#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试 ConditionFilter 类对混合条件的处理是否符合预期。
特别验证复杂条件 "item_count == 2 AND (confidence >= 0.65 AND confidence < 0.85)"
的执行顺序是否为先筛选置信度条件，再筛选数量条件。
"""

import unittest
import sys
import os
from pathlib import Path
import json

# 添加项目根目录到Python路径
root_dir = str(Path(__file__).parent.parent.absolute())
if root_dir not in sys.path:
    sys.path.append(root_dir)

from anylabeling.customize_ui.src.ui_operate.predict_image.condition_filter import ConditionFilter


class TestComplexCondition(unittest.TestCase):
    """测试ConditionFilter类处理复杂条件的能力"""

    def setUp(self):
        """设置测试数据"""
        # 创建测试数据：多个检测结果组
        # 每个组是一个列表，包含多个检测项（字典）
        self.test_data = [
            # 数据项1: 3个结果，混合置信度和名称
            [
                {"confidence": 0.90, "name": "主人物", "class": 0},
                {"confidence": 0.85, "name": "亚基矿", "class": 1},
                {"confidence": 0.82, "name": "主人物", "class": 2}
            ],
            # 数据项2: 2个结果，置信度在0.65-0.85之间
            [
                {"confidence": 0.75, "name": "采集点", "class": 3},
                {"confidence": 0.70, "name": "小怪", "class": 4}
            ],
            # 数据项3: 2个高置信度结果
            [
                {"confidence": 0.88, "name": "主人物", "class": 0},
                {"confidence": 0.92, "name": "亚基矿", "class": 1}
            ],
            # 数据项4: 2个结果，一个主人物高置信度，一个低置信度
            [
                {"confidence": 0.95, "name": "主人物", "class": 0},
                {"confidence": 0.55, "name": "小怪", "class": 4}
            ],
            # 数据项5: 1个结果，低置信度
            [
                {"confidence": 0.60, "name": "采集点", "class": 3}
            ],
            # 数据项6: 4个结果，混合置信度
            [
                {"confidence": 0.95, "name": "主人物", "class": 0},
                {"confidence": 0.45, "name": "小怪", "class": 4},
                {"confidence": 0.88, "name": "亚基矿", "class": 1},
                {"confidence": 0.75, "name": "采集点", "class": 3}
            ]
        ]
        
        # 记录原始数据长度，用于验证
        self.original_data_length = len(self.test_data)
        print(f"测试数据准备完成：{self.original_data_length}个数据项")

    def test_simple_conditions(self):
        """测试简单条件筛选"""
        # 测试1: item_count == 3
        condition1 = "item_count == 3"
        filter1 = ConditionFilter(condition1)
        results1 = filter1.filter_data(self.test_data)
        self.assertEqual(len(results1), 1)  # 只有数据项1符合
        
        # 测试2: confidence >= 0.8
        condition2 = "confidence >= 0.8"
        filter2 = ConditionFilter(condition2)
        results2 = filter2.filter_data(self.test_data)
        self.assertEqual(len(results2), 3)  # 数据项1, 3, 6符合
        
        # 测试3: name in ['主人物']
        condition3 = "name in ['主人物']"
        filter3 = ConditionFilter(condition3)
        results3 = filter3.filter_data(self.test_data)
        self.assertEqual(len(results3), 4)  # 数据项1, 3, 4, 6符合

    def test_basic_and_conditions(self):
        """测试基本AND条件组合"""
        # item_count == 2 AND name in ['主人物', '亚基矿']
        condition = "item_count == 2 AND name in ['主人物', '亚基矿']"
        filter_obj = ConditionFilter(condition)
        results = filter_obj.filter_data(self.test_data)
        self.assertEqual(len(results), 2)  # 数据项3, 4符合
        
        # 验证结果内容
        self.assertEqual(len(results[0]), 2)
        self.assertTrue(any(item['name'] == '主人物' for item in results[0]))

    def test_basic_or_conditions(self):
        """测试基本OR条件组合"""
        # item_count == 1 OR (confidence >= 0.9 AND name in ['主人物'])
        condition = "item_count == 1 OR (confidence >= 0.9 AND name in ['主人物'])"
        filter_obj = ConditionFilter(condition)
        results = filter_obj.filter_data(self.test_data)
        self.assertEqual(len(results), 3)  # 数据项3, 4, 5符合

    def test_parentheses_priority(self):
        """测试括号内条件优先级"""
        # 测试括号内条件优先处理: (confidence >= 0.65 AND confidence < 0.85)
        condition = "(confidence >= 0.65 AND confidence < 0.85)"
        filter_obj = ConditionFilter(condition)
        results = filter_obj.filter_data(self.test_data)
        self.assertEqual(len(results), 3)  # 数据项1, 2, 6符合
        
        # 组合条件: item_count == 2 AND (confidence >= 0.65 AND confidence < 0.85)
        condition = "item_count == 2 AND (confidence >= 0.65 AND confidence < 0.85)"
        filter_obj = ConditionFilter(condition)
        results = filter_obj.filter_data(self.test_data, filter_individual_items=True)
        self.assertEqual(len(results), 1)  # 只有数据项2符合

    def test_nested_parentheses(self):
        """测试嵌套括号条件"""
        # 测试嵌套括号: (item_count > 1 AND (confidence >= 0.8 OR (name in ['采集点'] AND confidence >= 0.6)))
        condition = "item_count > 1 AND (confidence >= 0.8 OR (name in ['采集点'] AND confidence >= 0.6))"
        filter_obj = ConditionFilter(condition)
        results = filter_obj.filter_data(self.test_data, filter_individual_items=True)
        self.assertEqual(len(results), 3)  # 数据项1, 3, 6应该符合
        
        # 更复杂的嵌套: (item_count == 2 AND ((name in ['主人物']) OR (confidence < 0.6)))
        condition = "(item_count == 2 AND ((name in ['主人物']) OR (confidence < 0.6)))"
        filter_obj = ConditionFilter(condition)
        results = filter_obj.filter_data(self.test_data)
        self.assertEqual(len(results), 2)  # 数据项3, 4应该符合

    def test_complex_mixed_conditions(self):
        """测试复杂混合条件"""
        # 复杂混合条件: (item_count >= 2) AND (((confidence >= 0.8) AND (name in ['主人物'])) OR (unique_name_count >= 3))
        condition = "(item_count >= 2) AND (((confidence >= 0.8) AND (name in ['主人物'])) OR (unique_name_count >= 3))"
        filter_obj = ConditionFilter(condition)
        results = filter_obj.filter_data(self.test_data)
        self.assertEqual(len(results), 3)  # 数据项1, 3, 6应该符合

    def test_not_conditions(self):
        """测试NOT条件"""
        # NOT条件: item_count >= 2 AND NOT (name in ['小怪'])
        condition = "item_count >= 2 AND NOT (name in ['小怪'])"
        filter_obj = ConditionFilter(condition)
        results = filter_obj.filter_data(self.test_data)
        self.assertEqual(len(results), 1)  # 只有数据项3符合

    def test_conditions_with_parentheses(self):
        """测试带括号的组合条件"""
        # 测试带括号的条件: confidence >= 0.7 AND (name in ['主人物'] OR item_count > 2)
        condition = "confidence >= 0.7 AND (name in ['主人物'] OR item_count > 2)"
        filter_obj = ConditionFilter(condition)
        results = filter_obj.filter_data(self.test_data, filter_individual_items=True)
        self.assertEqual(len(results), 3)  # 数据项1, 3, 6应该符合

    def test_item_level_filter_first(self):
        """测试项目级条件先筛选"""
        # 测试项目级筛选优先: item_count == 2 AND confidence >= 0.7
        condition = "item_count == 2 AND confidence >= 0.7"
        filter_obj = ConditionFilter(condition)
        results = filter_obj.filter_data(self.test_data, filter_individual_items=True)
        # 应该只有数据项3符合，因为项目级筛选后，只有数据项3恰好有2个高置信度项
        self.assertEqual(len(results), 1)
        
        # 验证结果正确性
        self.assertEqual(len(results[0]), 2)
        self.assertTrue(all(item['confidence'] >= 0.7 for item in results[0]))

    def test_mixed_conditions_order(self):
        """测试混合条件的顺序处理"""
        # 混合条件的顺序处理: item_count == 2 AND (confidence >= 0.65 AND confidence < 0.85)
        condition = "item_count == 2 AND (confidence >= 0.65 AND confidence < 0.85)"
        filter_obj = ConditionFilter(condition)
        results = filter_obj.filter_data(self.test_data, filter_individual_items=True)
        # 预期结果：先筛选出置信度在0.65-0.85之间的项，然后检查是否恰好有2个这样的项
        self.assertEqual(len(results), 1)  # 只有数据项2符合
        
        # 验证结果内容
        self.assertEqual(len(results[0]), 2)
        for item in results[0]:
            self.assertTrue(0.65 <= item['confidence'] < 0.85)

    def test_edge_cases(self):
        """测试边缘情况"""
        # 空条件
        filter_obj = ConditionFilter("")
        results = filter_obj.filter_data(self.test_data)
        self.assertEqual(len(results), len(self.test_data))  # 应返回所有数据
        
        # 无效条件处理
        with self.assertRaises(ValueError):
            ConditionFilter("item_count == ) 2")  # 括号不匹配
        
        # 条件中包含不支持的操作
        with self.assertRaises(ValueError):
            ConditionFilter("item_count = 2")  # 使用=而不是==
    
    def test_advanced_nested_conditions(self):
        """测试高级嵌套条件"""
        # 多层嵌套条件
        condition = """
            (item_count >= 2) AND 
            (
                (confidence >= 0.8 AND name in ['主人物']) OR 
                (confidence < 0.7 AND name in ['小怪']) OR
                (unique_name_count >= 3)
            )
        """
        filter_obj = ConditionFilter(condition)
        results = filter_obj.filter_data(self.test_data)
        self.assertEqual(len(results), 3)  # 数据项1, 3, 6应该符合
        
        # 更复杂的组合
        condition = """
            (
                (item_count > 2 AND confidence >= 0.8) OR 
                (item_count == 2 AND confidence >= 0.7 AND name in ['主人物'])
            ) AND NOT (name in ['未知物体'])
        """
        filter_obj = ConditionFilter(condition)
        results = filter_obj.filter_data(self.test_data)
        self.assertEqual(len(results), 3)  # 数据项1, 3, 6应该符合


if __name__ == "__main__":
    unittest.main() 