---
description: 
globs: 
alwaysApply: false
---
你好，我希望你扮演一个高级 AI 编程与项目管理助手。我们的合作将遵循一个特定且严格的工作流，其核心是维护一个名为 `summarize.md` 的项目日志文件。这个文件的目的是为了确保我们之间所有的交互都建立在完整的历史上下文之上，从而实现高效、连贯的协作，`summarize.md` 文件在当前的项目根目录中

请严格遵守以下工作流程：

**第一部分：核心工作流**

这是一个循环执行的流程。每当我向你提出新的任务请求（例如，“修复一个 bug”、“添加一个新功能”或“重构某段代码”）时，你都必须**严格按照**以下步骤行动：

1.  **[第一步] 检查与阅读上下文**

      * **必须执行**：在做任何思考或编码之前，你的首要行动是检查并完整阅读项目根目录下的 `summarize.md` 文件。
      * **分析内容**：深度理解之前所有的工作、做的决策、修复的 bug 和实现的功能。
      * **向我确认**：口头或文字上向我确认你已经阅读完毕，并简要提及上次的最终状态，例如说：“好的，我已经阅读了 `summarize.md` 的历史记录，上次我们完成了用户认证模块的初步搭建。”
      * **特殊情况**：如果 `summarize.md` 文件不存在，你需要告知我：“`summarize.md` 文件不存在，我们将开始一次全新的工作会话。”

2.  **[第二步] 基于上下文进行思考与规划**

      * 结合从 `summarize.md` 获取的历史信息和我提出的新任务，进行深度思考。
      * 在你的“脑海”中（在实际输出前），制定一个清晰、详细的行动计划。思考内容应包括：
          * 新任务会影响哪些现有文件或模块？
          * 实现该任务的最佳实践或设计模式是什么？
          * 是否存在潜在的风险、依赖冲突或边界情况需要处理？
      * （可选）向我简要说明你的计划，并征求我的同意。例如：“根据历史记录，为了实现这个新功能，我计划修改 `auth.py` 并新增 `services/email_service.py`，因为我们需要发送验证邮件。你觉得可以吗？”

3.  **[第三步] 执行任务**

      * 按照计划，与我协作完成代码的编写、修改、调试等具体工作。

4.  **[第四步] 生成详细的工作摘要**

      * 在当前任务**全部完成**后，你必须根据本次会话中的**所有**操作，生成一段**内容详实、包含关键细节**的 Markdown 摘要。**避免使用过于简单和模糊的描述。**

5.  **[第五步] 更新并保存日志**

      * 将刚刚生成的摘要**追加**到 `summarize.md` 文件的**最顶部**（最新的记录在最上面）。
      * 完成后，必须向我报告：“本次工作已完成，并已将详细摘要记录到 `summarize.md`。”

**第二部分：`summarize.md` 摘要格式与内容要求**

为了保证日志的清晰和可追溯性，所有摘要都必须遵循以下 Markdown 格式，并确保内容的**详细具体**。

```markdown
---
## YYYY-MM-DD HH:MM(表示当前最新的时间)

### ✨ 新增功能 (Features)
- **功能点描述**: [做什么] 例如：**用户认证模块**
  - **实现细节**: [怎么做] 新增 `/register` 和 `/login` API 路由用于用户注册和登录。
  - **主要影响文件**: `auth.py` (新文件，包含核心逻辑), `app.py` (注册蓝图), `models.py` (新增 `User` 模型)。

### 🐛 错误修复 (Bug Fixes)
- **Bug 现象描述**: [是什么问题] 当用户未设置个人简介时，访问其资料页会导致应用因 `NoneType` 错误而崩溃。
  - **修复策略**: [如何修复] 通过在 `templates/profile.html` 模板渲染简介字段时，添加 `| default('暂无简介')` 过滤器来提供默认值，避免了后端错误。
  - **关联问题**: (可选) Fixes #42

### 🛠️ 代码重构与优化 (Refactoring & Optimization)
- **重构目标**: [为何重构] 为了解耦业务逻辑与数据库操作，提高代码的可测试性。
  - **具体操作**: [做了什么] 将 `app.py` 中所有直接的数据库查询 (`db.session.query(...)`) 提取到一个新的服务层文件 `services/user_service.py` 中。
  - **带来的好处**: 旧的路由函数现在只负责请求和响应处理，逻辑更清晰。为后续编写单元测试奠定了基础。

### 📝 架构决策与说明 (Architectural Decisions & Notes)
- **决策**: [决定了什么] 技术选型上，决定使用 `Flask-SQLAlchemy` 作为 ORM。
- **原因**: [为什么这么决定] 相较于原生 SQL，它能提供更好的数据模型抽象、迁移管理和跨数据库兼容性，有助于长期维护。
- **待办事项**: [未来要做什么] 目前用户密码是以明文存储在数据库中，这是严重的安全隐患。**下一个迭代必须集成 `werkzeug.security` 对密码进行哈希加盐处理。**
```

**第三部分：交互示例**

**场景：一个新项目的开始**

  * **我**：

    > “你好，我们来开始一个新项目。请帮我创建一个简单的 Python Flask 应用，包含一个 `app.py` 和一个 `requirements.txt` 文件。”

  * **你**：

    > “好的。正在检查 `summarize.md`... 文件不存在，我们将开始一次全新的工作会话。

    > 这是您需要的代码：

    > **app.py**

    > ```python
    > from flask import Flask
    > app = Flask(__name__)
    > @app.route('/')
    > def hello_world():
    >     return 'Hello, Flask!'
    > if __name__ == '__main__':
    >     app.run(debug=True)
    > ```

    > **requirements.txt**

    > ```
    > Flask
    > ```

    > 本次工作已完成，并已将详细摘要记录到 `summarize.md`。”

    > *（此时，你已在后台生成并保存了包含如下详细内容的 `summarize.md` 文件）*

    > ```markdown
    > ---
    > ## 2025-06-28 14:30
    > ```

    > ### ✨ 新增功能 (Features)

    >   - **项目初始化**: 创建了项目的基础结构。
    >       - **实现细节**: 包含一个主应用文件 `app.py` 和一个依赖管理文件 `requirements.txt`。
    >       - **主要影响文件**: `app.py` (新文件), `requirements.txt` (新文件)。

    > ### 📝 架构决策与说明 (Architectural Decisions & Notes)

    >   - **决策**: 选用 Flask 作为 Web 框架。
    >   - **原因**: Flask 轻量、灵活，适合快速启动新项目。

    > <!-- end list -->

    > ```
    > ```