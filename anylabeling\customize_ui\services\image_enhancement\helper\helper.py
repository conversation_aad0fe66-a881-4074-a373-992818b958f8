# -*- coding: utf-8 -*-
from typing import List, Tuple, Literal, Dict
import cv2
import numpy as np

Point = Tuple[float, float]
Polygon = List[Point]
Edge = Literal['x_min', 'x_max', 'y_min', 'y_max']

class PolygonClipper:
    """
    一个用于将多边形精确裁剪到矩形边界内的静态工具类。

    该类使用 Sutherland-<PERSON> 算法来确保多边形被正确裁剪，
    而不是简单地将顶点坐标钳位到边界，这会保留多边形的真实几何形状。
    该实现是纯 Python，不依赖任何大型几何库。

    主要公共方法:
        clip(polygon, image_width, image_height): 裁剪单个多边形。
    
    使用示例:
        >>> # 示例 1: 多边形部分超出边界
        >>> polygon_partial = [(50, 50), (150, 50), (150, 150), (50, 150)]
        >>> width, height = 100, 100
        >>> PolygonClipper.clip(polygon_partial, width, height)
        [(100, 50), (100, 100), (50, 100), (50, 50)]

        >>> # 示例 2: 多边形完全超出边界
        >>> polygon_outside = [(200, 200), (300, 200), (250, 300)]
        >>> PolygonClipper.clip(polygon_outside, width, height)
        []

        >>> # 示例 3: 多边形完全在边界内
        >>> polygon_inside = [(20, 20), (80, 20), (80, 80), (20, 80)]
        >>> PolygonClipper.clip(polygon_inside, width, height)
        [(20, 20), (80, 20), (80, 80), (20, 80)]
        
        >>> # 示例 4: 一个更复杂的裁剪场景，一个角被切掉
        >>> complex_polygon = [(-50, 50), (150, 50), (150, 150), (50, 150)]
        >>> PolygonClipper.clip(complex_polygon, 100, 100)
        [(0, 50), (100, 50), (100, 100), (50, 100), (0, 100)]
    """

    @staticmethod
    def clip(
        polygon: Polygon,
        image_width: int,
        image_height: int
    ) -> Polygon:
        """
        使用 Sutherland-Hodgman 算法将单个多边形裁剪到图像边界内。

        此函数依次按图像的四个边界（右、下、左、上）对多边形进行裁剪。

        参数:
            polygon (Polygon): 一个表示多边形顶点的列表，格式为 `[(x1, y1), (x2, y2), ...]`。
            image_width (int): 裁剪区域的宽度。
            image_height (int): 裁剪区域的高度。

        返回:
            Polygon: 一个代表裁剪后新多边形的顶点列表。如果多边形完全在边界外，
                     则返回一个空列表 `[]`。
        """
        if not polygon:
            return []

        # 定义裁剪边界
        boundaries = {
            'x_max': image_width,
            'y_max': image_height,
            'x_min': 0,
            'y_min': 0,
        }
        
        clipped_polygon = polygon
        
        # 依次对每条边界进行裁剪
        for edge, value in boundaries.items():
            if not clipped_polygon:
                break
            clipped_polygon = PolygonClipper._clip_by_edge(
                clipped_polygon,
                edge, # type: ignore
                value
            )
            
        return clipped_polygon

    @staticmethod
    def _is_inside(point: Point, edge: Edge, edge_value: float) -> bool:
        """检查一个点是否在指定边界的"内侧" (包含边界)，并加入容差。"""
        x, y = point
        epsilon = 1e-9  # 引入一个小的容差值来处理浮点数精度问题
        if edge == 'x_max':
            return x <= edge_value + epsilon
        if edge == 'x_min':
            return x >= edge_value - epsilon
        if edge == 'y_max':
            return y <= edge_value + epsilon
        if edge == 'y_min':
            return y >= edge_value - epsilon
        return False

    @staticmethod
    def _compute_intersection(
        p1: Point,
        p2: Point,
        edge: Edge,
        edge_value: float
    ) -> Point:
        """
        健壮地计算线段 (p1, p2) 与裁剪边界的交点。
        
        此版本能正确处理与边界平行的线段，并使用容差来避免浮点数错误。
        """
        x1, y1 = p1
        x2, y2 = p2
        dx, dy = x2 - x1, y2 - y1
        epsilon = 1e-9

        if edge in ['x_min', 'x_max']:
            # 裁剪垂直边界，交点的 x 坐标已知
            ix = edge_value
            # 处理线段是垂直的特殊情况
            if abs(dx) < epsilon:
                # 线段与裁剪边界平行。理论上，如果一个点在内一个在外，这种情况不会发生。
                # 但为健壮性起见，返回线段上的一点。
                return (ix, y1)
            # 通过线性插值计算交点的 y 坐标
            iy = y1 + dy * (ix - x1) / dx
            return (ix, iy)
        else:  # edge in ['y_min', 'y_max']
            # 裁剪水平边界，交点的 y 坐标已知
            iy = edge_value
            # 处理线段是水平的特殊情况
            if abs(dy) < epsilon:
                # 线段与裁剪边界平行。
                return (x1, iy)
            # 通过线性插值计算交点的 x 坐标
            ix = x1 + dx * (iy - y1) / dy
            return (ix, iy)

    @staticmethod
    def _clip_by_edge(
        polygon: Polygon,
        edge: Edge,
        edge_value: float
    ) -> Polygon:
        """
        使用单条裁剪边（无限长直线）裁剪多边形。

        这是 Sutherland-Hodgman 算法的核心步骤。

        参数:
            polygon (Polygon): 输入的多边形。
            edge (Edge): 正在使用的裁剪边类型 ('x_min', 'x_max', 'y_min', 'y_max')。
            edge_value (float): 裁剪边的值（例如，对于 x_max，值是图像宽度）。

        返回:
            Polygon: 被该边裁剪后的新多边形。
        """
        clipped_polygon: Polygon = []
        
        if not polygon:
            return clipped_polygon

        # 遍历多边形的每一条边（由点 s 和 e 定义）
        for i in range(len(polygon)):
            s = polygon[i]
            e = polygon[(i + 1) % len(polygon)]  # 下一个点，如果到末尾则循环回第一个点
            
            s_inside = PolygonClipper._is_inside(s, edge, edge_value)
            e_inside = PolygonClipper._is_inside(e, edge, edge_value)

            if s_inside and e_inside:
                # 情况 1: 边的两个端点都在内侧 -> 只保留终点
                clipped_polygon.append(e)
            
            elif s_inside and not e_inside:
                # 情况 2: 边从内侧穿到外侧 -> 只保留交点
                intersection = PolygonClipper._compute_intersection(s, e, edge, edge_value)
                clipped_polygon.append(intersection)
            
            elif not s_inside and e_inside:
                # 情况 3: 边从外侧穿到内侧 -> 保留交点和终点
                intersection = PolygonClipper._compute_intersection(s, e, edge, edge_value)
                clipped_polygon.append(intersection)
                clipped_polygon.append(e)

            # 情况 4: 边的两个端点都在外侧 -> 不保留任何点
            
        return clipped_polygon 

class VisualizationHelper:
	"""
	一个用于可视化图像、多边形和标签的辅助类。
	通过将绘制逻辑和显示逻辑分离，提供了灵活的可视化功能。
	"""

	def visualize_polygons(
		self,
		image: np.ndarray,
		polygons: List[List[List[float]]],
		labels: List[str],
		color_map: Dict[str, Tuple[int, int, int]],
	) -> np.ndarray:
		"""
		在图像的副本上可视化多边形及其标签，并返回该副本。

		这个方法负责"绘制"逻辑，它不会显示图像。

		参数:
		- image (np.ndarray): 待绘制的原始图像 (BGR格式)。
		- polygons (List[List[List[float]]]): 多边形坐标的嵌套列表。
		- labels (List[str]): 与每个多边形对应的标签名称列表。
		- color_map (Dict[str, Tuple[int, int, int]]): 将标签名称映射到稳定颜色(BGR)的字典。

		返回:
		- np.ndarray: 一个新的、绘制了所有标注的图像 (BGR格式)。
		"""
		vis_image = image.copy()

		for i, polygon_pts in enumerate(polygons):
			# 从映射中获取稳定颜色
			label = labels[i]
			color = color_map.get(label, (0, 0, 255))  # 如果找不到，默认为红色

			# 绘制多边形轮廓
			pts = np.array(polygon_pts, dtype=np.int32)
			cv2.polylines(vis_image, [pts.reshape((-1, 1, 2))], isClosed=True, color=color, thickness=3)

			# --- 智能定位并绘制标签文本 ---
			x, y, w, h = cv2.boundingRect(pts)
			font_face = cv2.FONT_HERSHEY_SIMPLEX
			font_scale = 0.8
			thickness = 2
			(text_w, text_h), _ = cv2.getTextSize(label, font_face, font_scale, thickness)
			
			label_y = y - 10
			if (label_y - text_h) < 0:
				label_y = y + text_h + 10
			
			label_pos = (x, label_y)
			cv2.putText(
				vis_image, label, label_pos, font_face, font_scale,
				(0, 0, 0), thickness, cv2.LINE_AA # 黑色文本以保证可读性
			)
		
		return vis_image

	def display_images(self, images: List[np.ndarray], titles: List[str]):
		"""
		使用 Matplotlib 在一个统一的画布中显示多个图像。

		这个方法负责"显示"逻辑。

		参数:
		- images (List[np.ndarray]): 待显示的图像列表 (BGR格式)。
		- titles (List[str]): 每个图像对应的标题列表。
		"""
		# 延迟导入，仅在需要显示时才导入matplotlib
		try:
			from matplotlib import pyplot as plt
		except ImportError:
			print("错误: 需要安装 matplotlib 才能显示图像。请运行 'pip install matplotlib'")
			return

		num_plots = len(images)
		cols = min(num_plots, 4)
		rows = int(np.ceil(num_plots / cols))

		plt.figure(figsize=(cols * 5, rows * 4))
		
		for i, (image, title) in enumerate(zip(images, titles)):
			plt.subplot(rows, cols, i + 1)
			# Matplotlib 需要 RGB 格式，而 OpenCV 使用 BGR，因此需要转换
			plt.imshow(cv2.cvtColor(image, cv2.COLOR_BGR2RGB))
			plt.title(title)
			plt.axis('off')
		
		plt.tight_layout()
		plt.show()

		
	"""
	使用示例:
	
	# 假设你已经有了 image, polygons, labels
	
	# 1. 初始化辅助工具
	visualizer = VisualizationHelper()
	
	# 2. 创建颜色映射
	color_map = {
		"person": (255, 0, 0),
		"car": (0, 255, 0)
	}
	
	# 3. 生成带标注的图像
	vis_image = visualizer.visualize_polygons(
		image=my_image,
		polygons=my_polygons,
		labels=my_labels,
		color_map=color_map
	)
	
	# 4. 显示单个或多个图像
	visualizer.display_images(
		[my_image, vis_image],
		["Original", "Visualized"]
	)
	""" 