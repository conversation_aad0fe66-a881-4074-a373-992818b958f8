import time
from tkinter import N
from global_tools.ui_tools import constant
from global_tools.ui_tools import CheckBoxManager, InputCompleterCache, QPushButtonManager, QLabelManager, \
 LineEditManager, QProgressBarHelper, LogOutput
from global_tools.utils import Logger, clean_directory, Colors, find_latest_created_folder
from PyQt5.QtWidgets import QCheckBox, QWidget, QLayout, QLineEdit, QVBoxLayout, QLabel, QApplication, QTextEdit
from PyQt5.QtCore import QObject, Qt, QTimer
from PyQt5.QtGui import QMouseEvent, QTextCursor, QTextCharFormat, QColor
import logging
from typing import Dict, List, Union, Optional, Tuple, Set, Any
from collections import deque
import os
import json
import shutil
import random
import yaml
from functools import partial
import re
import cv2
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.patches as patches
import traceback
from global_tools.utils.enhanced_process import EnhancedProcess, SharedDataManager, ProcessLogger
from anylabeling.customize_ui.helper.helper import TextEditManager
import sys



def check_lineedit( line_edit_manager: LineEditManager ):
    # 校验 QLineEdit 的输入是否为 Windows 路径
    validator_path_qlienedit = [ "lineEdit_46", "lineEdit_61", "lineEdit_47", "lineEdit_66", "lineEdit_78" ]
    [
        line_edit_manager.set_input_validator( name = x, validator_type = "regexp", pattern = constant.WINDOWS_PATH_REGEX )
        for x in validator_path_qlienedit
    ]

    # 校验 QLineEdit 的输入是否为数字
    validator_num_qlienedit = [
        "lineEdit_64",
        "lineEdit_65",
        "lineEdit_67",
        "lineEdit_70",
        "lineEdit_71",
        "lineEdit_72",
        "lineEdit_73",
        "lineEdit_74",
        "lineEdit_79",
        "lineEdit_80",
        "lineEdit_81",
        "lineEdit_82",
        "lineEdit_83",
        "lineEdit_84",
    ]
    [ line_edit_manager.set_input_validator( name = x, validator_type = "regexp", pattern = constant.FLOAT_REGEX ) for x in validator_num_qlienedit ]



def clear_layout_widgets( layout: QLayout ):
    """
    递归清空布局容器中所有 QCheckBox 和 QLineEdit 控件的状态和内容。

    此函数会遍历指定的布局 (layout) 及其所有子布局。
    - 如果找到一个 QCheckBox 控件且其处于选中状态，则会取消其选中。
    - 如果找到一个 QLineEdit 控件且其包含文本，则会清空其内容。

    为了获得最佳性能和稳健性，此函数采用迭代方式，使用 `collections.deque`
    作为栈来遍历控件，避免了递归可能导致的栈溢出问题。

    Args:
        layout (QLayout): 需要清空其中控件的顶层布局容器。
                          可以是 QVBoxLayout, QHBoxLayout 等 QLayout 的任何子类。
    """
    if not layout:
        return

    # 使用 collections.deque 作为栈进行迭代，性能优于 list
    layouts_to_process = deque( [ layout ] )

    while layouts_to_process:
        current_layout = layouts_to_process.pop()

        for i in range( current_layout.count() ):
            item = current_layout.itemAt( i )
            if not item:
                continue

            # 检查 item 是否为 widget
            widget = item.widget()
            if widget:
                if isinstance( widget, QCheckBox ) and widget.isChecked():
                    widget.setChecked( False )
                elif isinstance( widget, QLineEdit ) and widget.text():
                    widget.clear()

            # 检查 item 是否为 layout
            sub_layout = item.layout()
            if sub_layout:
                layouts_to_process.append( sub_layout )



# =====================================================================================================================
# =====================================================================================================================
# =====================================================================================================================
# =====================================================================================================================

TRAINING_PARAM_MAPPING = {
    # 路径和名称配置
    'initial_model_path': 'lineEdit_46',    # 首次训练加载的模型路径
    'yaml_config_path': 'lineEdit_61',    # YAML 配置文件路径
    'resume_model_path': 'lineEdit_47',    # 中断后继续训练加载的模型路径
    'project_path': 'lineEdit_66',    # 结果保存的项目路径
    'project_name': 'lineEdit_78',    # 结果保存的项目名称

    # 核心训练参数
    'epochs': 'lineEdit_64',    # 训练轮次
    'image_size': 'lineEdit_70',    # 输入图像尺寸
    'save_period': 'lineEdit_72',    # 模型保存间隔（每N轮）
    'best_save_epoch': 'lineEdit_79',    # 开始保存最优模型的轮次

    # 批次大小 (Batch Size)
    # 注意: UI中存在多个描述相似的输入框，这里进行了区分
    'train_batch_size': 'lineEdit_65',    # 训练批次大小
    'val_batch_size': 'lineEdit_67',    # 验证批次大小 (来自 "bval_batch")

    # 性能相关
    'workers': 'lineEdit_71',    # 数据加载的工作线程数
}



class BestPtFinder:
    """
    一个用于在指定项目目录中自动查找最新 `best.pt` 权重文件的工具类。

    该类通过与 PyQt5 UI 控件交互来获取用户指定的项目路径，
    并在该路径下搜索由训练过程产生的最新文件夹。然后，它会尝试定位
    该文件夹内的 `weights/best.pt` 文件。

    整个查找过程中的关键步骤和结果都会通过注入的 `LogOutput` 实例
    实时反馈到 UI 界面，为用户提供清晰的操作指引和状态更新。

    Attributes:
        __line_edit_manager (LineEditManager): 用于访问 UI 中 QLineEdit 控件的管理器。
        __log_output (LogOutput): 用于在 PyQt5 UI 界面上输出带颜色日志的实例。
        __logger (Logger): 用于在后端记录详细日志的标准日志记录器。
    """

    def __init__( self, line_edit_manager: LineEditManager, log_output: LogOutput, logger: Logger ):
        """
        初始化 BestPtFinder。

        Args:
            line_edit_manager (LineEditManager):
                一个 LineEditManager 类的实例，用于获取 QLineEdit 的输入。
            log_output (LogOutput):
                一个 LogOutput 类的实例，用于在 UI 界面上显示日志。
            logger (Logger):
                一个标准的日志记录器实例，用于记录内部操作日志。
        """
        self.__line_edit_manager = line_edit_manager
        self.__log_output = log_output
        self.__logger = logger

    def __log_info( self, msg: str ):
        """向 UI 和后端同时记录一条信息日志。"""
        if self.__log_output:
            self.__log_output.append( message = msg, color = Colors.PRIMARY )
        if self.__logger:
            self.__logger.info( msg )

    def __log_warning( self, msg: str ):
        """向 UI 和后端同时记录一条警告日志。"""
        if self.__log_output:
            self.__log_output.append( message = msg, color = Colors.WARNING )
        if self.__logger:
            self.__logger.warning( msg )

    def find_best_pt_path( self ) -> Optional[ str ]:
        """
        执行查找最新 `best.pt` 文件的核心逻辑。

        Returns:
            Optional[str]:
                如果成功找到 `best.pt` 文件，则返回其绝对路径。
                如果在任何步骤失败（如路径无效、未找到文件夹或文件），则返回 None。
        """
        self.__log_info( "开始查找最新的 best.pt 文件..." )

        # 1. 获取项目路径
        project_path_le = self.__line_edit_manager.get_line_edit( 'lineEdit_47' )
        if not project_path_le or not project_path_le.text():
            self.__log_warning( "项目路径输入为空，无法查找 best.pt。" )
            return None
        project_path = project_path_le.text()

        # 2. 构建和验证搜索目录
        try:
            current_dir = os.getcwd()
            search_directory = os.path.join( current_dir, "train_result", project_path )
            if not os.path.isdir( search_directory ):
                self.__log_warning( f"指定的项目路径不是一个有效的目录: '{search_directory}'" )
                return None
        except Exception as e:
            self.__log_warning( f"构建项目路径时发生错误: {e}" )
            return None

        # 3. 查找最新的文件夹
        self.__log_info( f"正在目录 '{search_directory}' 中搜索最新的训练文件夹..." )
        latest_folder = find_latest_created_folder( search_directory, logger = self.__logger )
        if not latest_folder:
            self.__log_warning( f"在 '{search_directory}' 中未能找到任何训练文件夹。" )
            return None

        # 4. 定位并验证 best.pt 文件
        best_pt_path = os.path.join( latest_folder, 'weights', 'best.pt' )
        if os.path.isfile( best_pt_path ):
            self.__log_info( f"成功找到 best.pt 文件: {best_pt_path}" )
            return best_pt_path
        else:
            self.__log_warning( f"在最新的训练文件夹 '{latest_folder}' 中未找到 'weights/best.pt' 文件。" )
            return None



def get_training_parameters( line_edit_manager: LineEditManager ) -> Dict[ str, str ]:
    """
    从 UI 界面中提取所有与模型训练相关的参数。

    此函数通过一个预定义的映射 (TRAINING_PARAM_MAPPING)，
    使用 LineEditManager 来获取一系列 QLineEdit 控件的当前文本值。
    这种设计使得参数的增删和修改只需维护映射字典即可，代码耦合度低，易于维护。

    Args:
        line_edit_manager (LineEditManager): 一个 LineEditManager 类的实例，
                                             该实例已管理了包含目标 QLineEdit 的 UI 容器。

    Returns:
        Dict[str, str]:
            一个字典，包含了所有从 UI 中获取到的训练参数。
            键是参数的描述性名称（如 'initial_model_path'），值是用户输入的文本（字符串）。
            如果某个 QLineEdit 控件未找到，其对应的值将是空字符串。

            返回字典的详细结构如下:
            {
                'initial_model_path': str,  # 首次训练加载的模型路径
                'yaml_config_path': str,    # YAML 配置文件路径
                'resume_model_path': str,   # 中断后继续训练加载的模型路径
                'project_path': str,        # 结果保存的项目路径
                'project_name': str,        # 结果保存的项目名称
                'epochs': str,              # 训练轮次
                'image_size': str,          # 输入图像尺寸
                'save_period': str,         # 模型保存间隔
                'best_save_epoch': str,     # 开始保存最优模型的轮次
                'train_batch_size': str,    # 训练批次大小
                'val_batch_size': str,      # 验证批次大小
                'workers': str,             # 数据加载的工作线程数
            }
    """
    params = {}
    for key, object_name in TRAINING_PARAM_MAPPING.items():
        # 假设 LineEditManager 有一个 get_line_edit_by_object_name 方法
        # 基于 ui_utils.md 的设计模式推断
        line_edit = line_edit_manager.get_line_edit( object_name )
        if line_edit:
            params[ key ] = line_edit.text()
        else:
            # 如果控件未找到，则返回一个空字符串，以确保键存在
            params[ key ] = ""
            # 可以在这里添加日志记录，以备调试
            # logger.warning(f"无法找到 objectName 为 '{object_name}' 的 QLineEdit 控件。")

    # 检查 YAML 配置文件路径是否存在
    yaml_config_path = params.get( 'yaml_config_path', '' )
    if yaml_config_path and not os.path.exists( yaml_config_path ):
        raise FileNotFoundError( f"YAML 配置文件路径不存在: {yaml_config_path}" )

    # 检查模型路径文件是否存在
    resume_model_path = params.get( 'resume_model_path', '' )
    initial_model_path = params.get( 'initial_model_path', '' )

    # 检查是否至少有一个模型路径存在
    if resume_model_path and initial_model_path:
        # 两个路径都提供了，检查至少一个存在
        if not os.path.exists( resume_model_path ) and not os.path.exists( initial_model_path ):
            raise FileNotFoundError( f"继续训练的模型路径和初始模型路径都不存在: {resume_model_path}, {initial_model_path}" )
    elif resume_model_path and not os.path.exists( resume_model_path ):
        raise FileNotFoundError( f"继续训练的模型路径不存在: {resume_model_path}" )
    elif initial_model_path and not os.path.exists( initial_model_path ):
        raise FileNotFoundError( f"初始模型路径不存在: {initial_model_path}" )
    # End of Selection

    return params



def set_labels_copy_on_click(
    layouts: Union[ QLayout, List[ QLayout ], List[ QLabel ] ], copy_map: Dict[ str, Any ], log_output: LogOutput, logger: Logger
):
    """
    为指定布局容器或QLabel列表内的所有 QLabel 控件添加点击复制功能。

    此函数会迭代遍历一个或多个 PyQt5 布局容器，或一个QLabel列表，找到其中所有的 QLabel 控件。
    然后，它会为每个 QLabel 动态地设置一个 `mousePressEvent` 事件处理器。
    当用户点击某个 QLabel 时，该处理器会：
    1. 获取该 QLabel 的 objectName。
    2. 使用 objectName 从 `copy_map` 字典中查找对应的值。
    3. 将查找到的值（转换为字符串）复制到系统剪切板。
    4. 在 UI 界面上通过 `log_output` 显示一条成功复制的消息。

    如果一个被点击的 QLabel 的 objectName 不在 `copy_map` 中，
    函数会通过 `logger` 记录一条警告，而不会执行任何操作，从而保证程序的健壮性。

    Args:
        layouts (Union[QLayout, List[QLayout], List[QLabel]]):
            一个或一系列 PyQt5 布局容器，或一个 QLabel 控件列表。
            函数将会在这些容器中搜索 QLabel，或直接对列表中的QLabel进行操作。
        copy_map (Dict[str, Any]):
            一个字典，键是 QLabel 的 objectName，值是当该 QLabel 被点击时需要复制到剪切板的内容。
        log_output (LogOutput):
            一个 LogOutput 类的实例，用于在 PyQt5 UI 界面中显示用户可见的日志信息。
        logger (Logger):
            一个标准的日志记录器实例，用于记录程序内部的、开发者关心的警告或错误信息。
    """
    q_labels_to_process: List[ QLabel ] = []
    _layouts = layouts if isinstance( layouts, list ) else [ layouts ]

    if not _layouts:
        return

    # --- 收集阶段 ---
    first_item = _layouts[ 0 ]
    if isinstance( first_item, QLabel ):
        # 输入是 QLabel 列表
        q_labels_to_process.extend( l for l in _layouts if isinstance( l, QLabel ) )
    elif isinstance( first_item, QLayout ):
        # 输入是 QLayout 列表
        layouts_to_process = deque( _layouts )
        while layouts_to_process:
            current_layout = layouts_to_process.popleft()
            for i in range( current_layout.count() ):
                item = current_layout.itemAt( i )
                if not item:
                    continue
                widget = item.widget()
                if isinstance( widget, QLabel ):
                    q_labels_to_process.append( widget )
                sub_layout = item.layout()
                if sub_layout:
                    layouts_to_process.append( sub_layout )
    else:
        logger.warning( f"set_labels_copy_on_click 接收到不支持的类型: {type(first_item)}" )
        return

    # --- 绑定阶段 ---
    def _on_label_click( label: QLabel, event: QMouseEvent ):
        """实际的事件处理逻辑。"""
        object_name = label.objectName()
        try:
            value_to_copy = copy_map[ object_name ]
            str_value = str( value_to_copy )
            clipboard = QApplication.clipboard()
            clipboard.setText( str_value )
            log_output.append( message = f"成功复制: '{str_value}'", color = Colors.SUCCESS )
        except KeyError:
            logger.warning( f"被点击的 QLabel (objectName='{object_name}') 在 copy_map 中没有对应的条目。" )
        except Exception as e:
            logger.error( f"为 QLabel (objectName='{object_name}') 复制值时发生未知错误: {e}" )
            traceback.print_exc()

    for label in q_labels_to_process:
        # 为了解决严格的类型检查问题，我们使用一个工厂函数来创建
        # 一个具有正确签名的事件处理器闭包。
        def create_handler( lbl: QLabel ):

            def handler( event: QMouseEvent ):
                _on_label_click( lbl, event )

            return handler

        label.mousePressEvent = create_handler( label )    # type: ignore



class QuerySyntaxHighlighter( QObject ):
    """
    为 QTextEdit 提供带有防抖功能的自定义语法高亮和自动格式化。

    该类会监听 QTextEdit 的文本变化，并在用户停止输入一小段时间后（防抖），
    执行以下操作：
    1. 自动格式化: 确保 'AND', 'OR', 'NOT' 等关键字前后有且仅有一个空格。
    2. 语法高亮: 根据预设的规则对文本进行颜色高亮。

    高亮规则 (按优先级):
    1. 条件组 (e.g., 'epoch >= 10'): 每个独立的条件判断语句会被赋予一种随机深色。
    2. 逻辑分组括号 (): 每一对用于逻辑分组的括号及其内容都会被赋予一种独特的随机颜色。
                       (标识符内的括号，如 'mAP50(B)' 中的 '(B)'，则不会被当作逻辑分组)。
    3. 关键字 (AND, OR, NOT): 所有逻辑关键字都会被赋予一种统一的、醒目的颜色。
    """

    def __init__( self, text_edit: QTextEdit, debounce_ms: int = 300 ):
        """
        初始化格式化与高亮器。

        Args:
            text_edit (QTextEdit): 要应用功能的目标 QTextEdit 控件。
            debounce_ms (int): 防抖延迟时间（毫秒）。格式化将在用户停止输入这段时间后触发。
        """
        super().__init__( parent = text_edit )
        self.text_edit = text_edit
        self.debounce_ms = debounce_ms
        self.timer = QTimer( self )
        self.timer.setSingleShot( True )
        self.timer.timeout.connect( self._apply_highlight )
        self.text_edit.textChanged.connect( self.schedule_highlight )

        # 使用单次触发的 QTimer 延迟执行初始格式化
        # 延迟 100ms 以确保控件已完全加载和渲染
        QTimer.singleShot( 100, self._initial_format )

    def _initial_format( self ):
        """
        初始化时执行一次格式化和高亮。
        
        此方法会在控件首次加载完成后被调用，确保即使用户未进行任何输入操作，
        文本也能获得正确的格式化和高亮。
        """
        try:
            # 检查控件是否已经准备好并包含文本
            if self.text_edit and not self.text_edit.isHidden() and self.text_edit.toPlainText():
                # 直接调用高亮方法，避免经过计时器
                self._apply_highlight()
        except Exception as e:
            # 捕获可能的异常，避免在初始化时崩溃
            print( f"初始格式化失败: {str(e)}" )
            traceback.print_exc()

    def schedule_highlight( self ):
        """（重新）启动防抖计时器，在计时结束后触发格式化与高亮。"""
        self.timer.start( self.debounce_ms )

    def _generate_random_color( self ) -> QColor:
        """
        生成一个视觉上醒目且有区分度的随机深色。
        
        此方法会智能生成较深的颜色，确保在白色背景上具有足够的对比度和可读性。
        通过控制RGB通道的值范围并提高颜色饱和度，使得生成的颜色更深、更鲜明。
        
        Returns:
            QColor: 一个醒目的随机深色
        """
        # 降低RGB值的上限，特别是绿色通道（人眼对绿色最敏感）
        # 通过限制最大值，确保生成更深的颜色
        r = random.randint( 10, 140 )    # 降低红色的上限
        g = random.randint( 10, 120 )    # 进一步降低绿色的上限
        b = random.randint( 10, 150 )    # 降低蓝色的上限

        # 提高饱和度 - 确保至少有一个通道的值较高，至少有一个通道的值较低
        # 这种技术可以增加颜色的感知鲜明度和饱和度
        color_boost = random.choice( [ 'r', 'g', 'b' ] )
        if color_boost == 'r':
            r = min( r + 60, 180 )    # 提高红色，但不超过180
            g = max( g - 20, 10 )    # 降低绿色
        elif color_boost == 'g':
            g = min( g + 60, 160 )    # 提高绿色，但不超过160
            b = max( b - 20, 10 )    # 降低蓝色
        else:    # 'b'
            b = min( b + 60, 180 )    # 提高蓝色，但不超过180
            r = max( r - 20, 10 )    # 降低红色

        return QColor( r, g, b )

    def _apply_highlight( self ):
        """核心的格式化与高亮应用方法。"""
        self.text_edit.blockSignals( True )
        try:
            original_text = self.text_edit.toPlainText()
            original_cursor = self.text_edit.textCursor()

            # --- 自动格式化阶段 ---
            text = original_text
            # 1. 对 AND/OR/NOT 采用"非破坏性"智能格式化
            # 在左侧添加空格 (如果缺少)
            text = re.sub( r'(\S)\b(AND|OR|NOT)\b', r'\1 \2', text, flags = re.IGNORECASE )
            # 在右侧添加空格 (如果缺少)
            text = re.sub( r'\b(AND|OR|NOT)\b(\S)', r'\1 \2', text, flags = re.IGNORECASE )

            # 2. 对运算符采用"先扩张，再压缩"的健壮格式化
            text = re.sub( r'([<>=!]=?|=)', r' \1 ', text )
            text = re.sub( r'\s{2,}', ' ', text )

            formatted_text = text.strip()

            if formatted_text != original_text.strip():
                # 记录原始的逻辑位置（行号和列号）
                original_block = original_cursor.block()
                original_pos_in_block = original_cursor.positionInBlock()

                self.text_edit.setPlainText( formatted_text )

                # 恢复光标到逻辑位置
                new_cursor = QTextCursor( self.text_edit.document().findBlockByNumber( original_block.blockNumber() ) )
                new_cursor.movePosition( QTextCursor.Right, n = min( original_pos_in_block, new_cursor.block().length() - 1 ) )
                self.text_edit.setTextCursor( new_cursor )

                # 高亮将在格式化后的文本上执行。
                text = formatted_text
            else:
                text = original_text

            if not text:
                # 如果文本为空，清除所有格式。
                cursor = QTextCursor( self.text_edit.document() )
                cursor.select( QTextCursor.Document )
                cursor.setCharFormat( QTextCharFormat() )
                return

            color_map = [ None ]*len( text )
            keyword_color = QColor( 6, 95, 193 )    # 定义一个醒目的蓝色作为关键字颜色

            # 存储已分配的条件表达式颜色，用于确保相同表达式具有相同颜色
            condition_colors = {}

            # 新的处理顺序: 条件表达式 -> 括号 -> 关键字

            # 第1遍: 处理条件表达式
            # 增强版正则表达式，能够处理更复杂的标识符，包括破折号和多重括号
            # 左侧: 标识符可以包含字母、数字、下划线、点、斜杠、破折号和多个括号组
            # 操作符: 各种比较操作符
            # 右侧: 同样支持复杂标识符或数值
            condition_regex = re.compile(
                r'\b[a-zA-Z0-9_./\-]+(?:\([a-zA-Z0-9_./\-]+\))*\s*(?:[<>=!]=?|=)\s*[a-zA-Z0-9_./\-]+(?:\([a-zA-Z0-9_./\-]+\))*'
            )

            for match in condition_regex.finditer( text ):
                # 跳过如果匹配到的是单独的关键字
                if match.group().upper() in ( 'AND', 'OR', 'NOT' ):
                    continue

                # 提取整个条件表达式作为一个标识符
                condition_expr = match.group().strip()

                # 如果这个条件表达式之前已经分配了颜色，则复用该颜色
                # 否则，生成一个新颜色并存储
                if condition_expr not in condition_colors:
                    condition_colors[ condition_expr ] = self._generate_random_color()

                color = condition_colors[ condition_expr ]

                # 为整个条件表达式的每个字符应用同一种颜色
                for i in range( *match.span() ):
                    color_map[ i ] = color    # type: ignore

            # 第2遍: 处理括号
            # 这个逻辑会忽略那些已经被条件表达式着色的括号
            paren_stack = []
            for i, char in enumerate( text ):
                if char == '(' and color_map[ i ] is None:
                    paren_stack.append( ( i, self._generate_random_color() ) )
                elif char == ')' and color_map[ i ] is None and paren_stack:
                    start_index, color = paren_stack.pop()
                    # 为括号及其内容应用颜色。
                    for j in range( start_index, i + 1 ):
                        color_map[ j ] = color    # type: ignore

            # 第3遍: 处理关键字
            keyword_regex = re.compile( r'\b(AND|OR|NOT)\b', re.IGNORECASE )
            for match in keyword_regex.finditer( text ):
                for i in range( *match.span() ):
                    color_map[ i ] = keyword_color    # type: ignore

            # 应用阶段 - 优化的块状更新
            cursor = QTextCursor( self.text_edit.document() )
            i = 0
            while i < len( text ):
                color = color_map[ i ]
                j = i
                while j < len( text ) and color_map[ j ] == color:
                    j += 1

                char_format = QTextCharFormat()
                if color:
                    char_format.setForeground( color )

                cursor.setPosition( i )
                cursor.setPosition( j, QTextCursor.KeepAnchor )
                cursor.setCharFormat( char_format )

                i = j
        finally:
            self.text_edit.blockSignals( False )



class TargetFunction:

    process_logger: ProcessLogger

    def __call__( self, shared_data_proxy: SharedDataManager, data_queue, process_logger: ProcessLogger, *args, **kwargs ) -> Any:
        from global_tools.utils import Logger, Colors
        from ultralytics import YOLO
        import os
        import torch
        import gc

        os.environ[ 'KMP_DUPLICATE_LIB_OK' ] = 'True'
        os.environ[ "NO_ALBUMENTATIONS_UPDATE" ] = "true"


        current_working_directory = os.getcwd()    # 获取当前工作目录

        self.logger = Logger()

        self.process_logger = process_logger

        self.process_logger.log( {
            "type": "enhanced_process",
            "message": "训练模型进程已启动成功"
        } )

        # 全面清空NVIDIA GPU内存
        if torch.cuda.is_available():
            torch.cuda.synchronize()
            gc.collect()
            torch.cuda.empty_cache()
            self.logger.info( message = "已成功执行全面GPU内存清理。", color = Colors.SUCCESS )
           

        train_parameters = kwargs.get( "train_parameters" )
        self.condition_string = kwargs.get( "condition_string" )

        if not train_parameters:
            raise ValueError( "训练参数为空" )

        ecpohs = int( train_parameters.get( "epochs", 600 ) )    # 训练轮次
        save_period = int( train_parameters.get( "save_period", 5 ) )    # 模型保存间隔
        best_save_epoch = int( train_parameters.get( "best_save_epoch", 20 ) )    # 开始保存最优模型的轮次
        train_batch_size = int( train_parameters.get( "train_batch_size", 20 ) )    # 训练批次大小
        val_batch_size = int( train_parameters.get( "val_batch_size", 20 ) )    # 验证批次大小
        workers = int( train_parameters.get( "workers", 8 ) )    # 数据加载的工作线程数
        image_size = int( train_parameters.get( "image_size", 640 ) )    # 输入图像尺寸
        yaml_config_path = train_parameters.get( "yaml_config_path" )    # YAML 配置文件路径
        initial_model_path = train_parameters.get( "initial_model_path" )    # 首次训练加载的模型路径
        resume_model_path = train_parameters.get( "resume_model_path" )    # 中断后继续训练加载的模型路径
        project_path = train_parameters.get( "project_path" )    # 结果保存的项目路径
        project_name = train_parameters.get( "project_name" )    # 结果保存的项目名称
        train_mode_path = initial_model_path if initial_model_path else resume_model_path    # 训练模型路径
        save_latest_model_path = os.sep.join( [ current_working_directory, project_path, project_name ] )    # 保存最新模型的路径
        shared_data_proxy.set( "save_latest_model_path", save_latest_model_path )

        process_logger.log( {
            "type": "train_parame",
            "message": {
                "epochs": ecpohs,
                "save_period": save_period,
                "best_save_epoch": best_save_epoch,
                "train_batch_size": train_batch_size,
                "val_batch_size": val_batch_size,
                "workers": workers,
                "yaml_config_path": yaml_config_path,
                "project_path": project_path,
                "project_name": project_name,
                "train_mode_path": train_mode_path,
                "image_size": image_size,
            }
        } )

        self.logger.info( message = "模型训练开始", color = Colors.SUCCESS )

        model = YOLO( train_mode_path )

        result = model.train(
            data = yaml_config_path,
            epochs = ecpohs,    # 训练轮次
            batch = train_batch_size,    # 训练批次大小
            val_batch = val_batch_size,
            val_period = 1,    # 每1轮验证一次
            imgsz = image_size,    # 输入图像尺寸
            device = 0,    # GPU设备
            workers = workers,    # 数据加载线程数
            save = True,    # 保存结果6
            save_period = save_period,    # 每5轮保存一次
            project = project_path,    # 项目名称
            name = project_name,    # 实验名称
            optimizer = "auto",    # 优化器
            warmup_epochs = 5,    # 预热轮次
            patience = 0,    # 早停轮次
            close_mosaic = 10,
            seed = 42,    # 随机种子
            amp = True,    # 混合精度训练
            val = True,    # 是否验证
            weight_decay = 0.005,    # 权重衰减，防止过拟合
        # dropout=0.3,
            cache = "disk",
        # cos_lr=True,
        # cache="ram",  # 缓存方式
        # pretrained=True,
            resume = False,
            best_save_epoch = best_save_epoch,    # This might be handled by your custom trainer
            on_val_callback = self.on_val_callback,
            log_callback = self.log_callback,

        # scale=0.9,  # S:0.9; M:0.9; L:0.9; X:0.9
        # mosaic=1.0,
        # mixup=0.05,  # S:0.05; M:0.15; L:0.15; X:0.2
        # copy_paste=0.15,  # S:0.15; M:0.4; L:0.5; X:0.6

        # scale=0.5,  # S:0.9; M:0.9; L:0.9; X:0.9
        # mosaic=1.0,
        # mixup=0.0,  # S:0.05; M:0.15; L:0.15; X:0.2
        # copy_paste=0.1,  # S:0.15; M:0.4; L:0.5; X:0.6
        )

    def log_callback( self, log_entry: Dict[ str, Any ] ):
        self.process_logger.log( log_entry )

    def on_val_callback( self, metrics: dict, epoch_info: tuple ):
        from anylabeling.customize_ui.helper.helper import evaluate_condition_string
        # print(metrics)
        cur_epoch, total_epoch = epoch_info
        metrics[ "cur_ecpohs" ] = cur_epoch
        if evaluate_condition_string( condition_string = self.condition_string, data_dict = metrics, logger = self.logger ):
            return {
                "stop_train": True,
                "close_mosaic": ( True, 10 )
            }
        return False



class TrainModelProcess:

    def __init__(
        self,
        log_output: LogOutput,
        logger: Logger,
        label_manager: QLabelManager,
    ):
        self.log_output = log_output
        self.logger = logger
        self.label_manager = label_manager

        # PyQt5控件统一映射配置字典
        # 建立语义化的控件访问接口，便于后续修改和扩展
        self.__WIDGET_MAPPING = {
            # Label控件 - 训练进度和指标显示相关
            "labels": {
                # 训练进度显示
                "train_batch_progress": {
                    "widget_id": "label_216",
                    "widget_type": "QLabel",
                    "description": "训练批次进度显示标签 - 显示当前批次处理进度",
                    "function": "实时显示训练过程中的批次进度，格式为'当前批次/总批次数'，帮助用户了解单轮训练的进度"
                },
                "train_epoch_progress": {
                    "widget_id": "label_214",
                    "widget_type": "QLabel",
                    "description": "训练轮次进度显示标签 - 显示当前训练轮次进度",
                    "function": "实时显示训练过程中的轮次进度，格式为'当前轮次/总轮次数'，帮助用户了解整体训练进度"
                },

                # 训练损失显示
                "box_loss_display": {
                    "widget_id": "label_218",
                    "widget_type": "QLabel",
                    "description": "Box损失显示标签 - 显示边界框回归损失值",
                    "function": "实时显示训练过程中的边界框损失值，用于监控模型的边界框预测精度"
                },
                "seg_loss_display": {
                    "widget_id": "label_220",
                    "widget_type": "QLabel",
                    "description": "Seg损失显示标签 - 显示分割损失值",
                    "function": "实时显示训练过程中的分割损失值，用于监控模型的分割预测精度"
                },
                "cls_loss_display": {
                    "widget_id": "label_222",
                    "widget_type": "QLabel",
                    "description": "Cls损失显示标签 - 显示分类损失值",
                    "function": "实时显示训练过程中的分类损失值，用于监控模型的分类预测精度"
                },
                "dfl_loss_display": {
                    "widget_id": "label_224",
                    "widget_type": "QLabel",
                    "description": "DFL损失显示标签 - 显示分布焦点损失值",
                    "function": "实时显示训练过程中的DFL损失值，用于监控模型的分布焦点损失"
                },

                # 验证进度显示
                "val_batch_progress": {
                    "widget_id": "label_229",
                    "widget_type": "QLabel",
                    "description": "验证批次进度显示标签 - 显示验证过程中的批次进度",
                    "function": "实时显示验证过程中的批次进度，格式为'当前批次/总批次数'，帮助用户了解验证进度"
                },
                "val_epoch_progress": {
                    "widget_id": "label_227",
                    "widget_type": "QLabel",
                    "description": "验证轮次进度显示标签 - 显示验证过程中的轮次进度",
                    "function": "实时显示验证过程中的轮次进度，格式为'当前轮次/总轮次数'，帮助用户了解验证进度"
                },

                # Box指标显示
                "box_precision_display": {
                    "widget_id": "label_231",
                    "widget_type": "QLabel",
                    "description": "Box精度显示标签 - 显示边界框检测的精度指标",
                    "function": "实时显示验证过程中边界框检测的精度值，用于评估模型的检测性能"
                },
                "box_recall_display": {
                    "widget_id": "label_233",
                    "widget_type": "QLabel",
                    "description": "Box召回率显示标签 - 显示边界框检测的召回率指标",
                    "function": "实时显示验证过程中边界框检测的召回率值，用于评估模型的检测性能"
                },
                "box_map50_display": {
                    "widget_id": "label_235",
                    "widget_type": "QLabel",
                    "description": "Box mAP50显示标签 - 显示边界框检测的mAP@0.5指标",
                    "function": "实时显示验证过程中边界框检测的mAP@0.5值，用于评估模型在IoU=0.5阈值下的检测性能"
                },
                "box_map75_display": {
                    "widget_id": "label_237",
                    "widget_type": "QLabel",
                    "description": "Box mAP75显示标签 - 显示边界框检测的mAP@0.75指标",
                    "function": "实时显示验证过程中边界框检测的mAP@0.75值，用于评估模型在IoU=0.75阈值下的检测性能"
                },
                "box_map50_95_display": {
                    "widget_id": "label_239",
                    "widget_type": "QLabel",
                    "description": "Box mAP50-95显示标签 - 显示边界框检测的mAP@0.5:0.95指标",
                    "function": "实时显示验证过程中边界框检测的mAP@0.5:0.95值，用于评估模型在多个IoU阈值下的综合检测性能"
                },

                # Mask指标显示
                "mask_precision_display": {
                    "widget_id": "label_243",
                    "widget_type": "QLabel",
                    "description": "Mask精度显示标签 - 显示分割掩码的精度指标",
                    "function": "实时显示验证过程中分割掩码的精度值，用于评估模型的分割性能"
                },
                "mask_recall_display": {
                    "widget_id": "label_245",
                    "widget_type": "QLabel",
                    "description": "Mask召回率显示标签 - 显示分割掩码的召回率指标",
                    "function": "实时显示验证过程中分割掩码的召回率值，用于评估模型的分割性能"
                },
                "mask_map50_display": {
                    "widget_id": "label_247",
                    "widget_type": "QLabel",
                    "description": "Mask mAP50显示标签 - 显示分割掩码的mAP@0.5指标",
                    "function": "实时显示验证过程中分割掩码的mAP@0.5值，用于评估模型在IoU=0.5阈值下的分割性能"
                },
                "mask_map75_display": {
                    "widget_id": "label_249",
                    "widget_type": "QLabel",
                    "description": "Mask mAP75显示标签 - 显示分割掩码的mAP@0.75指标",
                    "function": "实时显示验证过程中分割掩码的mAP@0.75值，用于评估模型在IoU=0.75阈值下的分割性能"
                },
                "mask_map50_95_display": {
                    "widget_id": "label_251",
                    "widget_type": "QLabel",
                    "description": "Mask mAP50-95显示标签 - 显示分割掩码的mAP@0.5:0.95指标",
                    "function": "实时显示验证过程中分割掩码的mAP@0.5:0.95值，用于评估模型在多个IoU阈值下的综合分割性能"
                }
            },
            "checkbox": {
                "OBB": {
                    "widget_id": "rotate_35",
                    "widget_type": "QCheckBox",
                    "description": "训练的是 OBB 定向边界框模型",
                },
                "segment": {
                    "widget_id": "rotate_34",
                    "widget_type": "QCheckBox",
                    "description": "训练的是 分割模型",
                }
            }
        }

    def __update_label_text(self, semantic_name: str, text: str) -> bool:
        """
        根据语义化名称更新标签的文本显示。

        Args:
            semantic_name (str): 控件的语义化名称（在__WIDGET_MAPPING中定义）
            text (str): 要设置的文本内容

        Returns:
            bool: 更新成功返回True，失败返回False

        Usage Example:
            ```python
            # 更新训练批次进度
            success = self.__update_label_text("train_batch_progress", "50/100")

            # 更新训练轮次进度
            success = self.__update_label_text("train_epoch_progress", "10/50")

            # 更新Box损失显示
            success = self.__update_label_text("box_loss_display", "0.1234")

            # 更新验证指标
            success = self.__update_label_text("box_precision_display", "0.8567")
            ```
        """
        try:
            # 查找控件配置
            widget_config = None
            for category, widgets in self.__WIDGET_MAPPING.items():
                if semantic_name in widgets:
                    widget_config = widgets[semantic_name]
                    break

            if not widget_config:
                if self.logger:
                    self.logger.warning(f"未找到语义化名称 '{semantic_name}' 对应的控件映射")
                return False

            widget_id = widget_config["widget_id"]
            widget_type = widget_config["widget_type"]

            # 根据控件类型更新文本
            if widget_type == "QLabel":
                if self.label_manager:
                    self.label_manager.set_text(label_name=widget_id, text=text)
                    return True
                else:
                    if self.logger:
                        self.logger.warning("标签管理器不可用")
                    return False
            else:
                if self.logger:
                    self.logger.warning(f"控件类型 '{widget_type}' 不是标签")
                return False

        except Exception as e:
            if self.logger:
                self.logger.error(f"更新标签 '{semantic_name}' 的文本时发生错误: {e}")
            return False

    def __get_widget_info(self, semantic_name: str) -> Optional[dict]:
        """
        获取控件的详细配置信息。

        Args:
            semantic_name (str): 控件的语义化名称

        Returns:
            Optional[dict]: 控件的配置信息字典，如果不存在则返回None

        Usage Example:
            ```python
            # 获取控件配置信息
            info = self.__get_widget_info("train_batch_progress")
            if info:
                print(f"控件ID: {info['widget_id']}")
                print(f"控件类型: {info['widget_type']}")
                print(f"功能描述: {info['description']}")
            ```
        """
        try:
            # 查找控件配置
            for category, widgets in self.__WIDGET_MAPPING.items():
                if semantic_name in widgets:
                    return widgets[semantic_name].copy()
            return None

        except Exception as e:
            if self.logger:
                self.logger.error(f"获取控件 '{semantic_name}' 的信息时发生错误: {e}")
            return None

    def __call__( self, ) -> Any:
        target_function = TargetFunction()

        self.enhanced_process = EnhancedProcess( target = target_function, use_shared_queue_manager = True )
        self.enhanced_process.set_log_callback( self.log_callback )

    @property
    def set_save_best_model_path( self ):
        shared_data = self.enhanced_process.get_shared_data_proxy()
        model_path = shared_data.get( "save_latest_model_path" )    # type: ignore
        if os.path.exists( model_path ):
            model_folder = os.path.normpath( os.path.join( model_path, ".." ) )
            model_folder = find_latest_created_folder( directory_path = model_folder )
            model_folder = os.sep.join( [ model_folder, "weights", "best.pt" ] )
            if os.path.exists( model_folder ):
                self.log_output.append( message = f"找到最佳模型路径: {model_folder}", color = Colors.SUCCESS )
                return model_folder
            else:
                self.log_output.append( message = f"未找到最佳模型路径: {model_folder}", color = Colors.WARNING )
        return False

    def start( self, *args, **kwargs ) -> Any:
        self.enhanced_process.start( *args, **kwargs )

    def stop( self, *args, **kwargs ) -> Any:
        result = self.enhanced_process.terminate_gracefully( timeout = 60 )
        if result:
            self.log_output.append( message = "训练进程已停止", color = Colors.SUCCESS )
            self.logger.info( "训练进程已停止", color = Colors.SUCCESS )
        else:
            self.log_output.append( message = "训练进程停止失败", color = Colors.ERROR )
            self.logger.error( "训练进程停止失败", color = Colors.ERROR )

    def wait_for_completion( self, timeout: Optional[ float ] = None ) -> Any:
        self.enhanced_process.wait_for_completion( timeout = timeout )

    async def log_callback( self, log_entry: Dict[ str, Any ] ):
        if isinstance( log_entry, dict ) and log_entry.get( "type" ) == "enhanced_process":
            self.log_output.append( message = log_entry[ "message" ], color = Colors.SUCCESS )
        elif isinstance( log_entry, dict ) and log_entry.get( "type" ) == "train_parame":
            message = log_entry[ "message" ]
            message = "训练参数:" + "".join( [ f"\n\t{key} -> {value}" for key, value in message.items() ] )
        elif isinstance( log_entry, dict ) and log_entry.get( "type" ) == "train" and log_entry.get( "event" ) == "start":
            message = f"训练开始，total_epoch->{log_entry.get('total_epoch')}"
        elif isinstance( log_entry, dict ) and log_entry.get( "type" ) == "train_progress" and log_entry.get( "event" ) == "batch_end":
            batch, total_batch = log_entry.get( "batch" ), log_entry.get( "total_batch" )
            epoch, total_epoch = log_entry.get( "epoch" ), log_entry.get( "total_epoch" )
            self.__update_label_text( "train_batch_progress", f"{batch}/{total_batch}" )
            self.__update_label_text( "train_epoch_progress", f"{epoch}/{total_epoch}" )
        elif log_entry.get( "type" ) == "train" and log_entry.get( "event" ) == "batch_end":
            
            loss = log_entry.get( "loss", {} )
            if len(loss) == 3:
                box_loss, cls_loss,dfl_loss = loss
                seg_loss = 0
            else:
                box_loss, seg_loss, cls_loss, dfl_loss = loss

            self.__update_label_text( "box_loss_display", f"{box_loss:.4f}" )
            self.__update_label_text( "seg_loss_display", f"{seg_loss:.4f}" )
            self.__update_label_text( "cls_loss_display", f"{cls_loss:.4f}" )
            self.__update_label_text( "dfl_loss_display", f"{dfl_loss:.4f}" )
        elif log_entry.get( "type" ) == "val" and log_entry.get( "event" ) == "start":
            message = f"验证开始，total_epoch->{log_entry.get('total_epoch')}"
        elif log_entry.get( "type" ) == "val_progress" and log_entry.get( "event" ) == "batch_end":
            batch, total_batch = log_entry.get( "batch" ), log_entry.get( "total_batch" )
            self.__update_label_text( "val_batch_progress", f"{batch}/{total_batch}" )
        elif log_entry.get( "type" ) == "val" and log_entry.get( "event" ) == "val_end":
            metrics = log_entry.get( "metrics", {} )

            # Box指标显示
            self.__update_label_text( "box_precision_display", str( metrics.get( "metrics/precision(B)", 0 ) ) )
            self.__update_label_text( "box_recall_display", str( metrics.get( "metrics/recall(B)", 0 ) ) )
            self.__update_label_text( "box_map50_display", str( metrics.get( "metrics/mAP50(B)", 0 ) ) )
            self.__update_label_text( "box_map75_display", str( metrics.get( "metrics/mAP75(B)", 0 ) ) )
            self.__update_label_text( "box_map50_95_display", str( metrics.get( "metrics/mAP50-95(B)", 0 ) ) )

            # Mask指标显示
            self.__update_label_text( "mask_precision_display", str( metrics.get( "metrics/precision(M)", 0 ) ) )
            self.__update_label_text( "mask_recall_display", str( metrics.get( "metrics/recall(M)", 0 ) ) )
            self.__update_label_text( "mask_map50_display", str( metrics.get( "metrics/mAP50(M)", 0 ) ) )
            self.__update_label_text( "mask_map75_display", str( metrics.get( "metrics/mAP75(M)", 0 ) ) )
            self.__update_label_text( "mask_map50_95_display", str( metrics.get( "metrics/mAP50-95(M)", 0 ) ) )

            self.__update_label_text( "val_epoch_progress", f"{log_entry.get('epoch', 0)}/{log_entry.get('total_epoch', 0)}" )
        elif log_entry.get( "type" ) == "train" and log_entry.get( "event" ) == "best_model_save":
            self.log_output.append(
                message = f"ecpoh: {log_entry.get('epoch')}/{log_entry.get('total_epoch')}; {log_entry.get('message')}", color = Colors.SUCCESS
            )

        self.log_output.append( message = message, color = Colors.PRIMARY )
