import traceback
from anylabeling.customize_ui.layout.layout import Ui_Form
import global_tools.utils as global_tools_utils
from PyQt5.QtWidgets import QCheckBox, QLineEdit, QLabel

from anylabeling.customize_ui.helper.const import CONFIG_PATH, X_LABEL_LOG

from anylabeling.customize_ui.helper.helper import evaluate_condition_string, LineEditMutexManager, TextEditManager
import threading
from global_tools.ui_tools import (
	TextEditMemory, InputCompleterCache, LineEditManager,
	get_widgets_from_layout, LineEditMemory, LogOutput, QPushButtonManager, QProgressBarHelper, QLabelManager,
	CheckBoxStateManager,
	CheckBoxManager,
)
import os
from .helper import DataProcessingManager
import threading


class SavePredictData:

	def __init__( self, ui_form: Ui_Form, log_output: LogOutput ):
		self.ui_form = ui_form
		self.log_output = log_output

		self.logger: global_tools_utils.Logger = global_tools_utils.ClassInstanceManager.get_instance(
			X_LABEL_LOG
		)  # type: ignore

		# --------------------------------- QLineEdit ---------------------------------
		self.line_edit_manager = LineEditManager.get_instance()

		# --------------------------------- QPushButton ---------------------------------
		# self.push_button_list = [
		# 	self.ui_form.pushButton_40,
		# ]
		self.push_button_manager = QPushButtonManager.get_instance()
		self.push_button_manager.connect_clicked_signal(
			button_name="pushButton_68", slot=self.save_predict_data_button_function
		)

		# --------------------------------- QCheckBox ---------------------------------
		# self.check_box_list = [
		# 	*get_widgets_from_layout( layout=self.ui_form.horizontalLayout_248, widget_type=QCheckBox ),
		# 	*get_widgets_from_layout( layout=self.ui_form.gridLayout_21, widget_type=QCheckBox ),
		# 	self.ui_form.lineEdit_48,
		# ]

		# 状态管理器，持久化复选框状态
		# self.checkbox_state_manager = CheckBoxStateManager(
		# 	container=self.check_box_list,
		# 	config_dir=CONFIG_PATH,
		# 	log_output=self.log_output,
		# 	logger=self.logger
		# )
		# self.checkbox_state_manager.load_states()

		# QCheckBox 管理器，管理复选框的选中状态
		# self.checkbox_manager = CheckBoxManager(
		# 	*self.check_box_list,
		# 	logger=self.logger
		# )
		self.checkbox_manager = CheckBoxManager.get_instance()

		# 设置互斥选择模式
		# CheckBoxManager( *[ self.ui_form.horizontalLayout_248, ], logger=self.logger ).set_exclusive_selection()
		CheckBoxManager.set_exclusive_selection_static(
			checkboxes=[ *get_widgets_from_layout( self.ui_form.horizontalLayout_530, QCheckBox ) ], logger=self.logger
		)

		# --------------------------------- QLabel ---------------------------------
		# self.label_list = [
		# 	self.ui_form.label_276,
		# 	self.ui_form.label_292,
		# ]
		# self.label_manager = QLabelManager( *self.label_list )
		self.label_manager = QLabelManager.get_instance()

	def __call__( self ):
		pass

	def save_predict_data_button_function( self ):

		def thread_function():
			try:
				data_processing_manager = DataProcessingManager(
					line_edit_manager=self.line_edit_manager,
					checkbox_manager=self.checkbox_manager,
					label_manager=self.label_manager,
					log_output=self.log_output,
					logger=self.logger
				)
				result = data_processing_manager.process_data()
				self.logger.info( f"数据处理结果: {result}" )
			except Exception as e:
				self.logger.error( f"数据处理过程中发生错误: {e}" )

		thread = threading.Thread( target=thread_function )
		thread.start()
