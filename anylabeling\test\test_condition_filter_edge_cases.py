import unittest
import sys
import os
from pathlib import Path
import json
import re

# 设置工作目录为测试文件所在目录
current_dir = r"K:\yolo11\X-AnyLabeling-main"
# os.chdir(current_dir)
# 添加项目根目录到Python路径
sys.path.insert( 0, current_dir )

from anylabeling.customize_ui.src.ui_operate.predict_image.condition_filter import ConditionFilter


class TestConditionFilterEdgeCases(unittest.TestCase):
    """测试ConditionFilter类处理边界情况和错误处理能力"""

    def setUp(self):
        """设置基本测试数据"""
        # 创建基本测试数据
        self.test_data = [
            # 数据项1: 普通数据
            [
                {"confidence": 0.90, "name": "主人物", "class": 0},
                {"confidence": 0.85, "name": "亚基矿", "class": 1},
                {"confidence": 0.70, "name": "小怪", "class": 2}
            ],
            # 数据项2: 空列表
            [],
            # 数据项3: 包含非字典项
            [
                {"confidence": 0.95, "name": "主人物", "class": 0},
                "这不是一个字典",
                123,
                {"confidence": 0.85, "name": "亚基矿", "class": 1}
            ],
            # 数据项4: 包含缺少字段的字典
            [
                {"name": "主人物", "class": 0},  # 缺少confidence
                {"confidence": 0.85, "class": 1},  # 缺少name
                {"confidence": 0.70, "name": "小怪"}  # 缺少class
            ],
            # 数据项5: 包含异常值
            [
                {"confidence": "无效值", "name": "主人物", "class": 0},
                {"confidence": -0.5, "name": "亚基矿", "class": 1},
                {"confidence": 1.5, "name": "小怪", "class": 2}
            ]
        ]

    def test_empty_condition(self):
        """测试空条件字符串"""
        # 空字符串
        filter_obj = ConditionFilter("")
        results = filter_obj.filter_data(self.test_data)
        self.assertEqual(len(results), len(self.test_data), "空条件应返回所有数据项")
        
        # 只有空格的字符串
        filter_obj = ConditionFilter("   ")
        results = filter_obj.filter_data(self.test_data)
        self.assertEqual(len(results), len(self.test_data), "只有空格的条件应返回所有数据项")

    def test_unbalanced_parentheses(self):
        """测试括号不平衡的条件"""
        # 左括号多
        with self.assertRaises(ValueError) as cm:
            ConditionFilter("(confidence >= 0.8")
        self.assertIn("括号不平衡", str(cm.exception), "应检测到左括号不平衡")
        
        # 右括号多
        with self.assertRaises(ValueError) as cm:
            ConditionFilter("confidence >= 0.8)")
        self.assertIn("括号不平衡", str(cm.exception), "应检测到右括号不平衡")
        
        # 嵌套括号不平衡
        with self.assertRaises(ValueError) as cm:
            ConditionFilter("(item_count > 2 AND (confidence >= 0.8)")
        self.assertIn("括号不平衡", str(cm.exception), "应检测到嵌套括号不平衡")

    def test_invalid_operators(self):
        """测试无效的操作符"""
        # 单等号而不是双等号
        with self.assertRaises(ValueError):
            ConditionFilter("item_count = 2")
        
        # 使用未知操作符
        with self.assertRaises(ValueError):
            ConditionFilter("item_count === 2")
        
        # 使用未知逻辑操作符
        with self.assertRaises(ValueError):
            ConditionFilter("item_count > 2 XOR confidence < 0.8")

    def test_invalid_keywords(self):
        """测试无效的关键词"""
        # 使用未定义的关键词
        with self.assertRaises(ValueError):
            ConditionFilter("unknown_keyword > 2")
        
        # 关键词拼写错误
        with self.assertRaises(ValueError):
            ConditionFilter("item_counts > 2")  # 应为item_count

    def test_edge_data_cases(self):
        """测试边缘数据情况"""
        # 测试空数据列表
        filter_obj = ConditionFilter("confidence >= 0.8")
        results = filter_obj.filter_data([])
        self.assertEqual(len(results), 0, "空数据列表应返回空结果")
        
        # 测试None作为数据项
        filter_obj = ConditionFilter("confidence >= 0.8")
        results = filter_obj.filter_data([None, None])
        self.assertEqual(len(results), 0, "包含None的数据列表不应导致错误")

    def test_malformed_expressions(self):
        """测试格式错误的表达式"""
        # 表达式缺少右侧值
        with self.assertRaises(ValueError):
            ConditionFilter("confidence >=")
        
        # 表达式缺少左侧值
        with self.assertRaises(ValueError):
            ConditionFilter(">= 0.8")
        
        # 表达式缺少操作符
        with self.assertRaises(ValueError):
            ConditionFilter("confidence 0.8")

    def test_non_standard_data_handling(self):
        """测试非标准数据处理"""
        # 测试数据项3: 包含非字典项
        filter_obj = ConditionFilter("confidence >= 0.8")
        results = filter_obj.filter_data(self.test_data, filter_individual_items=True)
        # 确保可以处理非字典项且不崩溃
        self.assertTrue(len(results) > 0, "应该能处理包含非字典项的数据")
        
        # 测试数据项4: 包含缺少字段的字典
        filter_obj = ConditionFilter("name in ['主人物']")
        results = filter_obj.filter_data([self.test_data[3]], filter_individual_items=True)
        # 确保可以处理缺少字段的字典且不崩溃
        self.assertTrue(len(results) >= 0, "应该能处理缺少字段的字典")
        
        # 测试数据项5: 包含异常值
        filter_obj = ConditionFilter("confidence >= 0.8")
        results = filter_obj.filter_data([self.test_data[4]], filter_individual_items=True)
        # 确保可以处理异常值且不崩溃
        self.assertTrue(len(results) >= 0, "应该能处理包含异常值的数据")

    def test_complex_invalid_conditions(self):
        """测试复杂但无效的条件组合"""
        # 复杂但语法错误的条件
        with self.assertRaises(ValueError):
            ConditionFilter("(item_count > 2)) AND ((confidence >= 0.8)")
        
        # 逻辑运算符使用错误
        with self.assertRaises(ValueError):
            ConditionFilter("item_count > 2 AND AND confidence >= 0.8")
        
        # 混合有效和无效的关键词
        with self.assertRaises(ValueError):
            ConditionFilter("item_count > 2 AND unknown_keyword < 5")

    def test_malformed_in_not_in_expressions(self):
        """测试格式错误的in/not in表达式"""
        # in表达式缺少列表
        with self.assertRaises(ValueError):
            ConditionFilter("name in")
        
        # in表达式列表格式错误
        with self.assertRaises(ValueError):
            ConditionFilter("name in ['主人物', '亚基矿'")
        
        # not in表达式缺少列表
        with self.assertRaises(ValueError):
            ConditionFilter("name not in")
        
        # in表达式错误的列表语法
        with self.assertRaises(ValueError):
            ConditionFilter("name in {'主人物', '亚基矿'}")

    def test_extreme_parentheses_nesting(self):
        """测试极端的括号嵌套"""
        # 测试深度嵌套但合法的表达式
        try:
            condition = """
                ((((((item_count > 2)) AND ((((confidence >= 0.8))))))) OR
                ((((name in ['主人物'] AND ((confidence < 0.9))))))
            """
            filter_obj = ConditionFilter(condition)
            results = filter_obj.filter_data(self.test_data)
            self.assertTrue(True, "深度嵌套但合法的表达式应该能正常处理")
        except ValueError:
            self.fail("深度嵌套但合法的表达式抛出了异常")
        
        # 测试过深的嵌套（可能导致解析问题）
        extremely_nested = "(" * 50 + "item_count > 2" + ")" * 50
        with self.assertRaises(ValueError):
            ConditionFilter(extremely_nested)

    def test_filter_individual_items_edge_cases(self):
        """测试filter_individual_items参数的边界情况"""
        # 空列表中的单项过滤
        filter_obj = ConditionFilter("confidence >= 0.8")
        results = filter_obj.filter_data([[], []], filter_individual_items=True)
        self.assertEqual(len(results), 0, "空列表进行单项过滤应返回空结果")
        
        # 混合空和非空列表
        test_data = [[], self.test_data[0], []]
        filter_obj = ConditionFilter("confidence >= 0.8")
        results = filter_obj.filter_data(test_data, filter_individual_items=True)
        self.assertTrue(len(results) > 0, "混合空和非空列表应该只过滤非空列表")

    def test_invalid_condition_function_creation(self):
        """测试无效条件函数创建的处理"""
        # 模拟条件函数创建失败的情况
        original_create_func = ConditionFilter._create_safe_condition_function
        
        def mock_create_func(self, processed_condition):
            raise ValueError("模拟函数创建失败")
        
        # 替换方法
        ConditionFilter._create_safe_condition_function = mock_create_func
        
        try:
            # 应该抛出ValueError
            with self.assertRaises(ValueError):
                ConditionFilter("item_count > 2")
        finally:
            # 恢复原始方法
            ConditionFilter._create_safe_condition_function = original_create_func


if __name__ == "__main__":
    unittest.main() 