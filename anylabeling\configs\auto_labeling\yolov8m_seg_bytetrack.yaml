type: yolov8_seg_track
name: yolov8m_seg_bytetrack-r20240816
provider: Ultralytics
display_name: YOLOv8m-Seg-Bytetrack
model_path: https://github.com/CVHub520/X-AnyLabeling/releases/download/v0.1.0/yolov8m-seg.onnx
iou_threshold: 0.5
conf_threshold: 0.1
# show_boxes: True
tracker:
  # Base settings
  tracker_type: bytetrack # tracker type, ['botsort', 'bytetrack']
  track_high_thresh: 0.5 # threshold for the first association
  track_low_thresh: 0.1 # threshold for the second association
  new_track_thresh: 0.6 # threshold for init new track if the detection does not match any tracks
  track_buffer: 30 # buffer to calculate the time when to remove tracks
  match_thresh: 0.8 # threshold for matching tracks
  fuse_score: True
filter_classes:
  - person
  - car
classes:
  - person
  - bicycle
  - car
  - motorcycle
  - airplane
  - bus
  - train
  - truck
  - boat
  - traffic light
  - fire hydrant
  - stop sign
  - parking meter
  - bench
  - bird
  - cat
  - dog
  - horse
  - sheep
  - cow
  - elephant
  - bear
  - zebra
  - giraffe
  - backpack
  - umbrella
  - handbag
  - tie
  - suitcase
  - frisbee
  - skis
  - snowboard
  - sports ball
  - kite
  - baseball bat
  - baseball glove
  - skateboard
  - surfboard
  - tennis racket
  - bottle
  - wine glass
  - cup
  - fork
  - knife
  - spoon
  - bowl
  - banana
  - apple
  - sandwich
  - orange
  - broccoli
  - carrot
  - hot dog
  - pizza
  - donut
  - cake
  - chair
  - couch
  - potted plant
  - bed
  - dining table
  - toilet
  - tv
  - laptop
  - mouse
  - remote
  - keyboard
  - cell phone
  - microwave
  - oven
  - toaster
  - sink
  - refrigerator
  - book
  - clock
  - vase
  - scissors
  - teddy bear
  - hair drier
  - toothbrush
