from __future__ import annotations

import os
import random
import sys
from typing import Dict, Any, List, Optional, Tuple

import cv2
import numpy as np
from global_tools.utils import ClassInstanceManager
from global_tools.utils import Logger, Colors

import albumentations as A
import anylabeling.customize_ui.helper.const as CONST
from anylabeling.customize_ui.services.image_enhancement.helper.helper import VisualizationHelper

logger: Logger = ClassInstanceManager.get_instance( key=CONST.X_LABEL_LOG )  # type: ignore
if not logger:
	logger = Logger()


class AugmentationPipeline:
	"""
	一个强大且统一的数据增强管道，整合了多种变换策略。

	该类旨在提供一个单一、易于使用的接口，用于应用复杂的图像增强序列。
	它封装了四种主要的增强类别：颜色与光照、天气与环境、成像伪影与质量、以及几何变换。

	核心设计原则:
	1.  **统一入口**: 所有增强操作都通过唯一的 `apply` 方法调用。
	2.  **顺序保证**: 严格确保非几何变换（颜色、天气等）先于几何变换执行，
		这可以防止在缩放或裁剪后产生不真实的像素级伪影。
	3.  **精细控制**: 通过 `enabled_strategies` 参数，用户可以精确地启用或禁用
		任何一个增强类别，甚至可以控制到类别下的单个API。
	4.  **健壮性**: 继承了原 `geometric_transforms` 中的重试逻辑，确保在多次
		尝试后，变换结果中的标注点不会超出图像边界，从而保证标注数据的有效性。
	5.  **易于配置**: 通过全局的 `config` 字典，可以对任何一个具体的 `albumentations`
		API 的参数进行覆盖和微调。
	6.  **统一的几何变换架构**: 使用一致的实现方式提供完整的几何变换能力：
		基础变换：RandomScale（随机缩放）、Translate（百分比平移，Affine实现）、Rotate（旋转，Affine实现）、
		Shear（剪切，Affine实现）、HorizontalFlip（水平翻转）、VerticalFlip（垂直翻转）、Transpose（转置）、
		D4（对称变换）；高级扭曲：OpticalDistortion（光学扭曲）、GridDistortion（网格扭曲）、PiecewiseAffine（分段仿射）、
		ThinPlateSpline（薄板样条）；裁剪变换：RandomResizedCrop（随机裁剪缩放）。统一使用Affine实现基础变换，避免功能重复，提供一致性和精细控制。

	使用流程:
	1. 实例化 `pipeline = AugmentationPipeline(config=...)`。
	2. 调用 `transformed_image, ..., ... = pipeline.apply(...)`。
	"""

	def __init__( self, config: Optional[ Dict[ str, Any ] ] = None, priority_apis: Optional[ List[ str ] ] = None ):
		"""
		初始化增强管道。

		参数:
		- config (dict, optional): 一个全局配置字典，用于覆盖任何变换的默认参数。
		  键是变换API的类名（驼峰式，如 'RandomBrightnessContrast'），值是参数字典。
		  例如: `{"RandomBrightnessContrast": {"brightness_limit": 0.3}}`
		- priority_apis (list, optional): 一个API名称的列表，用于限制可使用的API:
		  变换限制：只有列表中的API会被使用，未指定的API将被禁用
		  
		  例如: `['GaussianBlur', 'RandomBrightnessContrast']` 表示只使用这两个API进行变换。
		"""
		logger.info( "初始化增强管道...", color=Colors.CYAN )
		self.config = config if config is not None else { }
		self.__priority_apis = priority_apis if priority_apis is not None else [ ]
		if self.config:
			logger.debug( f"管道已使用自定义配置初始化: {self.config}" )
		self._initialize_strategies()

	@staticmethod
	def is_obb_format( polygon: List ) -> bool:
		"""
		检查多边形是否为YOLO11 OBB格式（恰好有4个点的多边形）。
		
		参数:
		- polygon (List): 多边形坐标列表，格式为 [[x1,y1],[x2,y2],[x3,y3],[x4,y4]]
		
		返回:
		- bool: 如果是OBB格式则返回True，否则返回False
		"""
		return len( polygon ) == 4 and all( len( point ) == 2 for point in polygon )

	@staticmethod
	def validate_obb( polygon: List ) -> bool:
		"""
		验证OBB格式的有效性。
		目前简单检查是否有4个点，每个点是否有2个坐标。
		
		参数:
		- polygon (List): 多边形坐标列表，格式为 [[x1,y1],[x2,y2],[x3,y3],[x4,y4]]
		
		返回:
		- bool: 如果OBB格式有效则返回True，否则返回False
		"""
		if not AugmentationPipeline.is_obb_format( polygon ):
			return False

		# 所有坐标值都应该是数值型
		return all( isinstance( coord, (int, float) ) for point in polygon for coord in point )

	@staticmethod
	def ensure_obb_order( polygon: List ) -> List:
		"""
		确保OBB的点按照顺时针或逆时针顺序排列。
		这对于保持OBB的几何变换后的一致性很重要。
		
		参数:
		- polygon (List): OBB格式的多边形，可能点的顺序已经被打乱
		
		返回:
		- List: 重新排序后的OBB多边形
		"""
		if not AugmentationPipeline.is_obb_format( polygon ):
			return polygon

		# 计算中心点
		center_x = sum( p[ 0 ] for p in polygon ) / 4
		center_y = sum( p[ 1 ] for p in polygon ) / 4

		# 计算每个点相对于中心点的角度
		angles = [ ]
		for point in polygon:
			dx = point[ 0 ] - center_x
			dy = point[ 1 ] - center_y
			angle = np.arctan2( dy, dx )
			angles.append( angle )

		# 根据角度排序
		sorted_points = [ p for _, p in sorted( zip( angles, polygon ), key=lambda pair: pair[ 0 ] ) ]

		return sorted_points

	def _initialize_strategies( self ):
		"""
		初始化所有可用的增强策略和它们的默认参数。
		这使得代码更清晰，并集中管理所有可用的API。
		"""
		self.strategies = {
			'color':     {
				'description':   "颜色、光照和模糊变换。从此类别中随机选择2到5个应用。",
				'type':          'one_of',  # 支持 'one_of'(随机选一个), 'some_of'(随机选N个), 'sequential'(按顺序应用全部)
				'p':             0.9,
				'some_of_range': (1, 3),  # 当type为'some_of'时，随机选择的变换数量范围
				'apis':          {
					'RandomBrightnessContrast': {  # 随机调整亮度和对比度
						'brightness_limit': 0.1,  # 亮度变化范围
						'contrast_limit':   0.1  # 对比度变化范围
					},
					'HueSaturationValue':       {  # 随机调整色调、饱和度、明度
						'hue_shift_limit': (-15, 30),  # 色调偏移范围
						'sat_shift_limit': (-15, 45),  # 饱和度偏移范围
						'val_shift_limit': (-15, 20)  # 明度偏移范围
					},
					'GaussianBlur':             { 'blur_limit': (3, 7) },  # 高斯模糊，模拟失焦
					'MedianBlur':               { 'blur_limit': (3, 7) },  # 中值模糊，能有效去除椒盐噪声
					'MotionBlur':               { 'blur_limit': (3, 7) },  # 运动模糊，模拟相机或物体移动
					'GaussNoise':               {  # 向图像添加高斯分布的噪声
						'std_range':  (0.01, 0.025),  # 噪声标准差的范围
						'mean_range': (0.0, 0.0)  # 噪声均值的范围
					},
					'CLAHE':                    {  # 对比度受限的自适应直方图均衡化，增强局部对比度
						'clip_limit':     (1, 3),  # 对比度限制
						'tile_grid_size': (8, 8)  # 计算直方图的网格大小
					},
					'RandomGamma':              { 'gamma_limit': (95, 105) },  # Gamma校正，非线性亮度调整
					'ChannelShuffle':           { }  # 随机交换R,G,B通道，一种较强的颜色增强
				}
			},
			'weather':   {
				'description':   "模拟天气与环境效果。从此类别中随机选择一个应用。",
				'type':          'one_of',  # 支持 'one_of'(随机选一个), 'some_of'(随机选N个), 'sequential'(按顺序应用全部)
				'p':             0.25,
				'some_of_range': (1, 2),  # 当type为'some_of'时，随机选择的变换数量范围
				'apis':          {
					'RandomRain':     {  # 模拟下雨效果
						'slant_range':            (-10, 10),  # 雨滴倾斜角度
						'drop_length':            20,  # 雨滴长度
						'drop_width':             1,  # 雨滴宽度
						'drop_color':             (200, 200, 200),  # 雨滴颜色
						'blur_value':             5,  # 雨滴模糊程度
						'brightness_coefficient': 0.8,  # 应用效果后图像的亮度系数
						'rain_type':              'drizzle'  # 雨的类型，'drizzle'表示细雨
					},
					'RandomSnow':     {  # 模拟下雪效果
						'snow_point_range': (0.1, 0.4),  # 雪花生成的亮度阈值
						'brightness_coeff': 2.0  # 雪花亮度
					},
					'RandomFog':      {  # 模拟大雾效果
						'fog_coef_range': (0.2, 0.5),  # 雾的浓度
						'alpha_coef':     0.08  # 雾的透明度
					},
					'RandomSunFlare': {  # 模拟太阳耀斑
						'flare_roi':               (0, 0, 1, 0.5),  # 耀斑出现的感兴趣区域
						'angle_range':             (0.0, 1.0),  # 耀斑线的角度
						'num_flare_circles_range': (4, 6),  # 光圈数量
						'src_radius':              250,  # 耀斑源的半径
						'src_color':               (255, 255, 255)  # 太阳颜色
					}
				}
			},
			'artifact':  {
				'description':   "模拟数字成像伪影与质量下降。从此类别中随机选择一个应用。",
				'type':          'one_of',  # 支持 'one_of'(随机选一个), 'some_of'(随机选N个), 'sequential'(按顺序应用全部)
				'p':             0.5,
				'some_of_range': (1, 2),  # 当type为'some_of'时，随机选择的变换数量范围
				'apis':          {
					'ImageCompression': {  # 模拟JPEG或WebP等有损压缩
						'quality_range':    (60, 95),  # 压缩质量，值越低效果越明显
						'compression_type': 'jpeg'  # 压缩类型
					},
					'ISONoise':         {  # 模拟相机高ISO设置产生的传感器噪声
						'color_shift': (0.01, 0.05),  # 颜色偏移
						'intensity':   (0.1, 0.3)  # 噪声强度
					}
				}
			},
			'geometric': {
				'description':   "几何变换，如翻转、仿射、透视等。支持多种组合方式。",
				'type':          'one_of',  # 支持 'one_of'(随机选一个), 'some_of'(随机选N个), 'sequential'(按顺序应用全部)
				'p':             1.0,  # 这个p表示应用几何变换的整体概率
				# 'probabilities' 字典定义了当 'type' 为 'one_of' 时，各个几何变换被选中的相对权重。
				# 这些值不直接用作概率，而是在 _build_transforms 方法中被归一化。
				# 例如，'Affine' 的权重是5，而其他的是1，这意味着在一次随机选择中，
				# 'Affine' 被选中的概率大约是其他任何一个变换的5倍。
				# 总权重 = 5 + 1 + 1 + 1 + 1 = 9。
				# 'Affine' 的概率 ≈ 5/9，'RandomRotate90' 的概率 ≈ 1/9，以此类推。
				'probabilities': {
					'RandomScale':       1,  # 随机缩放，平均权重
					'Translate':         1,  # 平移变换，平均权重
					'Rotate':            1,  # 旋转变换，平均权重
					'Shear':             1,  # 剪切变换，平均权重
					'HorizontalFlip':    1,  # 水平翻转，平均权重
					'OpticalDistortion': 1,  # 光学扭曲，平均权重
					'GridDistortion':    1,  # 网格扭曲，平均权重
					'VerticalFlip':      1,  # 垂直翻转，平均权重
					'Transpose':         1,  # 转置变换，平均权重
					'D4':                1,  # D4对称变换，平均权重
					'PiecewiseAffine':   1,  # 分段仿射变换，平均权重
					'ThinPlateSpline':   1,  # 薄板样条变换，平均权重
					'Perspective':       1,  # 透视变换，平均权重
					'ElasticTransform':  1,  # 弹性变换，平均权重
					'RandomResizedCrop': 1,  # 随机裁剪缩放，平均权重
				},
				'some_of_range': (1, 3),  # 当type为'some_of'时，随机选择的变换数量范围
				'apis':          {
					'Translate':         {
						'translate_percent':  { 'x': (-0.3, 0.3), 'y': (-0.3, 0.3) },  # 百分比平移±10%
						'scale':              1.0,  # 不缩放，保持原始大小
						'rotate':             0,  # 不旋转，纯平移变换
						'shear':              0,  # 不剪切，纯平移变换
						'interpolation':      1,  # cv2.INTER_LINEAR，线性插值
						'mask_interpolation': 0,  # 对掩码使用最近邻插值
						'border_mode':        0,  # cv2.BORDER_CONSTANT，常量填充
						'fill':               0,  # 填充值为0（黑色）
						'fill_mask':          0,  # 掩码填充值为0
						'fit_output':         False,  # 不调整输出大小
						'p':                  1.0  # 如果被选中则100%应用
					},
					# Translate: 百分比平移变换，基于图像尺寸的相对平移，适合不同分辨率的图像

					'RandomScale':       {
						'scale_limit':        0.4,  # 缩放范围±40%，合理的大小变化
						'interpolation':      1,  # cv2.INTER_LINEAR，线性插值
						'mask_interpolation': 0,  # 对掩码使用最近邻插值
						'p':                  1.0  # 如果被选中则100%应用
					},
					# RandomScale: 纯缩放变换，模拟目标在不同距离下的大小变化，是最常用的几何增强之一
					'Rotate':            {
						'rotate':             (-80, 80),  # 旋转角度范围±30度
						'scale':              1.0,  # 不缩放，保持原始大小
						'translate_percent':  None,  # 不平移，纯旋转变换
						'shear':              0,  # 不剪切，纯旋转变换
						'interpolation':      1,  # cv2.INTER_LINEAR，线性插值
						'mask_interpolation': 0,  # 对掩码使用最近邻插值
						'border_mode':        0,  # cv2.BORDER_CONSTANT，常量填充
						'fill':               0,  # 填充值为0（黑色）
						'fill_mask':          0,  # 掩码填充值为0
						'fit_output':         False,  # 不调整输出大小
						'p':                  1.0  # 如果被选中则100%应用
					},
					# Rotate: 统一旋转变换，使用Affine实现纯旋转，模拟相机或目标的旋转角度变化，增强对旋转不变性的学习
					'Shear':             {
						'shear':              (-20, 20),  # 剪切角度范围±20度，适中的剪切强度
						'scale':              1.0,  # 不缩放，保持原始大小
						'translate_percent':  None,  # 不平移，纯剪切变换
						'rotate':             0,  # 不旋转，纯剪切变换
						'interpolation':      1,  # cv2.INTER_LINEAR，线性插值
						'mask_interpolation': 0,  # 对掩码使用最近邻插值
						'border_mode':        0,  # cv2.BORDER_CONSTANT，常量填充
						'fill':               0,  # 填充值为0（黑色）
						'fill_mask':          0,  # 掩码填充值为0
						'fit_output':         False,  # 不调整输出大小
						'p':                  1.0  # 如果被选中则100%应用
					},
					# Shear: 剪切变换，使用Affine实现纯剪切，模拟透视和倾斜效果，增强对几何变形的鲁棒性
					'HorizontalFlip':    {
						'p': 1.0  # 如果被选中则100%应用
					},
					# HorizontalFlip: 水平翻转变换，模拟镜像效果，是最简单有效的几何增强之一
					'VerticalFlip':      {
						'p': 1.0  # 如果被选中则100%应用
					},
					# VerticalFlip: 垂直翻转变换，模拟上下颠倒效果，适合某些特定场景
					'Transpose':         {
						'p': 1.0  # 如果被选中则100%应用
					},
					# Transpose: 转置变换，交换图像的宽高维度，创造独特的几何变化
					'OpticalDistortion': {
						'distort_limit':      0.3,  # 扭曲强度±30%，适中的光学畸变
						'interpolation':      1,  # cv2.INTER_LINEAR，线性插值
						'border_mode':        0,  # cv2.BORDER_CONSTANT，常量填充
						'mask_interpolation': 0,  # 对掩码使用最近邻插值
						'fill':               0,  # 填充值为0（黑色）
						'fill_mask':          0,  # 掩码填充值为0
						'p':                  1.0  # 如果被选中则100%应用
					},
					# OpticalDistortion: 光学扭曲变换，模拟镜头畸变效果，增强对光学失真的鲁棒性
					'GridDistortion':    {
						'num_steps':          5,  # 网格步数，控制扭曲的细致程度
						'distort_limit':      0.3,  # 扭曲强度±30%，适中的网格变形
						'interpolation':      1,  # cv2.INTER_LINEAR，线性插值
						'border_mode':        0,  # cv2.BORDER_CONSTANT，常量填充
						'mask_interpolation': 0,  # 对掩码使用最近邻插值
						'fill':               0,  # 填充值为0（黑色）
						'fill_mask':          0,  # 掩码填充值为0
						'p':                  1.0  # 如果被选中则100%应用
					},
					# GridDistortion: 网格扭曲变换，创建局部几何变形，模拟非均匀的空间扭曲
					'D4':                {
						'p': 1.0  # 如果被选中则100%应用
					},
					# D4: D4对称变换，应用8种可能的对称变换（旋转+翻转组合），增强几何不变性
					'PiecewiseAffine':   {
						'scale':              0.03,  # 变形强度3%，轻微的局部扭曲
						'nb_rows':            4,  # 网格行数，控制变形的分辨率
						'nb_cols':            4,  # 网格列数，控制变形的分辨率
						'interpolation':      1,  # cv2.INTER_LINEAR，线性插值
						'mask_interpolation': 0,  # 对掩码使用最近邻插值
						'p':                  1.0  # 如果被选中则100%应用
					},
					# PiecewiseAffine: 分段仿射变换，创建局部的非线性扭曲，模拟复杂的几何变形
					'ThinPlateSpline':   {
						'interpolation':      1,  # cv2.INTER_LINEAR，线性插值
						'mask_interpolation': 0,  # 对掩码使用最近邻插值
						'p':                  1.0  # 如果被选中则100%应用
					},
					# ThinPlateSpline: 薄板样条变换，创建平滑的非刚性变形，模拟自然的形变效果
					'Perspective':       {
						'scale':      (0.01, 0.0525),  # 强度较小，产生轻微的3D透视效果
						'fit_output': False,
						'p':          1.0  # 如果被选中则100%应用
					},
					# Perspective: 透视变换，通过扭曲图像角点模拟3D空间中的视角变化，增强对倾斜视角拍摄目标的识别能力
					'ElasticTransform':  {
						'alpha': 1,  # 形变强度
						'sigma': 25,  # 形变平滑度
						'p':     1.0  # 如果被选中则100%应用
					},
					# ElasticTransform: 弹性变换，产生局部非线性扭曲，模拟物体形变或镜头畸变，特别适用于增强对变形目标的识别能力
					'RandomResizedCrop': {
						'size':  (640, 640),  # 目标输出尺寸，应与模型输入匹配
						'scale': (0.5, 1.0),  # 裁剪面积为原图的50%-100%，学习不同尺度的特征
						'ratio': (0.75, 1.33)  # 限制裁剪框的宽高比，避免极端形变
					},
					# RandomResizedCrop: 随机裁剪并调整大小，模拟对目标的局部关注和不同尺度观察，增强模型对目标大小和位置变化的鲁棒性
				}
			}
		}

		# 注意：priority_apis 现在仅用于限制可用的API，不再对API进行排序
		if self.__priority_apis:
			logger.info( f"已设置API限制: {self.__priority_apis}", color=Colors.CYAN )

		# 将 albumentations 的类映射到字符串名称
		self.api_map = {
			'RandomBrightnessContrast': A.RandomBrightnessContrast,
			'HueSaturationValue':       A.HueSaturationValue,
			'GaussianBlur':             A.GaussianBlur,
			'MedianBlur':               A.MedianBlur,
			'MotionBlur':               A.MotionBlur,
			'GaussNoise':               A.GaussNoise,
			'CLAHE':                    A.CLAHE,
			'RandomGamma':              A.RandomGamma,
			'ChannelShuffle':           A.ChannelShuffle,
			'RandomRain':               A.RandomRain,
			'RandomSnow':               A.RandomSnow,
			'RandomFog':                A.RandomFog,
			'RandomSunFlare':           A.RandomSunFlare,
			'ImageCompression':         A.ImageCompression,
			'ISONoise':                 A.ISONoise,
			'RandomScale':              A.RandomScale,  # 随机缩放变换
			'Translate':                A.Affine,  # 百分比平移变换（使用Affine实现）
			'Rotate':                   A.Affine,  # 旋转变换（使用Affine实现）
			'Shear':                    A.Affine,  # 剪切变换（使用Affine实现）
			'HorizontalFlip':           A.HorizontalFlip,  # 水平翻转变换
			'VerticalFlip':             A.VerticalFlip,  # 垂直翻转变换
			'Transpose':                A.Transpose,  # 转置变换
			'OpticalDistortion':        A.OpticalDistortion,  # 光学扭曲变换
			'GridDistortion':           A.GridDistortion,  # 网格扭曲变换
			'D4':                       A.D4,  # D4对称变换
			'PiecewiseAffine':          A.PiecewiseAffine,  # 分段仿射变换
			'ThinPlateSpline':          A.ThinPlateSpline,  # 薄板样条变换
			'Perspective':              A.Perspective,
			'ElasticTransform':         A.ElasticTransform,
			'RandomResizedCrop':        A.RandomResizedCrop
		}

		# 创建一个从API名称到类别名称的反向映射，以便于日志记录
		self.__api_to_category_map = { }
		for category, details in self.strategies.items():
			if 'apis' in details:
				for api_name in details[ 'apis' ]:
					self.__api_to_category_map[ api_name ] = category

	def _build_transforms( self, enabled_strategies: Dict[ str, Any ] ) -> List[ A.BasicTransform ]:
		"""
		根据启用的策略动态构建一个完整的变换列表（非Compose对象）。
		这个方法是新架构的核心，它将所有逻辑整合在一起。
		"""
		logger.debug( "基于启用的策略构建变换..." )
		all_transforms = [ ]

		# --- 1. 构建非几何变换 ---
		non_geometric_transforms = [ ]
		for cat in [ 'color', 'weather', 'artifact' ]:
			if not enabled_strategies.get( cat, False ):
				continue

			strategy_info = self.strategies[ cat ]
			enabled_apis = enabled_strategies.get( cat )

			# 如果值为True，表示启用该分类下所有API；如果是列表，则只启用指定的API
			apis_to_build = strategy_info[ 'apis' ].keys()
			if isinstance( enabled_apis, list ):
				apis_to_build = [ api for api in enabled_apis if api in strategy_info[ 'apis' ] ]

			transform_pool = [ ]
			for api_name in apis_to_build:
				default_params = strategy_info[ 'apis' ][ api_name ]
				user_params = self.config.get( api_name, { } )
				final_params = {
					**default_params,
					**user_params
				}

				# 特殊处理，p需要从外部控制
				if 'p' in final_params and strategy_info[ 'type' ] != 'sequential':
					final_params.pop( 'p' )

				transform_pool.append(
					self.api_map[ api_name ](
						p=1.0 if strategy_info[ 'type' ] != 'sequential' else final_params.get( 'p', 1.0 ),
						**final_params
					)
				)

			if not transform_pool:
				logger.debug( f"根据当前启用的API，分类 '{cat}'下没有可构建的变换。" )
				continue

			# 根据策略类型（OneOf, SomeOf, Sequential）封装变换池
			if strategy_info[ 'type' ] == 'one_of':
				non_geometric_transforms.append( A.OneOf( transform_pool, p=strategy_info[ 'p' ] ) )
			elif strategy_info[ 'type' ] == 'some_of':
				pool_size = len( transform_pool )
				# 从策略配置中读取选择范围
				some_of_range = strategy_info.get( 'some_of_range', (1, 3) )
				n_min = min( pool_size, some_of_range[ 0 ] )
				n_max = min( pool_size, some_of_range[ 1 ] )
				num_to_apply = random.randint( n_min, n_max ) if pool_size > 0 else 0
				if num_to_apply > 0:
					selected = random.sample( transform_pool, num_to_apply )
					# SomeOf在旧版中可能不支持，所以我们创建一个Compose来模拟
					logger.debug(
						f"为分类'{cat}'应用'some_of'策略：从{len( transform_pool )}个变换中随机选择{num_to_apply}个。"
					)
					non_geometric_transforms.append( A.Compose( selected, p=strategy_info[ 'p' ] ) )
			elif strategy_info[ 'type' ] == 'sequential':
				# 按顺序应用所有变换
				logger.debug( f"为分类'{cat}'应用'sequential'策略：按顺序添加{len( transform_pool )}个变换。" )
				for transform in transform_pool:
					non_geometric_transforms.append( transform )

		if non_geometric_transforms:
			all_transforms.append( A.Compose( non_geometric_transforms ) )

		# --- 2. 构建几何变换 ---
		geometric_transforms = [ ]
		if enabled_strategies.get( 'geometric', False ):
			strategy_info = self.strategies[ 'geometric' ].copy()  # 复制默认配置
			enabled_apis = enabled_strategies.get( 'geometric' )

			# 支持用户自定义变换类型和some_of_range参数
			if isinstance( enabled_apis, dict ):
				# 如果是字典，可以包含 type、some_of_range 和 apis 等参数
				if 'type' in enabled_apis:
					# 确保type是有效的类型：'one_of', 'some_of', 'sequential'
					if enabled_apis[ 'type' ] in [ 'one_of', 'some_of', 'sequential' ]:
						strategy_info[ 'type' ] = enabled_apis[ 'type' ]
					else:
						logger.warning(
							f"不支持的几何变换类型 '{enabled_apis[ 'type' ]}'。将使用默认类型 '{strategy_info[ 'type' ]}'。",
							color=Colors.WARNING
						)
				if 'some_of_range' in enabled_apis:
					strategy_info[ 'some_of_range' ] = enabled_apis[ 'some_of_range' ]

				# 提取apis列表
				apis_to_build = strategy_info[ 'apis' ].keys()
				if 'apis' in enabled_apis and isinstance( enabled_apis[ 'apis' ], list ):
					apis_to_build = [ api for api in enabled_apis[ 'apis' ] if api in strategy_info[ 'apis' ] ]
			else:
				# 兼容旧版用法（直接使用True或API列表）
				apis_to_build = strategy_info[ 'apis' ].keys()
				if isinstance( enabled_apis, list ):
					apis_to_build = [ api for api in enabled_apis if api in strategy_info[ 'apis' ] ]

			# 构建几何变换池
			transform_pool = [ ]
			strategy_type = strategy_info.get( 'type' )
			probabilities_config = strategy_info.get( 'probabilities' )
			logger.debug( f"开始构建几何变换，类型: '{strategy_type}'。" )

			# --- 权重归一化逻辑 ---
			# albumentations要求每个变换的p值(概率)必须在[0, 1]区间内。
			# 为了实现加权随机选择(如让Affine有更高概率被选中)，我们不能直接使用权重(如5, 1)。
			normalized_probs = { }
			if strategy_type == 'one_of' and probabilities_config and apis_to_build:
				# 1. 提取当前启用的API的权重
				active_weights = {
					api: probabilities_config.get( api, 1 )
					for api in apis_to_build
				}
				# 2. 计算总权重
				total_weight = sum( active_weights.values() )
				# 3. 计算每个API的归一化概率 (weight / total_weight)
				if total_weight > 0:
					normalized_probs = {
						api: weight / total_weight
						for api, weight in active_weights.items()
					}
					logger.debug( f"几何变换'one_of'策略的归一化概率: {normalized_probs}" )

			# 遍历并构建每个具体的变换实例
			for api_name in apis_to_build:
				default_params = strategy_info[ 'apis' ][ api_name ]
				user_params = self.config.get( api_name, { } )
				final_params = {
					**default_params,
					**user_params
				}

				# --- 动态确定p值 ---
				p_value = 1.0  # 为所有情况设置一个安全的默认值

				if strategy_type == 'one_of':
					# 对于'one_of'，我们使用上面计算出的归一化概率。
					# A.OneOf会根据这些p值进行加权随机抽样。
					# 如果未提供权重配置，则所有p值默认为1.0，A.OneOf会进行均等概率抽样。
					p_value = normalized_probs.get( api_name, 1.0 )
				else:
					# 对于'sequential'或'some_of'，变换的p值由其自身定义决定，
					# 因为它们是在一个Compose或SomeOf容器的外部p值控制下执行的。
					p_value = final_params.get( 'p', 1.0 )

				# 清理：从final_params中移除'p'，因为它已经被我们手动处理并作为独立参数传递。
				# 这可以防止p参数被重复传递或错误地传递给不支持它的构造函数。
				if 'p' in final_params:
					final_params.pop( 'p' )

				transform_pool.append( self.api_map[ api_name ]( p=p_value, **final_params ) )

			if transform_pool:
				# 根据策略类型组织几何变换
				if strategy_info[ 'type' ] == 'one_of':
					# 从所有变换中随机选择一个
					logger.debug( f"为几何变换应用'one_of'策略：从{len( transform_pool )}个变换中随机选择1个。" )
					geometric_transforms.append( A.OneOf( transform_pool, p=strategy_info[ 'p' ] ) )
				elif strategy_info[ 'type' ] == 'some_of':
					# 随机选择N个变换，N由some_of_range参数决定
					pool_size = len( transform_pool )
					# 从策略配置中读取选择范围
					some_of_range = strategy_info.get( 'some_of_range', (1, 3) )
					n_min = min( pool_size, some_of_range[ 0 ] )
					n_max = min( pool_size, some_of_range[ 1 ] )
					num_to_apply = random.randint( n_min, n_max ) if pool_size > 0 else 0
					if num_to_apply > 0:
						selected = random.sample( transform_pool, num_to_apply )
						logger.debug(
							f"为几何变换应用'some_of'策略：从{len( transform_pool )}个变换中随机选择{num_to_apply}个。"
						)
						geometric_transforms.append( A.Compose( selected, p=strategy_info[ 'p' ] ) )
				else:  # 默认为 sequential
					# 按顺序应用所有变换
					logger.debug( f"为几何变换应用'sequential'策略：按顺序添加{len( transform_pool )}个变换。" )
					geometric_transforms.extend( transform_pool )

		if geometric_transforms:
			all_transforms.extend( geometric_transforms )

		logger.info( f"总共构建了 {len( all_transforms )} 个变换组。", color=Colors.SUCCESS )
		return all_transforms

	def __parse_replay_data( self, replay_transforms: List[ Dict[ str, Any ] ] ) -> List[ str ]:
		"""
		私有辅助方法：递归地解析ReplayCompose返回的数据，提取应用的叶子变换名称。
		"""
		applied_names = [ ]
		for transform_info in replay_transforms:
			# 我们只关心那些被实际应用的变换
			if not transform_info.get( 'applied', False ):
				continue

			# 判断它是否是一个容器（即，它内部是否还包含其他变换）
			is_container = 'transforms' in transform_info and transform_info[ 'transforms' ]

			if is_container:
				# 如果是容器，我们不记录容器本身的名字，而是递归地去寻找其内部被应用的叶子变换
				child_names = self.__parse_replay_data( transform_info[ 'transforms' ] )
				applied_names.extend( child_names )
			else:
				# 如果它不是容器并且被应用了，那么它就是一个叶子变换，我们记录下它的名字
				class_fullname = transform_info.get( '__class_fullname__' )
				if class_fullname:
					class_name = class_fullname.split( '.' )[ -1 ]
					applied_names.append( class_name )
		return applied_names

	def _is_image_changed( self, original_image: np.ndarray, transformed_image: np.ndarray ) -> bool:
		"""
		检查变换后的图像是否与原始图像的像素数据不同。

		该方法使用 `np.array_equal`，这是用于精确比较两个NumPy数组是否完全
		相同的最优算法。它速度极快，因为它在底层由C语言实现，并且只要找到
		第一个不匹配的元素就会立即返回，无需完整遍历。

		对于确保数据增强管道确实改变了图像数据的任务，精确的字节级比较是
		必须的，因此我们不使用结构相似性(SSIM)或感知哈希(pHash)等算法，
		因为它们可能会忽略细微但有效的像素级变换。

		参数:
		- original_image (np.ndarray): 原始图像。
		- transformed_image (np.ndarray): 变换后的图像。

		返回:
		- bool: 如果图像已改变，则返回 True，否则返回 False。
		"""
		return not np.array_equal( original_image, transformed_image )

	def apply(
			self,
			image_path: str,
			polygons_nested: List[ List[ Any ] ],
			labels_name: List[ str ],
			enabled_strategies: Optional[ Dict[ str, Any ] ] = None,
			save_image: bool = False,
			save_dir: Optional[ str ] = None
	) -> Tuple[ np.ndarray, List[ List[ List[ float ] ] ], List[ str ] ]:
		"""
		应用完整的增强管道。

		参数:
		- image_path (str): 待处理图像的完整路径。
		- polygons_nested (List): 多边形顶点坐标列表。
		  该参数支持三种格式:
		  1. 嵌套列表: `[[[x1,y1],[x2,y2],...], ...]`
		  2. 扁平列表: `[[x1,y1,x2,y2,...], ...]`
		  3. YOLO11 OBB: `[[[x1,y1],[x2,y2],[x3,y3],[x4,y4]], ...]`，恰好有4个点的多边形
		- labels_name (List[str]): 多边形对应的类别标签列表。
		- enabled_strategies (dict, optional): 控制启用哪些增强策略。
		  这是一个字典，用于精确控制四个增强类别 ('color', 'weather', 'artifact', 'geometric')
		  的行为。

		  默认行为:
		  如果此参数为 `None` (默认值)，则所有四个类别都将被启用，并使用它们各自
		  类别下的所有可用API。

		  字典结构和值含义:
		  - 键 (str): 必须是 'color', 'weather', 'artifact', 'geometric' 中的一个。
		  - 值 (bool or List[str]):
			- `True`: 启用该类别，并使用其下定义的所有API。
			- `False` (或字典中不包含该键): 完全禁用该类别。
			- `List[str]`: 启用该类别，但只使用列表中指定的API。API名称必须是
			  `albumentations` 变换的驼峰式类名字符串。所有可用的值如下:
			  - `for 'color'`: [
					'RandomBrightnessContrast', 'HueSaturationValue', 'GaussianBlur',
					'MedianBlur', 'MotionBlur', 'GaussNoise', 'CLAHE',
					'RandomGamma', 'ChannelShuffle'
				]
			  - `for 'weather'`: [
					'RandomRain', 'RandomSnow', 'RandomFog', 'RandomSunFlare'
				]
			  - `for 'artifact'`: [
					'ImageCompression', 'ISONoise'
				]
			  - `for 'geometric'`: [
					'RandomScale', 'Translate', 'Rotate', 'Shear', 'HorizontalFlip', 'VerticalFlip', 'Transpose',
					'OpticalDistortion', 'GridDistortion', 'D4', 'PiecewiseAffine', 'ThinPlateSpline',
					'Perspective', 'ElasticTransform', 'RandomResizedCrop'
				]

		  完整结构示例:
		  {
			  'color': True,
			  # 效果: 启用 'color' 类别，并从其9个API中随机选择2-5个应用。

			  'weather': ['RandomRain', 'RandomFog'],
			  # 效果: 启用 'weather' 类别，但只在 'RandomRain' 和 'RandomFog'
			  #       之间随机二选一应用。

			  'artifact': False,
			  # 效果: 完全禁用 'artifact' (伪影) 类别。

			  'color': {
				  'type': 'sequential',  # 按顺序应用所有API，而不是随机选择
				  'apis': ['RandomBrightnessContrast', 'HueSaturationValue']  # 可选，指定使用的API
			  },
			  # 效果: 启用 'color' 类别，并按顺序应用指定的API。

			  'weather': {
				  'type': 'some_of',
				  'some_of_range': (1, 2),  # 从API列表中随机选择1-2个应用
				  'apis': ['RandomRain', 'RandomFog', 'RandomSnow']
			  },
			  # 效果: 启用 'weather' 类别，并从指定的API中随机选择1-2个应用。

			  'geometric': {
				  'type': 'some_of',  # 可选值: 'one_of', 'some_of', 'sequential'
				  'some_of_range': (2, 4),  # 当type为'some_of'时，随机选择2-4个变换
				  'apis': ['RandomScale', 'Translate', 'Rotate', 'Shear', 'HorizontalFlip', 'OpticalDistortion', 'RandomResizedCrop']  # 可选，指定使用的API
			  }
			  # 效果: 启用 'geometric' 类别，并从指定的API中随机选择2-4个应用。
			  # 注意: 使用统一的Affine实现基础变换（平移、旋转、剪切），结合原生独立API（缩放、翻转、裁剪缩放）和高级扭曲变换，提供一致性和精细控制。
		  }
		- save_image (bool, optional): 是否保存变换后的图像。默认为False。
		- save_dir (str, optional): 保存图像的目录路径。如果为None，则使用原图像所在目录。

		返回:
		- Tuple[np.ndarray, List[List[List[float]]], List[str]]:
		  一个元组，包含变换后的图像以及同步更新后的多边形和标签。
		"""
		logger.info( f"开始对图像进行增强: '{image_path}'", color=Colors.YELLOW )

		# --- 0. 统一多边形格式 ---
		processed_polygons = [ ]
		has_obb = False  # 标记是否包含OBB格式的多边形

		if polygons_nested and isinstance( polygons_nested[ 0 ], list ) and polygons_nested[ 0 ]:
			# 检查第一个多边形的第一个元素，判断格式
			first_element = polygons_nested[ 0 ][ 0 ]
			if isinstance( first_element, (int, float) ):
				# 检测到扁平格式 [[x1,y1,x2,y2,...], ...]，进行转换
				logger.debug( "检测到扁平多边形格式，正在转换为标准嵌套格式..." )
				try:
					for flat_polygon in polygons_nested:
						if len( flat_polygon ) % 2 != 0:
							logger.warning(
								f"一个扁平多边形列表包含奇数个坐标值，可能不完整。跳过此多边形。长度: {len( flat_polygon )}"
							)
							continue
						nested_polygon = [ [ float( flat_polygon[ i ] ), float( flat_polygon[ i + 1 ] ) ]
						                   for i in range( 0, len( flat_polygon ), 2 ) ]
						processed_polygons.append( nested_polygon )

						# 检查是否为OBB格式（恰好有4个点）
						if self.is_obb_format( nested_polygon ):
							has_obb = True
				except (TypeError, IndexError) as e:
					logger.error( f"转换扁平多边形格式时出错: {e}。将使用原始数据继续，可能会失败。", color=Colors.ERROR )
					processed_polygons = polygons_nested  # 出错时回退
			else:
				# 认为是标准格式 [[[x1,y1],...], ...]
				processed_polygons = polygons_nested

				# 检查是否有OBB格式的多边形
				for poly in processed_polygons:
					if self.is_obb_format( poly ):
						has_obb = True
						break
		else:
			# 如果输入为空或格式无法判断，直接使用
			processed_polygons = polygons_nested

		# 如果检测到OBB格式，记录日志
		if has_obb:
			logger.info( "检测到YOLO11 OBB格式的定向边界框。", color=Colors.CYAN )

		# --- 1. 默认启用所有策略 ---
		if enabled_strategies is None:
			enabled_strategies = {
				cat: True
				for cat in self.strategies.keys()
			}
		logger.debug( f"启用的策略: {enabled_strategies}" )

		# --- 1.1 如果设置了priority_apis，限制API集合为priority_apis中的API ---
		if self.__priority_apis:
			priority_set = set( self.__priority_apis )
			logger.info( f"使用priority_apis作为API限制: {self.__priority_apis}", color=Colors.CYAN )

			# 处理每个变换类别
			for cat in list( enabled_strategies.keys() ):
				enabled_value = enabled_strategies[ cat ]

				# 检查该类别的策略信息是否存在
				if cat not in self.strategies:
					continue

				# 获取该类别下的所有可用API
				available_apis = set( self.strategies[ cat ][ 'apis' ].keys() )

				# 计算该类别下在priority_apis中的API
				category_priority_apis = list( priority_set.intersection( available_apis ) )

				if not category_priority_apis:
					# 如果该类别下没有priority_api，禁用该类别
					enabled_strategies[ cat ] = False
					logger.debug( f"类别 '{cat}' 没有指定的优先API，已禁用此类别。" )
				else:
					# 如果enabled_value是True，替换为priority_apis的子集
					if enabled_value is True:
						enabled_strategies[ cat ] = category_priority_apis
						logger.debug( f"类别 '{cat}' 的API已限制为: {category_priority_apis}" )
					# 如果enabled_value是列表，取其与priority_apis的交集
					elif isinstance( enabled_value, list ):
						enabled_value_set = set( enabled_value )
						filtered_apis = list( enabled_value_set.intersection( priority_set ) )
						if filtered_apis:
							enabled_strategies[ cat ] = filtered_apis
							logger.debug( f"类别 '{cat}' 的API已限制为: {filtered_apis}" )
						else:
							enabled_strategies[ cat ] = False
							logger.debug( f"类别 '{cat}' 的API交集为空，已禁用此类别。" )
					# 如果enabled_value是字典，处理其中的'apis'键
					elif isinstance( enabled_value, dict ) and 'apis' in enabled_value:
						if isinstance( enabled_value[ 'apis' ], list ):
							apis_set = set( enabled_value[ 'apis' ] )
							filtered_apis = list( apis_set.intersection( priority_set ) )
							if filtered_apis:
								enabled_value[ 'apis' ] = filtered_apis
								logger.debug( f"类别 '{cat}' 的API已限制为: {filtered_apis}" )
							else:
								enabled_strategies[ cat ] = False
								logger.debug( f"类别 '{cat}' 的API交集为空，已禁用此类别。" )
						else:
							# 如果apis不是列表，设置为category_priority_apis
							enabled_value[ 'apis' ] = category_priority_apis
							logger.debug( f"类别 '{cat}' 的API已限制为: {category_priority_apis}" )

			logger.debug( f"应用priority_apis限制后的策略: {enabled_strategies}" )

		# --- 2. 加载图像 ---
		try:
			image = cv2.imread( image_path )
			if image is None:
				raise IOError( f"cv2.imdecode failed for path: {image_path}" )
			image = cv2.cvtColor( image, cv2.COLOR_BGR2RGB )
			logger.info( "图像加载成功。" )
		except Exception as e:
			logger.error( f"从'{image_path}'加载图像失败。错误: {e}", color=Colors.ERROR )
			return np.array( [ ] ), [ ], [ ]

		# --- 3. 构建主管道 ---
		all_transforms = self._build_transforms( enabled_strategies )
		if not all_transforms:
			logger.warning( "未构建任何变换。将返回原始图像。", color=Colors.WARNING )
			return cv2.cvtColor( image, cv2.COLOR_RGB2BGR ), processed_polygons, labels_name

		logger.debug( f"主管道包含 {len( all_transforms )} 个变换组。" )

		compose_kwargs = {
			"transforms": all_transforms,
			"p":          1.0
		}

		# 核心修复: 仅当几何变换被启用时，才添加keypoint_params。
		# 这是为了防止在只应用像素级变换（如颜色、天气）时，出现
		# "Got processor for keypoints, but no transform to process it" 的良性警告。
		if enabled_strategies.get( 'geometric', False ):
			# 获取变换类型，支持从enabled_strategies中读取
			geometric_strategy = enabled_strategies.get( 'geometric' )
			if isinstance( geometric_strategy, dict ) and 'type' in geometric_strategy:
				strategy_type = geometric_strategy[ 'type' ]
			else:
				strategy_type = self.strategies[ 'geometric' ][ 'type' ]

			# 根据几何变换的类型选择合适的关键点处理参数
			# 对于sequential类型，需要更严格的检查
			if strategy_type == 'sequential':
				remove_invisible = True
				check_each_transform = True
			else:
				# one_of或some_of类型
				remove_invisible = False
				check_each_transform = False

			logger.debug(
				f"为几何变换添加关键点参数。"
				f"remove_invisible={remove_invisible}, check_each_transform={check_each_transform}"
			)
			compose_kwargs[ "keypoint_params" ] = A.KeypointParams(
				format='xy', label_fields=[ 'category_ids' ], remove_invisible=remove_invisible,
				check_each_transform=check_each_transform
			)

		master_pipeline = A.ReplayCompose( **compose_kwargs )

		# --- 4. 数据展平 ---
		keypoints = [ ]
		category_ids = [ ]
		for i, polygon in enumerate( processed_polygons ):
			keypoints.extend( polygon )
			category_ids.extend( [ i ] * len( polygon ) )

		# --- 5. 执行变换（统一重试逻辑） ---
		# 这个循环旨在找到一个同时满足以下所有条件的变换结果：
		# 1. 图像被有效变换 (与原图不同)。
		# 2. 所有标注点在变换后依然位于图像边界内 (仅当启用几何变换时检查)。
		#    注意：所有变换类别 (color, weather, artifact, geometric) 均支持三种类型：
		#      - 'one_of': 随机选择一个变换应用
		#      - 'some_of': 随机选择N个变换应用，N的范围由some_of_range参数控制
		#      - 'sequential': 按顺序应用所有变换
		# 3. 变换后幸存的标注框数量与原始数量一致。
		transformed = { }
		original_poly_count = len( processed_polygons )
		attempt_count = 0
		logger.info( f"开始变换重试循环，直到找到有效变换为止..." )

		# 用于存储应用的变换API
		applied_transforms_names = [ ]
		grouped_transforms = { }

		while True:
			attempt_count += 1
			transformed_attempt = master_pipeline( image=image, keypoints=keypoints, category_ids=category_ids )

			# 条件1: 检查图像是否真的发生了变化
			image_is_changed = self._is_image_changed( image, transformed_attempt[ 'image' ] )

			# 条件2: 检查关键点是否仍在界内 (仅当需要时)
			keypoints_are_valid = True
			if enabled_strategies.get( 'geometric', False ):
				h, w = transformed_attempt[ "image" ].shape[ :2 ]
				kps_check = transformed_attempt.get( "keypoints", [ ] )
				if any( not (0 <= x < w and 0 <= y < h) for x, y in kps_check ):
					keypoints_are_valid = False

			# 条件3: 检查标注框数量是否一致
			# 为此，我们需要临时重组一下数据以获取变换后的多边形数量
			temp_transformed_keypoints = transformed_attempt.get( 'keypoints', [ ] )
			temp_transformed_category_ids = transformed_attempt.get( 'category_ids', [ ] )
			temp_regrouped = { }
			for kp, cat_id in zip( temp_transformed_keypoints, temp_transformed_category_ids ):
				if cat_id not in temp_regrouped:
					temp_regrouped[ cat_id ] = [ ]
				temp_regrouped[ cat_id ].append( list( kp ) )
			count_is_consistent = len( temp_regrouped ) == original_poly_count

			# 如果所有条件都满足，我们就找到了一个完美的变换
			if image_is_changed and keypoints_are_valid and count_is_consistent:
				transformed = transformed_attempt
				logger.info( f"在 {attempt_count} 次尝试后找到有效的变换。", color=Colors.SUCCESS )

				# 记录应用的变换
				if 'replay' in transformed:
					applied_transforms_names = self.__parse_replay_data( transformed[ 'replay' ][ 'transforms' ] )
					if applied_transforms_names:
						grouped_transforms = { }
						for name in applied_transforms_names:
							category = self.__api_to_category_map.get( name, 'unknown' )
							if category not in grouped_transforms:
								grouped_transforms[ category ] = [ ]
							grouped_transforms[ category ].append( name )

						log_parts = [ f"{cat}={val}" for cat, val in sorted( grouped_transforms.items() ) ]
						log_str = ", ".join( log_parts )
						logger.info( f"应用变换: {log_str}", color=Colors.ORANGE )

				break
			else:
				# 否则，暂时保存这次尝试的结果，以备所有重试都失败后使用
				logger.debug(
					f"第 {attempt_count} 次尝试失败。 "
					f"图像已更改: {image_is_changed}, "
					f"关键点有效: {keypoints_are_valid}, "
					f"实例数量一致: {count_is_consistent}"
				)

		# --- 6. 数据重组 ---
		transformed_image = cv2.cvtColor( transformed[ 'image' ], cv2.COLOR_RGB2BGR )
		transformed_keypoints = transformed.get( 'keypoints', [ ] )
		transformed_category_ids = transformed.get( 'category_ids', [ ] )

		regrouped_polygons = { }
		for kp, cat_id in zip( transformed_keypoints, transformed_category_ids ):
			if cat_id not in regrouped_polygons:
				regrouped_polygons[ cat_id ] = [ ]
			regrouped_polygons[ cat_id ].append( list( kp ) )

		surviving_indices = sorted( [ int( k ) for k in regrouped_polygons.keys() ] )
		final_polygons = [ ]

		# 重新组织多边形，并确保OBB格式的点顺序一致性
		for i in surviving_indices:
			polygon = regrouped_polygons[ i ]
			# 如果是OBB格式（4个点）并且原始多边形也是OBB格式，则确保点的顺序一致性
			if len( polygon ) == 4 and has_obb:
				final_polygons.append( self.ensure_obb_order( polygon ) )
			else:
				final_polygons.append( polygon )

		final_labels = [ labels_name[ i ] for i in surviving_indices ]

		logger.info(
			f"增强完成。{original_poly_count}个多边形中有{len( final_polygons )}个在变换后保留。", color=Colors.PRIMARY
		)

		# --- 7. 保存图像（如果需要）---
		if save_image:
			import os
			from pathlib import Path

			logger.info( f"开始保存增强后的图像...", color=Colors.YELLOW )

			try:
				# 获取原始图像的文件名和扩展名
				original_path = Path( image_path )
				original_filename = original_path.stem
				original_extension = original_path.suffix

				# 如果未指定保存目录，使用原图像所在目录
				if save_dir is None:
					save_dir = str( original_path.parent )
					logger.debug( f"未指定保存目录，使用原图像所在目录: {save_dir}" )
				else:
					logger.debug( f"使用指定的保存目录: {save_dir}" )

				# 生成新的文件名，包含应用的变换API
				transform_suffix = "_"
				for cat, apis in sorted( grouped_transforms.items() ):
					for api in apis:
						# 将API名称添加到文件名中，但避免名称过长
						transform_suffix += api[ :4 ]

				# 截断文件名，确保不会过长
				if len( transform_suffix ) > 30:
					transform_suffix = transform_suffix[ :30 ] + "..."

				# 生成完整的保存路径
				save_filename = f"{original_filename}{transform_suffix}{original_extension}"
				logger.debug( f"生成的文件名: {save_filename}" )

				# 确保目录存在
				save_dir_path = Path( save_dir )
				os.makedirs( save_dir_path, exist_ok=True )
				logger.debug( f"确保目录存在: {save_dir}" )

				# 构建完整路径
				save_path = os.path.join( save_dir, save_filename )

				# 保存图像
				logger.info( f"正在写入图像: {save_path}", color=Colors.CYAN )
				cv2.imwrite( save_path, transformed_image )

				# 打印应用的变换API和保存的文件路径
				# 构建API信息字符串，用于日志显示
				api_info_str = ""
				for cat, apis in sorted( grouped_transforms.items() ):
					if api_info_str:
						api_info_str += "; "
					api_info_str += f"{cat}: {', '.join( apis )}"

				# 通过logger输出API和文件名在同一行
				logger.info( f"应用变换 API: [{api_info_str}] -> 保存文件: {save_path}", color=Colors.SUCCESS )

				# 为用户界面打印更详细的格式化信息
				print( f"\n应用的变换API:" )
				for cat, apis in sorted( grouped_transforms.items() ):
					print( f"  {cat}: {', '.join( apis )}" )
				print( f"\n增强后的图像已保存至: {save_path}" )

			except Exception as e:
				logger.error( f"保存图像时出错: {e}", color=Colors.ERROR )
				print( f"保存图像时出错: {e}" )

		return transformed_image, final_polygons, final_labels


__all__ = [ 'AugmentationPipeline' ]


def example_usage( num_samples: int = 10 ):
	"""
	展示如何使用 `AugmentationPipeline` 类的示例。

	此示例演示了在默认情况下（即所有增强策略全部启用时）
	管道的功能。它会生成多个样本，每个样本都是完整增强管道
	（颜色->天气->伪影->几何）一次随机应用的结果。
	"""
	# 将项目根目录添加到Python路径中，以解决模块导入问题
	try:
		project_root = os.path.dirname(
			os.path.dirname( os.path.dirname( os.path.dirname( os.path.dirname( os.path.abspath( __file__ ) ) ) ) )
		)
		if project_root not in sys.path:
			sys.path.insert( 0, project_root )
	except NameError:
		pass

	image_path = r"K:\PaddleDet\PaddleDetection-release-2.8\GUI\image\wow_screentshot\yajikuang\a497b6e8-a87c-4713-a794-0a06d6175778 (2)\00d98ec8-d485-4665-91e0-880908fd27d0.jpg"
	if not os.path.exists( image_path ):
		print( f"错误: 示例图片路径不存在 '{image_path}'。" )
		return

	image = cv2.imread( image_path )
	polygons_nested = [
		[
			[ 439.2105263157895, 334.91228070175436 ],
			[ 476.0526315789474, 325.7894736842105 ],
			[ 471.140350877193, 294.91228070175436 ],
			[ 458.859649122807, 279.4736842105263 ],
			[ 451.8421052631579, 268.9473684210526 ],
			[ 439.2105263157895, 258.0701754385965 ],
			[ 443.42105263157896, 252.10526315789474 ],
			[ 473.9473684210526, 255.96491228070178 ],
			[ 484.1228070175439, 266.140350877193 ],
			[ 507.280701754386, 288.24561403508767 ],
			[ 511.49122807017545, 316.66666666666663 ],
			[ 510.7894736842105, 333.1578947368421 ],
			[ 518.5087719298245, 348.59649122807014 ],
			[ 525.5263157894736, 366.14035087719293 ],
			[ 504.47368421052636, 371.7543859649123 ],
			[ 503.7719298245614, 394.2105263157894 ],
			[ 498.50877192982455, 418.77192982456137 ],
			[ 482.36842105263156, 436.66666666666663 ],
			[ 465.1754385964912, 451.7543859649123 ],
			[ 447.280701754386, 450.7017543859649 ],
			[ 426.57894736842104, 452.1052631578947 ],
			[ 438.50877192982455, 436.31578947368416 ],
			[ 452.54385964912285, 409.29824561403507 ],
			[ 455.70175438596493, 388.24561403508767 ],
			[ 463.42105263157896, 365.43859649122805 ],
			[ 463.7719298245614, 358.0701754385965 ],
			[ 442.719298245614, 348.9473684210526 ] ],
		[
			[ 505.8771929824561, 280.52631578947364 ],
			[ 522.719298245614, 258.4210526315789 ],
			[ 526.578947368421, 240.87719298245614 ],
			[ 518.5087719298245, 222.63157894736844 ],
			[ 499.9122807017544, 215.26315789473685 ],
			[ 477.8070175438596, 215.26315789473685 ],
			[ 463.7719298245614, 222.28070175438597 ],
			[ 452.54385964912285, 237.3684210526316 ],
			[ 450.7894736842105, 253.1578947368421 ],
			[ 456.40350877192986, 262.6315789473684 ]
		],
	]
	labels_name = [ "0", "2" ]

	# 1. 初始化管道 (不传入 enabled_strategies 将默认启用所有策略)
	print( "--- 示例1: 使用默认API顺序 ---" )
	pipeline_default = AugmentationPipeline()

	visualizer = VisualizationHelper()
	unique_labels = sorted( list( set( labels_name ) ) )
	predefined_colors = [ (255, 0, 0), (0, 255, 0), (0, 0, 255), (255, 255, 0), (255, 0, 255), (0, 255, 255) ]
	color_map = {
		label: predefined_colors[ i % len( predefined_colors ) ]
		for i, label in enumerate( unique_labels )
	}

	images_to_display = [ ]
	titles_to_display = [ ]

	original_vis_image = visualizer.visualize_polygons( image.copy(), polygons_nested, labels_name, color_map )
	images_to_display.append( original_vis_image )
	titles_to_display.append( "Original Image" )

	# 我们将只使用模糊和亮度对比度调整这两个API
	priority_list = [ 'HueSaturationValue', 'Affine', 'ElasticTransform' ]
	pipeline_priority = AugmentationPipeline( priority_apis=priority_list )

	# 应用这个带有API限制的管道
	print( f"正在使用受限API生成 {num_samples} 个增强样本..." )

	images_to_display_priority = [ original_vis_image ]
	titles_to_display_priority = [ "Original Image" ]

	for i in range( num_samples ):
		t_img, t_poly, t_labels = pipeline_priority.apply(
			image_path=image_path,
			polygons_nested=polygons_nested,
			labels_name=labels_name,
		)

		title = f"Transformed (Limited APIs) {i + 1}"
		vis_image = visualizer.visualize_polygons( t_img, t_poly, t_labels, color_map )
		images_to_display_priority.append( vis_image )
		titles_to_display_priority.append( title )
		print( f"生成第 {i + 1} 个API受限样本。" )

	print( "生成完成。" )
	visualizer.display_images( images_to_display_priority, titles_to_display_priority )


if __name__ == '__main__':
	os.environ[ 'KMP_DUPLICATE_LIB_OK' ] = 'TRUE'
	os.environ[ "MKL_NUM_THREADS" ] = "1"
	os.environ[ "NUMEXPR_NUM_THREADS" ] = "1"
	os.environ[ "OMP_NUM_THREADS" ] = "1"
# example_usage()
