from __future__ import annotations
from typing import Any
import cv2
import numpy as np
from albumentations.augmentations.geometric.functional import (
	apply_affine_to_points, 
	calculate_affine_transform_padding,
	get_pad_grid_dimensions,
	generate_reflected_keypoints
)

def custom_keypoints_affine(
	keypoints: np.ndarray,
	matrix: np.ndarray,
	scale: dict[str, float],
	image_shape: tuple[int, int],
	border_mode: int,
	**kwargs,
) -> np.ndarray:
	"""
	对关键点应用仿射变换，但不为反射边界创建新的关键点。
	这是对 albumentations.augmentations.geometric.functional.keypoints_affine 的修改版。
	原始函数会为反射、包裹等边框模式生成新的关键点，而此版本仅转换
	原始关键点，以满足"只填充像素，不标注反射对象"的需求。

	参数:
	- keypoints (np.ndarray): 待变换的关键点。
	- matrix (np.ndarray): 仿射变换矩阵。
	- scale (dict): 包含 'x' 和 'y' 缩放因子的字典。
	- image_shape (tuple[int, int]): 图像的 (高度, 宽度)。
	- border_mode (int): OpenCV 的边框模式 (例如 cv2.BORDER_REFLECT_101)。
	  注意：此参数在此函数的修改版中保留以保持接口兼容性，但不再用于生成反射点。

	返回:
	- np.ndarray: 仅包含原始变换后幸存的关键点，不含任何反射点。
	"""
	if keypoints.shape[0] == 0:
		return keypoints

	# 清洗 keypoints，确保只使用 x, y 坐标进行变换，以兼容不同版本的内部数据结构
	points = keypoints[:, :2]
	# 对所有关键点应用核心的仿射变换
	transformed_points = apply_affine_to_points(points, matrix)

	# 将变换后的坐标与其他可能存在的列（如角度、缩放等）重新组合
	transformed_keypoints = np.column_stack((transformed_points, keypoints[:, 2:]))

	# 【核心修改】
	# 原始的 albumentations 函数会在此处检查 border_mode，
	# 如果不是 BORDER_CONSTANT，就会调用 generate_reflected_keypoints
	# 来创建反射的标注点。
	# 我们在这里直接返回变换后的原始点，从而阻止为反射区域创建标注。
	return transformed_keypoints 