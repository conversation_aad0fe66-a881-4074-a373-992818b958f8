from typing import Any
from anylabeling.customize_ui.layout.layout import Ui_Form
from global_tools.utils import Logger, LogLevel
from global_tools.ui_tools import LogOutput,LineEditManager, LineEditMemory, \
    QPushButtonManager,CheckBoxStateManager,SelectAllCheckBoxManager,CheckBoxManager, QLabelManager

import threading
import time
import os
import yaml

import anylabeling.customize_ui.src.helper.const as CONST
from anylabeling.customize_ui.helper.const import X_LABEL_LOG
from anylabeling.customize_ui.helper.folder_importer import FolderImporter
from anylabeling.customize_ui.src.ui_operate.home.helper import check_lineedit
import traceback

import anylabeling.customize_ui.src.ui_operate.home.helper as home_helper
from global_tools.utils import ClassInstanceManager



class HomeUiOperate:

    def __init__( self, ui_form: Ui_Form, log_output: LogOutput, logger: Logger = None ):
        self.__ui_form = ui_form
        self.__logger = ClassInstanceManager.get_instance( X_LABEL_LOG )
        self.__log_output = log_output

        # --------------------- QLineEdit ---------------------
        # self.__line_edit_list = [
        #     self.__ui_form.lineEdit_45,
        #     self.__ui_form.lineEdit_6,
        #     self.__ui_form.lineEdit_7,
        #     self.__ui_form.lineEdit_9,
        #     self.__ui_form.lineEdit_8,
        #     self.__ui_form.lineEdit_11,
        # ]
        # self.__line_edit_manager = LineEditManager( *self.__line_edit_list )
        # self.__line_edit_manager.set_all_auto_clear( False )
        # check_lineedit( self.__line_edit_manager )

        # self.__line_edit_memory = LineEditMemory(
        #     line_edits = self.__line_edit_list,
        #     log_output = self.__log_output,
        #     config_filename = CONST.LINE_EDIT_MEMORY_FILE_NAME,
        #     config_path = CONST.CONFIG_PATH,
        # )
        self.__line_edit_manager = LineEditManager.get_instance()

        # --------------------- QPushButton ---------------------
        # self.__push_button_list = [
        #     self.__ui_form.pushButton_31,
        #     self.__ui_form.pushButton_20,
        #     self.__ui_form.pushButton_22,
        #     self.__ui_form.pushButton_24,
        # ]
        self.__push_button_manager = QPushButtonManager.get_instance()
        self.__push_button_manager.connect_clicked_signal( button_name = "pushButton_31", slot = self.open_folder_image_button_function )
        self.__push_button_manager.connect_clicked_signal( button_name = "pushButton_20", slot = self.__export_dataset_button_function )
        self.__push_button_manager.connect_clicked_signal( button_name = "pushButton_22", slot = self.__get_annotation_count_button_function )
        self.__push_button_manager.connect_clicked_signal( button_name = "pushButton_24", slot = self.__generate_yaml_config_button_function )

        # --------------------- QLabel ---------------------
        # self.__label_list = [
        #     self.__ui_form.label_34,
        #     self.__ui_form.label_37,
        #     self.__ui_form.label_178,
        #     self.__ui_form.label_69,
        #     self.__ui_form.label_51,
        # ]
        self.__label_manager = QLabelManager.get_instance()

        # --------------------- QCheckBox ---------------------
        # self.__checkbox_container_list = [
        #     self.__ui_form.verticalLayout_2,
        #     self.__ui_form.verticalLayout_5,
        # ]

        # self.__checkbox_state_manager = CheckBoxStateManager(
        #     container = self.__checkbox_container_list,
        #     config_dir = r"K:\yolo11\X-AnyLabeling-main\anylabeling\customize_ui\config",
        #     log_output = self.__log_output,
        #     logger = self.__logger
        # )
        # self.__checkbox_state_manager.load_states()

        # # 管理多个复选框组的全选/取消全选功能。
        # # 该管理器会自动在 `container` 列表中的每个指定布局（如 color, weather 等）内，
        # # 动态添加一个文本为 "全选/取消全选" 的QCheckBox。
        # # 点击该复选框，可以控制其所在布局内的所有其他兄弟QCheckBox同时被选中或取消选中。
        # self.__select_all_checkbox_manager = SelectAllCheckBoxManager(
        #     container = [
        #         self.__ui_form.color,
        #         self.__ui_form.weather,
        #         self.__ui_form.artifact,
        #         self.__ui_form.geometric,
        #     ],
        #     select_all_text = "全选/取消全选",
        #     logger = self.__logger
        # )

        # self.__checkbox_manager = CheckBoxManager( *[ self.__ui_form.verticalLayout_5 ], logger = self.__logger )

    def __call__( self, *args: Any, **kwds: Any ) -> Any:
        pass

    def ui_operate( self ):
        pass

    def open_folder_image_button_function( self ):
        # 打开文件夹图像按钮
        pass
        image_folder_path = self.__line_edit_manager.get_line_edit( "lineEdit_45" ).text()

        try:

            def func():
                return [
                    r"K:\PaddleDet\PaddleDetection-release-2.8\GUI\image\wow_screentshot\亚基矿\a497b6e8-a87c-4713-a794-0a06d6175778\00d98ec8-d485-4665-91e0-880908fd27d0.jpg",
                    r"K:\PaddleDet\PaddleDetection-release-2.8\GUI\image\wow_screentshot\亚基矿\a497b6e8-a87c-4713-a794-0a06d6175778\0b0f63e4-e3d3-497a-a60f-701d8788e677.jpg",
                ]

            FolderImporter.load_from_path( func )
        except Exception as e:
            traceback.print_exc()

    def __export_dataset_button_function( self ):
        # 导出标注数据集按钮
        from .helper import DatasetExporter
        from global_tools.utils import Logger

        def func():
            try:
                logger = self.__logger

                # 初始化标签显示
                if self.__label_manager:
                    self.__label_manager.set_text( "label_34", "0/0" )
                    self.__label_manager.set_text( "label_37", "0/0" )

                exporter = DatasetExporter(
                    self.__line_edit_manager, self.__checkbox_manager, self.__log_output, logger = logger, label_manager = self.__label_manager
                )
                success = exporter.export_dataset()

                if success:
                    self.__log_output.append( "数据集导出成功！", color = "green" )
                else:
                    self.__log_output.append( "数据集导出失败，请检查日志信息。", color = "red" )
            except Exception as e:
                import traceback
                self.__log_output.append( f"导出数据集时发生错误: {str(e)}", color = "red" )
                self.__log_output.append( traceback.format_exc(), color = "red" )

        th = threading.Thread( target = func )
        th.start()

    def __get_annotation_count_button_function( self ):
        """获取标注对象总数量的按钮函数"""
        from .helper import AnnotationCounter

        def func():
            try:
                self.__log_output.append( "开始统计标注对象总数量...", color = "blue" )

                # 创建标注对象计数器实例
                counter = AnnotationCounter( self.__line_edit_manager, self.__log_output, self.__logger, self.__label_manager )

                # 执行统计
                total_count = counter.count_annotations()

                # 显示结果
                if total_count > 0:
                    self.__log_output.append( f"成功统计完成，共找到 {total_count} 个标注对象。", color = "green" )
                else:
                    self.__log_output.append( "未找到有效的标注对象，请检查输入路径。", color = "yellow" )

            except Exception as e:
                import traceback
                self.__log_output.append( f"统计标注对象时发生错误: {str(e)}", color = "red" )
                self.__log_output.append( traceback.format_exc(), color = "red" )

        # 在单独的线程中运行，避免阻塞UI
        th = threading.Thread( target = func )
        th.start()

    def __generate_yaml_config_button_function( self ):
        """生成YAML配置文件按钮函数"""
        from .helper import YAMLConfigGenerator

        def func():
            try:
                self.__log_output.append( "开始生成YAML配置文件...", color = "blue" )

                # 创建YAML配置生成器实例
                generator = YAMLConfigGenerator( self.__line_edit_manager, self.__log_output, self.__logger, self.__label_manager )

                # 执行生成
                success = generator.generate_yaml_config()

                # 显示结果
                if success:
                    self.__log_output.append( "YAML配置文件生成成功！", color = "green" )
                else:
                    self.__log_output.append( "YAML配置文件生成失败，请检查日志信息。", color = "yellow" )

            except Exception as e:
                import traceback
                self.__log_output.append( f"生成YAML配置文件时发生错误: {str(e)}", color = "red" )
                self.__log_output.append( traceback.format_exc(), color = "red" )

        # 在单独的线程中运行，避免阻塞UI
        th = threading.Thread( target = func )
        th.start()
