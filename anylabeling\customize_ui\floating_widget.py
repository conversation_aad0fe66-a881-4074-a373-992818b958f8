from PyQt5 import QtCore, QtGui, QtWidgets

from .layout.layout import Ui_Form
from global_tools.ui_tools import NestedScrollAreaFixer
from global_tools.utils import Logger, LogLevel
from global_tools.ui_tools import LogOutput
import traceback
import anylabeling.customize_ui.src.ui_operate.train_data.train_data as Train
from anylabeling.customize_ui.src.ui_operate.train_model.train_model import TrainModel
from anylabeling.customize_ui.src.ui_operate.predict_image.predict_image import PredictImage
from anylabeling.customize_ui.src.ui_operate.save_data.save_predict_data import SavePredictData
from anylabeling.customize_ui.src.ui_operate.common.common import line_edit_sync, CommonLineEdit

logger = Logger()


class FloatingWidget( QtWidgets.QWidget ):
	"""
	一个自定义的浮动窗口部件，可以显示在主应用程序的顶部。
	它从 Ui_Form 类加载其UI定义。
	"""

	def __init__( self, parent=None ):
		"""
		初始化浮动窗口部件并从 layout.py 设置UI。

		参数:
			parent: 父窗口部件。
		"""
		super().__init__( parent )

		# --- 窗口行为 ---
		# 设置窗口标志，使其成为一个工具窗口。
		# 移除了 WindowStaysOnTopHint, 使其不再全局置顶。
		self.setWindowFlags( QtCore.Qt.Tool )  # type: ignore

		# --- 从 layout.py 设置UI ---
		# 实例化UI类并在此窗口部件上进行设置
		self.ui = Ui_Form()
		self.ui.setupUi( self )
		self.log_output = LogOutput( self.ui.textEdit )
		scroll_area = self.ui.scrollArea
		self.scroll_area_manager = NestedScrollAreaFixer( scroll_area )

		# 窗口标题和大小现在由布局文件控制。
		# 如果需要，我们可以覆盖标题。
		self.setWindowTitle( "自定义浮动容器" )

		# --- 定位 ---
		self.center_on_parent()

		try:

			CommonLineEdit.get_line_edit_list( ui_form=self.ui ) # QLineEdit
			CommonLineEdit.line_edit_memory( self.log_output ) # QLineEdit
			CommonLineEdit.line_edit_manager() # QLineEdit
			CommonLineEdit.setup_input_validators() # QLineEdit
			CommonLineEdit.line_edit_completer_cache( ui_form=self.ui ) # QLineEdit
			CommonLineEdit.check_box_manager( ui_form=self.ui, log_output=self.log_output ) # QCheckBox
			CommonLineEdit.qpush_button_manager( ui_form=self.ui ) # QPushButton
			CommonLineEdit.qlabel_manager( ui_form=self.ui ) # QLabel

			# 同步行编辑器
			line_edit_sync( cls=self, ui_form=self.ui, log_output=self.log_output )

			self.ui_operate()
		except Exception as e:
			traceback.print_exc()
		# raise RuntimeError(e)

	def center_on_parent( self ):
		"""
		将窗口部件居中于其父窗口的中间。
		如果没有可用的父窗口，它将居中于屏幕。
		"""
		if self.parent():
			parent_rect = self.parent().frameGeometry()  # type: ignore
			parent_center = parent_rect.center()
		else:
			# 如果没有父窗口，则回退到屏幕中心
			screen = QtWidgets.QApplication.primaryScreen()
			parent_center = screen.availableGeometry().center()

		self_rect = self.frameGeometry()
		self_rect.moveCenter( parent_center )
		self.move( self_rect.topLeft() )

	def ui_operate( self ):
		# --- 准备训练数据 ---
		self.home_ui_operate = Train.Train( ui_form=self.ui, log_output=self.log_output )
		self.home_ui_operate()

		# --- 训练模型 ---
		self.train_model = TrainModel( ui_form=self.ui, log_output=self.log_output )
		self.train_model()

		# --- 预测图片 ---
		self.predict_image = PredictImage( ui_form=self.ui, log_output=self.log_output )
		self.predict_image()

		# --- 保存预测数据 ---
		self.save_predict_data = SavePredictData( ui_form=self.ui, log_output=self.log_output )
		self.save_predict_data()

# --- Usage Example ---
# To use this widget, you would typically do the following in your main window class:
#
# from anylabeling.customize_ui.floating_widget import FloatingWidget
#
# # In your main window's __init__
# self.custom_widget = None
#
# # In a method or connected to a signal/shortcut
# def show_custom_widget(self):
#     if self.custom_widget is None:
#         self.custom_widget = FloatingWidget(parent=self)
#     self.custom_widget.show()
#     self.custom_widget.raise_()
#     self.custom_widget.activateWindow()
