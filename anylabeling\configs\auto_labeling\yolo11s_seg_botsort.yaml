type: yolo11_seg_track
name: yolo11s_seg_botsort-r20240930
provider: Ultralytics
display_name: YOLO11s-Seg-BoT-SORT
model_path: https://github.com/CVHub520/X-AnyLabeling/releases/download/v2.4.4/yolo11s-seg.onnx
iou_threshold: 0.5
conf_threshold: 0.1
# show_boxes: True
tracker:
  # Base settings
  tracker_type: botsort # tracker type, ['botsort', 'bytetrack']
  track_high_thresh: 0.5 # threshold for the first association
  track_low_thresh: 0.1 # threshold for the second association
  new_track_thresh: 0.6 # threshold for init new track if the detection does not match any tracks
  track_buffer: 30 # buffer to calculate the time when to remove tracks
  match_thresh: 0.8 # threshold for matching tracks
  fuse_score: True
  # BoT-SORT settings
  gmc_method: sparseOptFlow # method of global motion compensation
  # ReID model related thresh (not supported yet)
  proximity_thresh: 0.5
  appearance_thresh: 0.25
  with_reid: False
filter_classes:
  - person
  - car
classes:
  - person
  - bicycle
  - car
  - motorcycle
  - airplane
  - bus
  - train
  - truck
  - boat
  - traffic light
  - fire hydrant
  - stop sign
  - parking meter
  - bench
  - bird
  - cat
  - dog
  - horse
  - sheep
  - cow
  - elephant
  - bear
  - zebra
  - giraffe
  - backpack
  - umbrella
  - handbag
  - tie
  - suitcase
  - frisbee
  - skis
  - snowboard
  - sports ball
  - kite
  - baseball bat
  - baseball glove
  - skateboard
  - surfboard
  - tennis racket
  - bottle
  - wine glass
  - cup
  - fork
  - knife
  - spoon
  - bowl
  - banana
  - apple
  - sandwich
  - orange
  - broccoli
  - carrot
  - hot dog
  - pizza
  - donut
  - cake
  - chair
  - couch
  - potted plant
  - bed
  - dining table
  - toilet
  - tv
  - laptop
  - mouse
  - remote
  - keyboard
  - cell phone
  - microwave
  - oven
  - toaster
  - sink
  - refrigerator
  - book
  - clock
  - vase
  - scissors
  - teddy bear
  - hair drier
  - toothbrush
