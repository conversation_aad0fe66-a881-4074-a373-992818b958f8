import os
import json
import logging
import re
from collections import deque
import traceback
from typing import Dict, List, Tuple, Optional, Any, Union, Callable

from PyQt5.QtGui import QMouseEvent, QCursor
from PyQt5.QtWidgets import QApplication, QLabel, QLayout, QLineEdit, QWidget, QTextEdit, QCheckBox
from PyQt5.QtCore import Qt

from global_tools.utils import Colors, Logger
from global_tools.utils.enhanced_process import EnhancedProcess, SharedDataManager, ProcessLogger
from global_tools.ui_tools import LineEditManager, LogOutput, constant



class YOLOFormatConverter:
    """
    YOLO格式转换器，用于将标注数据转换为YOLO格式。
    
    该类接收输入文件夹、标签文件和日志记录器，支持将JSON格式的标注数据转换为YOLO格式。
    
    使用示例:
    ```python
    # 初始化转换器
    converter = YOLOFormatConverter(
        input_folder="path/to/input_folder",
        labels_file="path/to/labels.txt",
        logger=Logger()  # 传入一个Logger实例
    )
    
    # 执行转换
    yolo_data = converter.convert_to_yolo_format()
    ```
    """

    def __init__( self, input_folder: str, labels_file: str, logger = None ):
        """
        初始化YOLO格式转换器
        
        Args:
            input_folder: 包含图像和JSON文件的输入文件夹路径
            labels_file: 标签文件路径
            logger: 日志记录器实例，用于记录转换过程中的日志信息
        """
        self.input_folder = input_folder
        self.labels_file = labels_file
        self.logger = logger
        self.labels = []
        self.file_pairs = []

        # 初始化后立即读取标签文件
        self.__read_labels_file()
        # 收集文件对
        self.__collect_files()

    def __log( self, message: str, level: str = "info" ):
        """
        处理日志消息。
        
        Args:
            message: 日志消息
            level: 日志级别，默认为info
        """
        if self.logger:
            if hasattr( self.logger, level.lower() ):
                getattr( self.logger, level.lower() )( message )
            else:
                # 如果logger没有对应的方法，使用标准logging模块
                if level.lower() == "debug":
                    logging.debug( message )
                elif level.lower() == "warning":
                    logging.warning( message )
                elif level.lower() == "error":
                    logging.error( message )
                else:
                    logging.info( message )
        else:
            # 如果没有设置日志记录器，则使用内置的logging模块
            if level.lower() == "debug":
                logging.debug( message )
            elif level.lower() == "warning":
                logging.warning( message )
            elif level.lower() == "error":
                logging.error( message )
            else:
                logging.info( message )

    def __collect_files( self ) -> None:
        """
        收集输入文件夹中的图像和JSON文件对。
        """
        self.__log( f"开始从 {self.input_folder} 收集文件", level = "info" )

        if not os.path.exists( self.input_folder ):
            self.__log( f"输入文件夹不存在: {self.input_folder}", level = "error" )
            return

        if not os.path.isdir( self.input_folder ):
            self.__log( f"指定的路径不是一个文件夹: {self.input_folder}", level = "error" )
            return

        image_extensions = ( '.jpg', '.jpeg', '.png', '.bmp', '.tif', '.tiff' )
        self.file_pairs = []

        # 统计数据
        total_image_files = 0
        total_json_files = 0
        valid_pairs = 0

        try:
            for filename in os.listdir( self.input_folder ):
                file_path = os.path.join( self.input_folder, filename )

                # 跳过非文件
                if not os.path.isfile( file_path ):
                    continue

                base_name, ext = os.path.splitext( filename )
                ext = ext.lower()    # 转为小写以进行不区分大小写的比较

                # 检查是否是图像文件
                if ext in image_extensions:
                    total_image_files += 1

                    # 查找对应的JSON文件
                    json_path = os.path.join( self.input_folder, base_name + '.json' )
                    if os.path.exists( json_path ) and os.path.isfile( json_path ):
                        total_json_files += 1
                        valid_pairs += 1
                        self.file_pairs.append( ( file_path, json_path ) )
                        self.__log( f"找到文件对: {file_path} - {json_path}", level = "debug" )

            self.__log(
                f"扫描结果: {total_image_files} 个图像文件, {total_json_files} 个JSON文件, {valid_pairs} 对有效配对", level = "info"
            )

        except Exception as e:
            self.__log( f"扫描文件时出错: {str(e)}", level = "error" )
            return

        self.__log( f"找到 {len(self.file_pairs)} 对图像和JSON文件", level = "info" )

    def __read_labels_file( self ) -> None:
        """
        读取标签文件内容。
        """
        self.__log( f"读取标签文件: {self.labels_file}", level = "info" )

        if not os.path.exists( self.labels_file ):
            self.__log( f"标签文件不存在: {self.labels_file}", level = "error" )
            return

        if not os.path.isfile( self.labels_file ):
            self.__log( f"指定的标签路径不是一个文件: {self.labels_file}", level = "error" )
            return

        try:
            with open( self.labels_file, 'r', encoding = 'utf-8' ) as f:
                # 读取所有行并过滤空行
                lines = f.readlines()
                self.labels = []

                for i, line in enumerate( lines, 1 ):
                    line = line.strip()
                    if line:    # 非空行
                        if line in self.labels:
                            self.__log( f"警告: 标签文件中发现重复标签 '{line}' (第{i}行)", level = "warning" )
                        else:
                            self.labels.append( line )

            if not self.labels:
                self.__log( "警告: 标签文件为空或只包含空行", level = "warning" )

            self.__log( f"读取到 {len(self.labels)} 个标签: {', '.join(self.labels)}", level = "info" )

        except Exception as e:
            self.__log( f"读取标签文件出错: {self.labels_file}, 错误: {str(e)}", level = "error" )

    def __read_json_file( self, json_path: str ) -> List[ Dict ]:
        """
        读取JSON文件中的标注信息。
        
        Args:
            json_path: JSON文件路径
            
        Returns:
            包含标注信息的列表
        """
        try:
            with open( json_path, 'r', encoding = 'utf-8' ) as f:
                data = json.load( f )

            shapes = data.get( 'shapes', [] )
            self.__log( f"从 {json_path} 读取到 {len(shapes)} 个标注形状", level = "debug" )
            return shapes
        except Exception as e:
            self.__log( f"读取JSON文件出错: {json_path}, 错误: {str(e)}", level = "error" )
            return []

    def __normalize_coordinates( self, points: List[ List[ float ] ], image_width: int,
                                 image_height: int ) -> List[ float ]:
        """
        将坐标归一化为YOLO格式。
        
        Args:
            points: 多边形坐标点列表，如[[x1, y1], [x2, y2], ...]
            image_width: 图像宽度
            image_height: 图像高度
            
        Returns:
            归一化后的坐标列表，如[x1, y1, x2, y2, ...]
        """
        self.__log( f"归一化坐标点 (图像尺寸: {image_width}x{image_height})", level = "debug" )

        # 检查宽高是否有效
        if image_width <= 0 or image_height <= 0:
            self.__log( f"错误: 图像尺寸无效 ({image_width}x{image_height})，无法进行坐标归一化", level = "error" )
            return []

        normalized = []
        for point in points:
            # 归一化x坐标
            normalized.append( point[ 0 ]/image_width )
            # 归一化y坐标
            normalized.append( point[ 1 ]/image_height )
        return normalized

    def __get_image_dimensions_from_json( self, json_path: str ) -> Tuple[ int, int ]:
        """
        从JSON文件中获取图像尺寸。
        
        Args:
            json_path: JSON文件路径
            
        Returns:
            包含图像宽度和高度的元组
        """
        try:
            with open( json_path, 'r', encoding = 'utf-8' ) as f:
                data = json.load( f )
                image_width = data.get( 'imageWidth', 0 )
                image_height = data.get( 'imageHeight', 0 )

            return image_width, image_height
        except Exception as e:
            self.__log( f"从JSON文件获取图像尺寸出错: {json_path}, 错误: {str(e)}", level = "error" )
            return 0, 0

    def convert_to_yolo_format( self ) -> List[ Tuple[ str, List[ str ] ] ]:
        """
        将标注信息转换为YOLO格式。
        
        Returns:
            包含图像路径和对应YOLO格式标注的列表，如[(image_path, ["class_idx x1 y1 x2 y2..."]), ...]
            
        使用示例:
        ```python
        # 初始化转换器
        converter = YOLOFormatConverter(
            input_folder="path/to/input_folder",
            labels_file="path/to/labels.txt",
            logger=Logger()
        )
        
        # 执行转换
        yolo_data = converter.convert_to_yolo_format()
        ```
        """
        self.__log( f"开始将 {len(self.file_pairs)} 对文件转换为YOLO格式", level = "info" )

        if not self.labels:
            self.__log( "错误: 没有读取到有效标签，无法进行转换", level = "error" )
            return []

        if not self.file_pairs:
            self.__log( "错误: 没有找到有效的文件对，无法进行转换", level = "error" )
            return []

        yolo_data = []

        for image_path, json_path in self.file_pairs:
            shapes = self.__read_json_file( json_path )

            if not shapes:
                self.__log( f"警告: JSON文件没有标注信息: {json_path}", level = "warning" )
                continue

            # 获取图像尺寸，从JSON文件中读取
            image_width, image_height = self.__get_image_dimensions_from_json( json_path )

            if image_width <= 0 or image_height <= 0:
                self.__log( f"警告: 无法获取图像尺寸: {image_path}", level = "warning" )
                continue

            yolo_annotations = []

            for shape in shapes:
                label_name = shape.get( 'label' )
                points = shape.get( 'points', [] )

                # 检查标签是否在预定义列表中
                if label_name not in self.labels:
                    self.__log( f"警告: 标签 '{label_name}' 不在标签列表中，跳过", level = "warning" )
                    continue

                # 获取标签索引
                label_idx = self.labels.index( label_name )

                # 归一化坐标
                normalized_coords = self.__normalize_coordinates( points, image_width, image_height )

                # 格式化为YOLO格式字符串: "label_idx x1 y1 x2 y2 ..."
                coords_str = ' '.join( f"{coord:.6f}" for coord in normalized_coords )
                yolo_annotation = f"{label_idx} {coords_str}"

                yolo_annotations.append( yolo_annotation )

            self.__log( f"文件 {os.path.basename(image_path)} 转换为 {len(yolo_annotations)} 个YOLO标注", level = "debug" )

            yolo_data.append( ( image_path, yolo_annotations ) )

        self.__log( f"转换了 {len(yolo_data)} 个文件到YOLO格式", level = "info" )
        return yolo_data



def evaluate_condition_string( condition_string: str, data_dict: Dict[ str, float ], logger: Logger ) -> bool:
    """
    根据给定的数据字典，安全地评估一个自定义的条件字符串。

    此函数通过一个健壮的"替换-净化-执行"流程，将一个包含逻辑的字符串
    转换为可执行的Python代码，并返回其布尔结果。它被设计为可抵御
    基本的代码注入风险，并能优雅地处理各种错误。

    Args:
        condition_string (str):
            需要评估的逻辑条件字符串。
            例如: "metrics/mAP50-95(B) >= 0.75 AND cur_ecpohs >= 50"

        data_dict (Dict[str, float]):
            一个字典，其中包含了条件字符串中可能引用的所有度量指标及其
            对应的当前数值。
        
        logger (logging.Logger):
            一个标准的日志记录器实例，用于记录执行过程中的警告或错误信息。

    Returns:
        bool:
            条件字符串的最终评估结果。
            在以下情况下会返回 False 并记录错误/警告:
            - 条件字符串为空。
            - 字符串中引用了`data_dict`中不存在的度量指标。
            - 最终生成的表达式存在语法错误。
            - 在净化阶段检测到潜在的不安全代码。
    """
    if not condition_string or not condition_string.strip():
        return False

    # 步骤 1: 查找条件字符串中所有潜在的变量名。
    # 这个正则表达式旨在捕获可以包含字母、数字、下划线、点、斜杠、连字符
    # 以及可选的括号后缀（如'(B)'或'(M)'）的标识符。
    variable_regex = re.compile( r'[a-zA-Z0-9_./-]+(?:\([a-zA-Z]+\))?' )
    all_potential_vars = set( variable_regex.findall( condition_string ) )

    # 步骤 1.5: 过滤掉所有纯数字的"变量"，因为它们是值而不是键。
    # 这是一个关键的修正，可以防止 "0.75" 这类数值被错误地当作度量指标去字典里查找。
    variables_found = set()
    for var in all_potential_vars:
        try:
            # 尝试将找到的片段转换为浮点数。
            float( var )
        except ValueError:
            # 如果转换失败，说明它不是一个纯数字，而是一个真正的变量名。
            variables_found.add( var )

    eval_string = condition_string

    # 步骤 2: 按变量名长度降序排序，然后进行替换。
    # 这是至关重要的一步，可以防止因短变量名是长变量名的子串而导致的替换错误。
    # 例如，确保 "metrics/mAP50" 在 "metrics/mAP" 之前被替换。
    sorted_variables = sorted( list( variables_found ), key = len, reverse = True )

    for var in sorted_variables:
        # 忽略逻辑关键字
        if var.upper() in ( 'AND', 'OR', 'NOT' ):
            continue

        if var in data_dict:
            # 将变量名安全地替换为其在字典中的数值
            eval_string = eval_string.replace( var, str( data_dict[ var ] ) )
        else:
            # 如果在数据字典中找不到度量指标，记录警告并返回False
            logger.warning( f"在条件字符串中发现未知度量指标 '{var}'，该条件将不被满足。" )
            return False

    # 步骤 3: 将用户输入的逻辑关键字 (AND, OR, NOT) 转换为Python可执行的小写版本。
    # 使用正则表达式和 \b 单词边界来确保只替换独立的单词。
    eval_string = re.sub( r'\bAND\b', 'and', eval_string, flags = re.IGNORECASE )
    eval_string = re.sub( r'\bOR\b', 'or', eval_string, flags = re.IGNORECASE )
    eval_string = re.sub( r'\bNOT\b', 'not', eval_string, flags = re.IGNORECASE )

    # 步骤 4: 最终安全检查。
    # 在所有变量被数字替换后，字符串中不应再有任何非逻辑关键字的字母标识符。
    # 此步骤用于捕获任何潜在的、未被替换的变量或恶意注入的函数名。
    remaining_words = re.findall( r'[a-zA-Z_][a-zA-Z0-9_]*', eval_string )
    disallowed_words = [ word for word in remaining_words if word.lower() not in ( 'and', 'or', 'not' ) ]
    if disallowed_words:
        logger.error( f"在净化后发现潜在不安全的标识符: {disallowed_words}。"
                      f"处理后的字符串: '{eval_string}'" )
        return False

    # 步骤 5: 在一个高度受限的环境中安全地执行 `eval`。
    # `'__builtins__': {}` 会移除所有内置函数的访问权限，防止执行任何文件I/O或系统调用。
    # 第二个空字典确保没有额外的局部变量可以被访问。
    try:
        print( eval_string )
        result = bool( eval( eval_string, {
            '__builtins__': {}
        }, {} ) )
        return result
    except SyntaxError:
        logger.error( f"评估条件时发生语法错误。处理后的字符串: '{eval_string}'" )
        return False
    except Exception as e:
        logger.error( f"评估条件时发生未知错误: {e}。处理后的字符串: '{eval_string}'" )
        return False



class LineEditMutexManager:
    """
    通过类的方式管理多个布局中 QLineEdit 的互斥行为。

    此类将互斥逻辑封装起来，提供了更清晰的结构和更好的生命周期管理。
    实例化此类后，它会自动为指定布局中的 QLineEdit 设置信号/槽连接，
    确保在每个布局内（包括所有嵌套的子布局），只有一个 QLineEdit 可以包含文本。

    为了确保互斥逻辑在各种情况下（包括用户直接输入、编程方式设置文本、
    编辑完成等）都能被可靠触发，该管理器会同时监听 `textChanged` 和
    `editingFinished` 两个信号。

    该实现具有以下优点：
    1.  **深度查找**: 能够递归地遍历所有子布局，确保不遗漏任何深层嵌套的控件。
    2.  **封装性**: 将所有相关逻辑和状态（如信号处理器）封装在一个对象中。
    3.  **生命周期管理**: 实例持有对其创建的所有信号处理器的引用，
        防止它们被意外垃圾回收，从而确保连接的稳定性。
    4.  **可扩展性**: 易于添加新功能，如 `disconnect_all()` 方法。
    5.  **高可靠性**: 通过双信号监听，极大增强了触发的稳健性。

    Args:
        layouts (List[QLayout]):
            一个包含 PyQt5 布局容器的列表。管理器将为每个布局
            独立设置互斥规则。

    使用示例:
    ```python
    # self.my_layout1 是一个可能包含多层嵌套布局的容器
    # layout_list = [self.my_layout1, self.my_layout2]
    # # 创建一个管理器实例，它会自动完成所有设置
    # self.mutex_manager = LineEditMutexManager(layout_list)
    ```
    """

    def __init__( self, layouts: List[ QLayout ] ):
        # 存储所有动态创建的处理器，防止被垃圾回收
        self._handlers: List[ Callable[ [], None ] ] = []
        self._setup_connections( layouts )

    def _create_handler( self, source_edit: QLineEdit, group: List[ QLineEdit ] ) -> Callable[ [], None ]:
        """
        内部工厂方法，用于创建一个具有特定上下文的信号处理器（槽）。

        Args:
            source_edit (QLineEdit): 触发信号的源控件。
            group (List[QLineEdit]): 与源控件同组的所有 QLineEdit 控件的列表。

        Returns:
            Callable[[], None]: 一个可连接到信号的槽函数。
        """

        def handler() -> None:
            """当源控件文本改变时执行的实际逻辑。"""
            if not source_edit.text():
                return

            for edit in group:
                edit.blockSignals( True )
            try:
                for other_edit in group:
                    if other_edit is not source_edit:
                        other_edit.clear()
            finally:
                for edit in group:
                    edit.blockSignals( False )

        return handler

    def _find_line_edits_in_layout( self, layout: QLayout ) -> List[ QLineEdit ]:
        """
        以迭代方式（非递归）深度查找布局及其所有子布局中的所有 QLineEdit 控件。
        
        此方法使用 `collections.deque` 作为处理队列，实现了比递归更高效、更安全的遍历，
        可以处理任意复杂的嵌套布局而不会导致堆栈溢出。

        Args:
            layout (QLayout): 要开始搜索的根布局。

        Returns:
            List[QLineEdit]: 在布局层次结构中找到的所有 QLineEdit 控件的列表。
        """
        found_line_edits: List[ QLineEdit ] = []
        # 使用 deque 作为处理队列，初始时包含根布局
        items_to_search = deque( [ layout ] )

        while items_to_search:
            current_item = items_to_search.popleft()

            if isinstance( current_item, QLayout ):
                # 如果是布局，将其所有子项（控件或子布局）添加到队列中
                for i in range( current_item.count() ):
                    items_to_search.append( current_item.itemAt( i ).widget() or current_item.itemAt( i ).layout() )
            elif isinstance( current_item, QLineEdit ):
                # 如果是 QLineEdit，直接添加到结果列表
                if current_item not in found_line_edits:
                    found_line_edits.append( current_item )
            elif isinstance( current_item, QWidget ):
                # 如果是其他 QWidget，检查它是否包含子布局
                child_layout = current_item.layout()
                if child_layout is not None:
                    items_to_search.append( child_layout )

        return found_line_edits

    def _setup_connections( self, layouts: List[ QLayout ] ) -> None:
        """
        遍历所有布局，深度查找 QLineEdit 控件组并为其设置信号/槽连接。
        """
        for layout in layouts:
            # 使用辅助方法进行深度查找
            line_edit_group = self._find_line_edits_in_layout( layout )

            if len( line_edit_group ) < 2:
                continue

            for line_edit in line_edit_group:
                handler = self._create_handler( line_edit, line_edit_group )
                # 存储处理器的引用
                self._handlers.append( handler )

                # 连接两个信号以确保最高的可靠性:
                # 1. textChanged: 在文本内容发生任何变化时实时触发（包括编程方式）。
                # 2. editingFinished: 在用户完成编辑（按回车或失去焦点）时触发，作为一种可靠的回退机制。
                line_edit.textChanged.connect( handler )
                line_edit.editingFinished.connect( handler )



class TextEditManager:
    """
    一个高效管理和访问多个 QTextEdit 控件的工具类。

    该类通过在初始化时将 QTextEdit 列表转换为一个以 objectName 为键的字典，
    提供了对单个 QTextEdit 控件的快速、O(1) 时间复杂度的访问。
    它旨在简化从一组分散的UI控件中获取数据和实例的操作。

    Attributes:
        __text_edits (Dict[str, QTextEdit]):
            一个私有字典，用于存储 objectName 到 QTextEdit 实例的映射。
        __logger (Optional[Logger]):
            一个可选的日志记录器实例，用于在发生警告（如找不到控件）时输出信息。
    """

    def __init__( self, text_edits: List[ QTextEdit ], logger: Optional[ Logger ] = None ):
        """
        初始化 TextEditManager。

        此构造函数会遍历传入的 QTextEdit 列表，并构建一个内部字典，
        以便后续可以快速通过 objectName 访问任何一个控件。

        Args:
            text_edits (List[QTextEdit]):
                一个包含多个 QTextEdit 控件的列表，这些控件将被该管理器实例所管理。
            logger (Optional[Logger]):
                一个可选的日志记录器实例。如果提供，管理器将在找不到控件或
                某些控件因缺少 objectName 而被跳过时记录警告。
        """
        self.__text_edits: Dict[ str, QTextEdit ] = {
            te.objectName(): te
            for te in text_edits
            if te.objectName()
        }
        self.__logger = logger

        if self.__logger:
            skipped_count = len( text_edits ) - len( self.__text_edits )
            if skipped_count > 0:
                self.__logger.warning( f"{skipped_count} 个 QTextEdit 控件因缺少 objectName 而被初始化时跳过。" )

    def get_text( self, object_name: str ) -> str:
        """
        根据 objectName 获取指定 QTextEdit 控件的纯文本内容。

        这是一个便捷方法，用于安全地提取文本。如果找不到具有指定 objectName 的控件，
        它将返回一个空字符串，并（如果配置了）记录一条警告，从而避免了 `NoneType` 错误。

        Args:
            object_name (str): 目标 QTextEdit 控件的 objectName。

        Returns:
            str: 控件的纯文本内容。如果找不到控件，则返回空字符串。

        Usage Example:
            '''python
            # 假设:
            # text_edit1 = QTextEdit()
            # text_edit1.setObjectName("editor_main")
            # text_edit1.setPlainText("这是主要内容。")
            #
            # text_edit2 = QTextEdit()
            # text_edit2.setObjectName("editor_notes")
            # text_edit2.setPlainText("这是一些笔记。")
            #
            # my_logger = Logger()
            # manager = TextEditManager([text_edit1, text_edit2], logger=my_logger)
            #
            # # 获取文本
            # main_content = manager.get_text("editor_main")
            # print(f"主要内容: {main_content}")  # 输出: 主要内容: 这是主要内容。
            #
            # # 尝试获取不存在的控件的文本
            # non_existent_content = manager.get_text("editor_header")
            # print(f"不存在的内容: '{non_existent_content}'") # 输出: 不存在的内容: ''
                                                        # 同时会在日志中记录一条警告
            '''
        """
        widget = self.get_widget( object_name )
        if widget:
            return widget.toPlainText()
        return ""

    def get_widget( self, object_name: str ) -> Optional[ QTextEdit ]:
        """
        根据 objectName 获取 QTextEdit 控件的实例本身。

        此方法提供了对控件实例的直接访问，允许调用者执行更复杂的操作，
        例如修改内容、连接信号或更改样式。

        Args:
            object_name (str): 目标 QTextEdit 控件的 objectName。

        Returns:
            Optional[QTextEdit]: 如果找到，则返回 QTextEdit 控件实例；否则返回 None。

        Usage Example:
            '''python
            # (续上例)
            # 获取控件实例
            notes_editor = manager.get_widget("editor_notes")
            if notes_editor:
                # 直接操作控件
                notes_editor.append("添加新的一行笔记。")
                notes_editor.setStyleSheet("background-color: #f0f0f0;")
            '''
        """
        widget = self.__text_edits.get( object_name )
        if not widget and self.__logger:
            self.__logger.warning( f"在管理器中未找到 objectName 为 '{object_name}' 的 QTextEdit 控件。" )
        return widget



def set_labels_copy_on_click(
    layouts: Union[ QLayout, List[ QLayout ], List[ QLabel ] ], copy_map: Dict[ str, Any ], log_output: LogOutput,
    logger: Logger
):
    """
    为指定布局容器或QLabel列表内的所有 QLabel 控件添加点击复制功能。

    此函数会迭代遍历一个或多个 PyQt5 布局容器，或一个QLabel列表，找到其中所有的 QLabel 控件。
    然后，它会为每个 QLabel 动态地设置一个 `mousePressEvent` 事件处理器。
    当用户点击某个 QLabel 时，该处理器会：
    1. 获取该 QLabel 的 objectName。
    2. 使用 objectName 从 `copy_map` 字典中查找对应的值。
    3. 将查找到的值（转换为字符串）复制到系统剪切板。
    4. 在 UI 界面上通过 `log_output` 显示一条成功复制的消息。

    如果一个被点击的 QLabel 的 objectName 不在 `copy_map` 中，
    函数会通过 `logger` 记录一条警告，而不会执行任何操作，从而保证程序的健壮性。

    Args:
        layouts (Union[QLayout, List[QLayout], List[QLabel]]):
            一个或一系列 PyQt5 布局容器，或一个 QLabel 控件列表。
            函数将会在这些容器中搜索 QLabel，或直接对列表中的QLabel进行操作。
        copy_map (Dict[str, Any]):
            一个字典，键是 QLabel 的 objectName，值是当该 QLabel 被点击时需要复制到剪切板的内容。
        log_output (LogOutput):
            一个 LogOutput 类的实例，用于在 PyQt5 UI 界面中显示用户可见的日志信息。
        logger (Logger):
            一个标准的日志记录器实例，用于记录程序内部的、开发者关心的警告或错误信息。
    """
    q_labels_to_process: List[ QLabel ] = []
    _layouts = layouts if isinstance( layouts, list ) else [ layouts ]

    if not _layouts:
        return

    # --- 收集阶段 ---
    first_item = _layouts[ 0 ]
    if isinstance( first_item, QLabel ):
        # 输入是 QLabel 列表
        q_labels_to_process.extend( l for l in _layouts if isinstance( l, QLabel ) )
    elif isinstance( first_item, QLayout ):
        # 输入是 QLayout 列表
        layouts_to_process = deque( _layouts )
        while layouts_to_process:
            current_layout = layouts_to_process.popleft()
            for i in range( current_layout.count() ):
                item = current_layout.itemAt( i )
                if not item:
                    continue
                widget = item.widget()
                if isinstance( widget, QLabel ):
                    q_labels_to_process.append( widget )
                sub_layout = item.layout()
                if sub_layout:
                    layouts_to_process.append( sub_layout )
    else:
        logger.warning( f"set_labels_copy_on_click 接收到不支持的类型: {type(first_item)}" )
        return

    # --- 绑定阶段 ---
    def _on_label_click( label: QLabel, event: QMouseEvent ):
        """实际的事件处理逻辑。"""
        object_name = label.objectName()
        try:
            value_to_copy = copy_map[ object_name ]
            str_value = str( value_to_copy )
            clipboard = QApplication.clipboard()
            clipboard.setText( str_value )
            log_output.append( message = f"成功复制: '{str_value}'", color = Colors.SUCCESS )
        except KeyError:
            logger.warning( f"被点击的 QLabel (objectName='{object_name}') 在 copy_map 中没有对应的条目。" )
        except Exception as e:
            logger.error( f"为 QLabel (objectName='{object_name}') 复制值时发生未知错误: {e}" )
            traceback.print_exc()

    for label in q_labels_to_process:
        # 为了解决严格的类型检查问题，我们使用一个工厂函数来创建
        # 一个具有正确签名的事件处理器闭包。
        def create_handler( lbl: QLabel ):

            def handler( event: QMouseEvent ):
                _on_label_click( lbl, event )

            return handler

        label.mousePressEvent = create_handler( label )    # type: ignore
