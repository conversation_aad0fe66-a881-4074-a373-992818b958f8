type: geco
name: geco_sam_hq_vit_h-r20250330
provider: CVHub
display_name: GECO (ViT-huge)
encoder_data_path: https://github.com/CVHub520/X-AnyLabeling/releases/download/v3.0.0/GeCo_encoder_data.bin
encoder_model_path: https://github.com/CVHub520/X-AnyLabeling/releases/download/v3.0.0/GeCo_encoder.onnx
# encoder_model_path: https://github.com/CVHub520/X-AnyLabeling/releases/download/v3.0.0/GeCo_encoder_quant.onnx  (quantized)
decoder_model_path: https://github.com/CVHub520/X-AnyLabeling/releases/download/v3.0.0/GeCo_decoder.onnx
input_size: 1024
box_threshold: 4
