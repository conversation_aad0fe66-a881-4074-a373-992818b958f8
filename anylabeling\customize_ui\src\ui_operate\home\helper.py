from global_tools.ui_tools import constant
from global_tools.ui_tools import CheckBoxManager, InputCompleterCache, QPushButtonManager, QLabelManager, \
	LineEditManager, QProgressBarHelper, LogOutput
from global_tools.utils import Logger
from PyQt5.QtWidgets import QCheckBox, QWidget, QLayout
from PyQt5.QtCore import QObject
import logging
from typing import Dict, List, Union, Optional, Tuple
from collections import deque
import os
import json
import shutil
import random
import yaml


def check_lineedit( line_edit_manager: LineEditManager ):
	# Windows路径正则表达式 - 匹配形如C:\路径\文件夹的格式
	windows_path_regex = r'^[a-zA-Z]:\\(?:[^\\/:*?"<>|\r\n]+\\)*[^\\/:*?"<>|\r\n]*$'

	# 比例格式正则表达式 - 匹配形如85:15的格式
	ratio_regex = r'^\d+:\d+$'

	# 为lineEdit_45设置Windows路径验证器(之前已有的)
	line_edit_manager.set_input_validator(
		name="lineEdit_45", validator_type="regexp", pattern=constant.WINDOWS_PATH_REGEX
	)

	# 为lineEdit_6设置Windows路径验证器
	line_edit_manager.set_input_validator( name="lineEdit_6", validator_type="regexp", pattern=windows_path_regex )

	# 为lineEdit_7设置Windows路径验证器
	line_edit_manager.set_input_validator( name="lineEdit_7", validator_type="regexp", pattern=windows_path_regex )

	# 为lineEdit_9设置Windows路径验证器
	line_edit_manager.set_input_validator( name="lineEdit_9", validator_type="regexp", pattern=windows_path_regex )

	# 为lineEdit_8设置比例格式验证器
	line_edit_manager.set_input_validator( name="lineEdit_8", validator_type="regexp", pattern=ratio_regex )

	# 为lineEdit_11设置Windows路径验证器
	line_edit_manager.set_input_validator( name="lineEdit_11", validator_type="regexp", pattern=windows_path_regex )


class QCheckBoxFinder:
    """
    一个功能强大的工具类，用于在给定的PyQt5容器内递归查找、过滤并返回选中的QCheckBox控件。
    该类中的所有方法都是静态的，无需实例化即可调用。
    """

    @staticmethod
    def __get_all_checkboxes_from_item(item: Union[QWidget, QLayout]) -> List[QCheckBox]:
        """
        一个通用的私有方法，旨在从一个QWidget或QLayout中递归获取所有的QCheckBox子控件。
        该方法采用手动遍历布局项的方式。

        Args:
            item (Union[QWidget, QLayout]): 一个容器控件或一个布局。

        Returns:
            List[QCheckBox]: 找到的所有QCheckBox控件的列表。
        """
        checkboxes: List[QCheckBox] = []
        q = deque()
        
        layout_to_process = None
        if isinstance(item, QWidget):
            layout_to_process = item.layout()
        elif isinstance(item, QLayout):
            layout_to_process = item
        
        if layout_to_process:
            q.append(layout_to_process)
        # 对于没有布局的QWidget，直接查找其子QCheckBox
        elif isinstance(item, QWidget):
            checkboxes.extend(item.findChildren(QCheckBox))

        visited_layouts = set()

        while q:
            layout = q.popleft()
            if layout in visited_layouts:
                continue
            visited_layouts.add(layout)
            
            for i in range(layout.count()):
                layout_item = layout.itemAt(i)
                if layout_item is None:
                    continue

                widget = layout_item.widget()
                sub_layout = layout_item.layout()

                if isinstance(widget, QCheckBox):
                    checkboxes.append(widget)
                
                if widget is not None and widget.layout() is not None:
                    q.append(widget.layout())
                elif sub_layout is not None:
                    q.append(sub_layout)
        
        return checkboxes

    @staticmethod
    def find_checked_checkboxes(
            containers: Union[QWidget, QLayout, List[Union[QWidget, QLayout]]],
            exclude_texts: Optional[List[str]] = None
    ) -> Dict[str, List[str]]:
        """
        在指定的一个或多个容器（或布局）中，查找所有被选中的QCheckBox，并按容器分组返回，同时可根据文本排除特定的复选框。

        Args:
            containers (Union[QWidget, QLayout, List[Union[QWidget, QLayout]]]):
                一个或一系列PyQt5容器控件(QWidget)或布局(QLayout)。
            exclude_texts (Optional[List[str]], optional):
                一个用于排除的字符串列表。如果一个QCheckBox的文本在此列表中，它将被忽略。
                如果为 None 或空列表，则不会根据文本排除任何选中的QCheckBox。
                Defaults to None.

        Returns:
            Dict[str, List[str]]: 一个字典，键是容器控件的objectName，值是被选中的、未被排除的QCheckBox的objectName列表。
            如果一个容器没有符合条件的复选框，其对应的值将是一个空列表。
        """
        """
        使用示例:
        
        # 假设我们有以下的PyQt5控件结构:
        # parent_container = QWidget()
        # parent_container.setObjectName("ParentContainer")
        #
        # child_container1 = QWidget(parent_container)
        # child_container1.setObjectName("ChildContainer1")
        # cb1 = QCheckBox("Apple", child_container1)      # 将被排除
        # cb1.setObjectName("checkbox_apple")
        # cb1.setChecked(True)
        # cb2 = QCheckBox("Banana", child_container1)     # 将被包含
        # cb2.setObjectName("checkbox_banana")
        # cb2.setChecked(True)
        #
        # child_container2 = QWidget(parent_container)
        # child_container2.setObjectName("ChildContainer2")
        # cb3 = QCheckBox("Orange", child_container2)     # 未选中，不包含
        # cb3.setObjectName("checkbox_orange")
        # cb3.setChecked(False)
        # cb4 = QCheckBox("Apple", child_container2)      # 将被排除
        # cb4.setObjectName("checkbox_apple_in_2")
        # cb4.setChecked(True)
        # cb5 = QCheckBox("Grape", child_container2)      # 将被包含
        # cb5.setObjectName("checkbox_grape")
        # cb5.setChecked(True)
        #
        # # 直接通过类名调用静态方法，无需创建实例
        #
        # # 定义我们希望排除的复选框文本
        # fruits_to_exclude = ["Apple"]
        #
        # # 1. 测试单个容器，带文本排除
        # result1 = QCheckBoxFinder.find_checked_checkboxes(child_container1, exclude_texts=fruits_to_exclude)
        # print(f"Result for ChildContainer1 with exclusion: {result1}")
        # # 预期输出: Result for ChildContainer1 with exclusion: {'ChildContainer1': ['checkbox_banana']}
        #
        # # 2. 测试容器列表，带文本排除
        # all_containers = [child_container1, child_container2]
        # result2 = QCheckBoxFinder.find_checked_checkboxes(all_containers, exclude_texts=fruits_to_exclude)
        # print(f"Result for all containers with exclusion: {result2}")
        # # 预期输出: Result for all containers with exclusion: {'ChildContainer1': ['checkbox_banana'], 'ChildContainer2': ['checkbox_grape']}
        #
        # # 3. 测试单个容器，不带文本排除 (exclude_texts=None)
        # result3 = QCheckBoxFinder.find_checked_checkboxes(child_container2)
        # print(f"Result for ChildContainer2 without exclusion: {result3}")
        # # 预期输出: Result for ChildContainer2 without exclusion: {'ChildContainer2': ['checkbox_grape']}
        """
        if not isinstance(containers, list):
            containers = [containers]

        results: Dict[str, List[str]] = {}
        
        for item in containers:
            container_name = item.objectName()
            if not container_name:
                logging.warning(
                    f"Input item of type {type(item).__name__} lacks an objectName and will be skipped."
                )
                continue

            checkboxes = QCheckBoxFinder.__get_all_checkboxes_from_item(item)
            
            if container_name not in results:
                results[container_name] = []
            
            for checkbox in checkboxes:
                # 复选框必须被选中，且其文本不能在排除列表中（或者没有提供排除列表）
                if checkbox.isChecked() and (not exclude_texts or checkbox.text() not in exclude_texts):
                    if checkbox.objectName() not in results[container_name]:
                        results[container_name].append(checkbox.objectName())

        return results


class DatasetExporter:
	"""
	数据集转换器，用于将标注数据转换为YOLO11 Segment训练模型格式。

	该类处理以下功能：
	1. 收集输入文件夹中的图像和JSON文件
	2. 读取JSON文件获取标注信息
	3. 将标注转换为YOLO格式
	4. 按比例划分训练集和验证集
	5. 根据用户选择处理文件

	使用示例:
	```
	# 在HomeUiOperate.__export_dataset_button_function中使用
	exporter = DatasetExporter(
		self.__line_edit_manager,
		self.__checkbox_manager,
		self.__log_output,
		self.__logger,
		self.__label_manager
	)
	exporter.export_dataset()
	```
	"""

	def __init__(
			self,
			line_edit_manager: LineEditManager,
			checkbox_manager: CheckBoxManager,
			log_output: LogOutput = None,
			logger: Logger = None,
			label_manager: QLabelManager = None
	):
		"""
		初始化数据集转换器。

		Args:
			line_edit_manager: 用于获取输入文本的LineEditManager实例
			checkbox_manager: 用于获取复选框状态的CheckBoxManager实例
			log_output: 用于输出日志的LogOutput实例，可选
			logger: 用于记录日志的Logger实例，可选
			label_manager: 用于更新UI标签的LabelManager实例，可选
		"""
		self.__line_edit_manager = line_edit_manager
		self.__checkbox_manager = checkbox_manager
		self.__log_output = log_output
		self.__logger = logger
		self.__label_manager = label_manager



	def __log( self, message: str, color: str = None, level: str = "info" ):
		"""
		打印日志信息。

		Args:
			message: 要打印的信息
			color: 输出颜色，可选
			level: 日志级别，可选，默认为info
		"""
		# 判断是否应该输出到UI界面
		# 1. 带颜色的日志（通常表示重要信息）都输出到UI界面
		# 2. warning和error级别的日志都输出到UI界面
		# 3. 关键步骤的info日志也输出到UI界面（通常是进度相关的信息）
		should_log_to_ui = (
				color is not None or  # 有颜色的日志都显示到UI
				level.lower() == "warning" or
				level.lower() == "error"
		)

		# 处理log_output记录UI日志，只输出重要的日志
		if self.__log_output and should_log_to_ui:
			self.__log_output.append( message, color=color )

		# 处理logger记录控制台日志，所有级别都记录
		if self.__logger:
			if level.lower() == "debug":
				self.__logger.debug( message )
			elif level.lower() == "warning":
				self.__logger.warning( message )
			elif level.lower() == "error":
				self.__logger.error( message )
			else:
				self.__logger.info( message )
		# 如果两者都不存在，则使用print
		elif not self.__log_output:
			print( message )

	def __update_progress_label( self, label_name: str, current: int, total: int ):
		"""
		更新进度标签显示。

		Args:
			label_name: 标签对象名称
			current: 当前进度值
			total: 总进度值
		"""
		try:
			if not self.__label_manager:
				return

			# if not self.__label_manager.has_label( label_name ):
			# 	self.__log( f"找不到标签: {label_name}", color="yellow", level="warning" )
			# 	return

			progress_text = f"{current}/{total}"
			self.__label_manager.set_text( label_name, progress_text )

			# 使用淡入淡出动画效果提示用户
			self.__label_manager.start_fade_animation( label_name, duration_ms=200 )

			self.__log( f"更新进度显示 {label_name}: {progress_text}", level="debug" )
		except Exception as e:
			self.__log( f"更新进度标签时出错: {str( e )}", color="red", level="error" )
		# 此处不抛出异常，确保即使标签更新失败，主流程也能继续

	def __get_input_folder_path( self ) -> str:
		"""
		获取输入文件夹路径。

		Returns:
			输入文件夹路径字符串
		"""
		path = self.__line_edit_manager.get_line_edit( "lineEdit_6" ).text()
		self.__log( f"获取输入文件夹路径: {path}", level="debug" )
		return path

	def __get_output_folder_path( self ) -> str:
		"""
		获取输出文件夹路径。

		Returns:
			输出文件夹路径字符串
		"""
		path = self.__line_edit_manager.get_line_edit( "lineEdit_7" ).text()
		self.__log( f"获取输出文件夹路径: {path}", level="debug" )
		return path

	def __get_labels_file_path( self ) -> str:
		"""
		获取标签文件路径。

		Returns:
			标签文件路径字符串
		"""
		path = self.__line_edit_manager.get_line_edit( "lineEdit_9" ).text()
		self.__log( f"获取标签文件路径: {path}", level="debug" )
		return path

	def __get_split_ratio( self ) -> Tuple[ float, float ]:
		"""
		获取划分比例。

		Returns:
			包含训练集和验证集比例的元组，如(0.85, 0.15)
		"""
		ratio_text = self.__line_edit_manager.get_line_edit( "lineEdit_8" ).text()
		train_ratio, val_ratio = map( int, ratio_text.split( ':' ) )
		total = train_ratio + val_ratio
		result = (train_ratio / total, val_ratio / total)
		self.__log( f"获取划分比例: {ratio_text} -> {result}", level="debug" )
		return result

	def __should_delete_source_files( self ) -> bool:
		"""
		判断是否删除源文件。

		Returns:
			如果勾选了删除选项，则返回True；否则返回False
		"""
		checkbox = self.__checkbox_manager.get_checkbox_by_object_name( "rotate_7" )
		result = checkbox.isChecked() if checkbox else False
		self.__log( f"是否删除源文件: {result}", level="debug" )
		return result

	def __should_clear_output_folders( self ) -> bool:
		"""
		判断是否清空输出文件夹。

		Returns:
			如果勾选了清空选项，则返回True；否则返回False
		"""
		checkbox = self.__checkbox_manager.get_checkbox_by_object_name( "rotate_6" )
		result = checkbox.isChecked() if checkbox else False
		self.__log( f"是否清空输出文件夹: {result}", level="debug" )
		return result

	def __collect_files( self ) -> List[ Tuple[ str, str ] ]:
		"""
		收集输入文件夹中的图像和JSON文件。

		Returns:
			包含图像文件和对应JSON文件路径的列表，如[(image_path, json_path), ...]
		"""
		input_folder = self.__get_input_folder_path()
		self.__log( f"开始从 {input_folder} 收集文件", color="blue", level="info" )

		if not os.path.exists( input_folder ):
			self.__log( f"输入文件夹不存在: {input_folder}", color="red", level="error" )
			return [ ]

		if not os.path.isdir( input_folder ):
			self.__log( f"指定的路径不是一个文件夹: {input_folder}", color="red", level="error" )
			return [ ]

		image_extensions = ('.jpg', '.jpeg', '.png', '.bmp', '.tif', '.tiff')
		file_pairs = [ ]

		# 统计数据
		total_image_files = 0
		total_json_files = 0
		valid_pairs = 0

		try:
			for filename in os.listdir( input_folder ):
				file_path = os.path.join( input_folder, filename )

				# 跳过非文件
				if not os.path.isfile( file_path ):
					continue

				base_name, ext = os.path.splitext( filename )
				ext = ext.lower()  # 转为小写以进行不区分大小写的比较

				# 检查是否是图像文件
				if ext in image_extensions:
					total_image_files += 1

					# 查找对应的JSON文件
					json_path = os.path.join( input_folder, base_name + '.json' )
					if os.path.exists( json_path ) and os.path.isfile( json_path ):
						total_json_files += 1
						valid_pairs += 1
						file_pairs.append( (file_path, json_path) )
						self.__log( f"找到文件对: {file_path} - {json_path}", level="debug" )

			self.__log(
				f"扫描结果: {total_image_files} 个图像文件, {total_json_files} 个JSON文件, {valid_pairs} 对有效配对",
				color="blue"
			)

		except Exception as e:
			self.__log( f"扫描文件时出错: {str( e )}", color="red", level="error" )
			return [ ]

		self.__log( f"找到 {len( file_pairs )} 对图像和JSON文件", color="blue" )
		return file_pairs

	def __read_labels_file( self ) -> List[ str ]:
		"""
		读取标签文件内容。

		Returns:
			标签列表，如['person', 'car', ...]
		"""
		labels_file = self.__get_labels_file_path()

		self.__log( f"读取标签文件: {labels_file}", color="blue", level="info" )

		if not os.path.exists( labels_file ):
			self.__log( f"标签文件不存在: {labels_file}", color="red", level="error" )
			return [ ]

		if not os.path.isfile( labels_file ):
			self.__log( f"指定的标签路径不是一个文件: {labels_file}", color="red", level="error" )
			return [ ]

		try:
			with open( labels_file, 'r', encoding='utf-8' ) as f:
				# 读取所有行并过滤空行
				lines = f.readlines()
				labels = [ ]

				for i, line in enumerate( lines, 1 ):
					line = line.strip()
					if line:  # 非空行
						if line in labels:
							self.__log(
								f"警告: 标签文件中发现重复标签 '{line}' (第{i}行)", color="yellow", level="warning"
							)
						else:
							labels.append( line )

			if not labels:
				self.__log( "警告: 标签文件为空或只包含空行", color="yellow", level="warning" )

			self.__log( f"读取到 {len( labels )} 个标签: {', '.join( labels )}", color="blue" )
			return labels

		except Exception as e:
			self.__log( f"读取标签文件出错: {labels_file}, 错误: {str( e )}", color="red", level="error" )
			return [ ]

	def __read_json_file( self, json_path: str ) -> List[ Dict ]:
		"""
		读取JSON文件中的标注信息。

		Args:
			json_path: JSON文件路径

		Returns:
			包含标注信息的列表
		"""
		try:
			with open( json_path, 'r', encoding='utf-8' ) as f:
				data = json.load( f )

			shapes = data.get( 'shapes', [ ] )
			self.__log( f"从 {json_path} 读取到 {len( shapes )} 个标注形状", level="debug" )
			return shapes
		except Exception as e:
			self.__log( f"读取JSON文件出错: {json_path}, 错误: {str( e )}", color="red", level="error" )
			return [ ]

	def __normalize_coordinates( self, points: List[ List[ float ] ], image_width: int, image_height: int ) -> List[
		float ]:
		"""
		将坐标归一化为YOLO格式。

		Args:
			points: 多边形坐标点列表，如[[x1, y1], [x2, y2], ...]
			image_width: 图像宽度
			image_height: 图像高度

		Returns:
			归一化后的坐标列表，如[x1, y1, x2, y2, ...]
		"""
		self.__log( f"归一化坐标点 (图像尺寸: {image_width}x{image_height})", level="debug" )

		# 检查宽高是否有效
		if image_width <= 0 or image_height <= 0:
			self.__log(
				f"错误: 图像尺寸无效 ({image_width}x{image_height})，无法进行坐标归一化", color="red", level="error"
			)
			return [ ]

		normalized = [ ]
		for point in points:
			# 归一化x坐标
			normalized.append( point[ 0 ] / image_width )
			# 归一化y坐标
			normalized.append( point[ 1 ] / image_height )
		return normalized

	def __convert_to_yolo_format( self, file_pairs: List[ Tuple[ str, str ] ], labels: List[ str ] ) -> List[
		Tuple[ str, List[ str ] ] ]:
		"""
		将标注信息转换为YOLO格式。

		Args:
			file_pairs: 图像和JSON文件对列表
			labels: 标签列表

		Returns:
			包含图像路径和对应YOLO格式标注的列表，如[(image_path, ["class_idx x1 y1 x2 y2..."]), ...]
		"""
		self.__log( f"开始将 {len( file_pairs )} 对文件转换为YOLO格式", color="blue", level="info" )

		yolo_data = [ ]

		for image_path, json_path in file_pairs:
			shapes = self.__read_json_file( json_path )

			if not shapes:
				self.__log( f"警告: JSON文件没有标注信息: {json_path}", color="yellow", level="warning" )
				continue

			# 获取图像尺寸，从JSON文件中读取
			with open( json_path, 'r', encoding='utf-8' ) as f:
				data = json.load( f )
				image_width = data.get( 'imageWidth', 0 )
				image_height = data.get( 'imageHeight', 0 )

			if image_width <= 0 or image_height <= 0:
				self.__log( f"警告: 无法获取图像尺寸: {image_path}", color="yellow", level="warning" )
				continue

			yolo_annotations = [ ]

			for shape in shapes:
				label_name = shape.get( 'label' )
				points = shape.get( 'points', [ ] )

				# 检查标签是否在预定义列表中
				if label_name not in labels:
					self.__log( f"警告: 标签 '{label_name}' 不在标签列表中，跳过", color="yellow", level="warning" )
					continue

				# 获取标签索引
				label_idx = labels.index( label_name )

				# 归一化坐标
				normalized_coords = self.__normalize_coordinates( points, image_width, image_height )

				# 格式化为YOLO格式字符串: "label_idx x1 y1 x2 y2 ..."
				coords_str = ' '.join( f"{coord:.6f}" for coord in normalized_coords )
				yolo_annotation = f"{label_idx} {coords_str}"

				yolo_annotations.append( yolo_annotation )

			self.__log(
				f"文件 {os.path.basename( image_path )} 转换为 {len( yolo_annotations )} 个YOLO标注", level="debug"
			)

			yolo_data.append( (image_path, yolo_annotations) )

		self.__log( f"转换了 {len( yolo_data )} 个文件到YOLO格式", color="blue" )
		return yolo_data

	def __split_dataset( self, yolo_data: List[ Tuple[ str, List[ str ] ] ] ) -> Tuple[
		List[ Tuple[ str, List[ str ] ] ], List[ Tuple[ str, List[ str ] ] ] ]:
		"""
		按比例划分训练集和验证集。

		Args:
			yolo_data: 包含图像路径和YOLO格式标注的列表

		Returns:
			训练集和验证集的元组
		"""
		self.__log( f"开始划分数据集，共 {len( yolo_data )} 个样本", color="blue", level="info" )

		train_ratio, _ = self.__get_split_ratio()

		# 随机打乱数据
		random.shuffle( yolo_data )

		# 计算训练集大小
		train_size = int( len( yolo_data ) * train_ratio )

		# 划分数据
		train_data = yolo_data[ :train_size ]
		val_data = yolo_data[ train_size: ]

		self.__log( f"数据集划分: 训练集 {len( train_data )}个, 验证集 {len( val_data )}个", color="blue" )
		return train_data, val_data

	def __prepare_output_dirs( self ) -> Tuple[ str, str, str, str ]:
		"""
		准备输出目录结构。

		Returns:
			训练集和验证集的图像和标签目录元组，如(train_images_dir, train_labels_dir, val_images_dir, val_labels_dir)
		"""
		output_folder = self.__get_output_folder_path()
		self.__log( f"准备输出目录结构: {output_folder}", color="blue", level="info" )

		# 创建目录结构
		train_images_dir = os.path.join( output_folder, 'images', 'train' )
		train_labels_dir = os.path.join( output_folder, 'labels', 'train' )
		val_images_dir = os.path.join( output_folder, 'images', 'val' )
		val_labels_dir = os.path.join( output_folder, 'labels', 'val' )

		# 如果选择清空输出文件夹
		if self.__should_clear_output_folders():
			for dir_path in [ train_images_dir, train_labels_dir, val_images_dir, val_labels_dir ]:
				if os.path.exists( dir_path ):
					shutil.rmtree( dir_path )
					self.__log( f"已清空目录: {dir_path}", color="blue" )

		# 确保目录存在
		for dir_path in [ train_images_dir, train_labels_dir, val_images_dir, val_labels_dir ]:
			os.makedirs( dir_path, exist_ok=True )
			self.__log( f"确保目录存在: {dir_path}", level="debug" )

		return train_images_dir, train_labels_dir, val_images_dir, val_labels_dir

	def __save_dataset(
			self, train_data: List[ Tuple[ str, List[ str ] ] ], val_data: List[ Tuple[ str, List[ str ] ] ]
	):
		"""
		保存处理后的数据集。

		Args:
			train_data: 训练集数据
			val_data: 验证集数据
		"""
		self.__log( "开始保存数据集", color="blue", level="info" )

		train_images_dir, train_labels_dir, val_images_dir, val_labels_dir = self.__prepare_output_dirs()

		# 处理函数，用于训练集和验证集
		def process_dataset( dataset, images_dir, labels_dir, dataset_type, label_name=None ):
			self.__log( f"处理{dataset_type}数据集: {len( dataset )}个文件", color="blue", level="info" )

			total_items = len( dataset )

			# 初始化进度显示
			if label_name and self.__label_manager:
				self.__update_progress_label( label_name, 0, total_items )

			for i, (image_path, annotations) in enumerate( dataset, 1 ):
				# 更新处理进度
				if label_name and self.__label_manager:  # 每次都更新UI
					self.__update_progress_label( label_name, i, total_items )

				# 复制图像文件
				image_filename = os.path.basename( image_path )
				target_image_path = os.path.join( images_dir, image_filename )
				shutil.copy2( image_path, target_image_path )

				# 保存标注文件
				base_name = os.path.splitext( image_filename )[ 0 ]
				target_label_path = os.path.join( labels_dir, f"{base_name}.txt" )

				with open( target_label_path, 'w', encoding='utf-8' ) as f:
					f.write("\n".join(annotations))

				self.__log(
					f"保存{dataset_type}文件: {image_filename} -> {target_image_path}, 标签: {target_label_path}",
					level="debug"
				)

			# 更新最终进度
			if label_name and self.__label_manager:
				self.__update_progress_label( label_name, total_items, total_items )

		# 处理训练集，使用label_34显示进度
		process_dataset( train_data, train_images_dir, train_labels_dir, "训练集", "label_34" )
		self.__log( f"已保存训练集: {len( train_data )}个文件", color="green" )

		# 处理验证集，使用label_37显示进度
		process_dataset( val_data, val_images_dir, val_labels_dir, "验证集", "label_37" )
		self.__log( f"已保存验证集: {len( val_data )}个文件", color="green" )

	def __delete_source_files_if_needed( self, file_pairs: List[ Tuple[ str, str ] ] ):
		"""
		如果用户选择，删除源文件。

		Args:
			file_pairs: 图像和JSON文件对列表
		"""
		if not self.__should_delete_source_files():
			return

		self.__log( f"删除源文件: {len( file_pairs )} 对文件", color="blue", level="info" )

		success_count = 0
		fail_count = 0

		for image_path, json_path in file_pairs:
			try:
				os.remove( image_path )
				os.remove( json_path )
				success_count += 1
				self.__log( f"删除文件: {image_path}, {json_path}", level="debug" )
			except Exception as e:
				fail_count += 1
				self.__log( f"删除文件失败: {image_path}, 错误: {str( e )}", color="red", level="error" )

		self.__log( f"已删除 {success_count} 对源文件, 失败 {fail_count} 对", color="blue" )
		self.__log( f"删除源文件完成: 成功 {success_count} 对, 失败 {fail_count} 对", color="blue", level="info" )

	def export_dataset( self ) -> bool:
		"""
		执行数据集导出流程。

		Returns:
			操作成功返回True，否则返回False
		"""
		try:
			self.__log( "开始执行数据集导出流程", color="blue", level="info" )

			# 获取输入输出路径检查
			input_path = self.__get_input_folder_path()
			output_path = self.__get_output_folder_path()
			labels_path = self.__get_labels_file_path()

			if not (input_path and output_path and labels_path):
				self.__log( "输入、输出或标签文件路径不能为空", color="red", level="error" )
				return False

			if not os.path.exists( input_path ):
				self.__log( f"输入文件夹不存在: {input_path}", color="red", level="error" )
				return False

			if not os.path.exists( labels_path ):
				self.__log( f"标签文件不存在: {labels_path}", color="red", level="error" )
				return False

			# 1. 收集文件对
			self.__log( "开始收集文件...", color="blue" )
			file_pairs = self.__collect_files()
			if not file_pairs:
				self.__log( "没有找到有效的图像和JSON文件对", color="red", level="error" )
				return False

			# 2. 读取标签文件
			self.__log( "读取标签文件...", color="blue" )
			labels = self.__read_labels_file()
			if not labels:
				self.__log( "标签文件为空或读取失败", color="red", level="error" )
				return False

			# 3. 转换为YOLO格式
			self.__log( "转换标注为YOLO格式...", color="blue" )
			yolo_data = self.__convert_to_yolo_format( file_pairs, labels )
			if not yolo_data:
				self.__log( "转换标注数据失败", color="red", level="error" )
				return False

			# 4. 划分数据集
			self.__log( "划分训练集和验证集...", color="blue" )
			train_data, val_data = self.__split_dataset( yolo_data )

			# 5. 保存数据集
			self.__log( "保存数据集...", color="blue" )
			self.__save_dataset( train_data, val_data )

			# 6. 如果需要，删除源文件
			if self.__should_delete_source_files():
				self.__log( "删除源文件...", color="blue" )
				self.__delete_source_files_if_needed( file_pairs )

			self.__log( "数据集导出完成!", color="green" )
			return True

		except Exception as e:
			self.__log( f"导出过程中发生错误: {str( e )}", color="red", level="error" )
			import traceback
			self.__log( traceback.format_exc(), color="red", level="error" )
			return False

class AnnotationCounter:
	"""
	标注对象统计器，用于统计指定目录中所有标注文件的标注对象总数量。
	
	该类处理以下功能：
	1. 从输入路径中收集所有的图像和对应的JSON标注文件对
	2. 统计所有JSON标注文件中的标注对象总数量
	3. 更新UI中的相关标签显示统计结果
	
	使用示例:
	```python
	# 在HomeUiOperate中使用
	counter = AnnotationCounter(
		self.__line_edit_manager,
		self.__log_output,
		self.__logger,
		self.__label_manager
	)
	counter.count_annotations()
	```
	"""
	
	def __init__(
			self,
			line_edit_manager: LineEditManager,
			log_output: LogOutput = None,
			logger: Logger = None,
			label_manager: QLabelManager = None
	):
		"""
		初始化标注对象统计器。
		
		Args:
			line_edit_manager: 用于获取输入文本的LineEditManager实例
			log_output: 用于输出日志的LogOutput实例，可选
			logger: 用于记录日志的Logger实例，可选
			label_manager: 用于更新UI标签的LabelManager实例，可选
		"""
		self.__line_edit_manager = line_edit_manager
		self.__log_output = log_output
		self.__logger = logger
		self.__label_manager = label_manager
		
	def __log(self, message: str, color: str = None, level: str = "info"):
		"""
		打印日志信息。
		
		Args:
			message: 要打印的信息
			color: 输出颜色，可选
			level: 日志级别，可选，默认为info
		"""
		# 判断是否应该输出到UI界面
		# 1. 带颜色的日志（通常表示重要信息）都输出到UI界面
		# 2. warning和error级别的日志都输出到UI界面
		should_log_to_ui = (
			color is not None or  # 有颜色的日志都显示到UI
			level.lower() == "warning" or
			level.lower() == "error"
		)
		
		# 处理log_output记录UI日志，只输出重要的日志
		if self.__log_output and should_log_to_ui:
			self.__log_output.append(message, color=color)
			
		# 处理logger记录控制台日志，所有级别都记录
		if self.__logger:
			if level.lower() == "debug":
				self.__logger.debug(message)
			elif level.lower() == "warning":
				self.__logger.warning(message)
			elif level.lower() == "error":
				self.__logger.error(message)
			else:
				self.__logger.info(message)
		# 如果两者都不存在，则使用print
		elif not self.__log_output:
			print(message)
	
	def __update_result_label(self, total_count: int):
		"""
		更新结果标签显示。
		
		Args:
			total_count: 标注对象总数量
		"""
		try:
			if not self.__label_manager:
				return
				
			result_text = f"总标注对象: {total_count}"
			self.__label_manager.set_text("label_69", result_text)
			
			# 使用淡入淡出动画效果提示用户
			self.__label_manager.start_fade_animation("label_69", duration_ms=200)
			
			self.__log(f"更新结果显示: {result_text}", level="debug")
		except Exception as e:
			self.__log(f"更新结果标签时出错: {str(e)}", color="red", level="error")
			
	def __update_valid_files_label(self, valid_files_count: int):
		"""
		更新有效文件数量标签显示。
		
		Args:
			valid_files_count: 有效文件数量
		"""
		try:
			if not self.__label_manager:
				return
				
			result_text = f"有效文件数量: {valid_files_count}"
			self.__label_manager.set_text("label_178", result_text)
			
			# 使用淡入淡出动画效果提示用户
			self.__label_manager.start_fade_animation("label_178", duration_ms=200)
			
			self.__log(f"更新有效文件数量显示: {result_text}", level="debug")
		except Exception as e:
			self.__log(f"更新有效文件数量标签时出错: {str(e)}", color="red", level="error")
			
	def __get_input_folder_path(self) -> str:
		"""
		获取输入文件夹路径。
		
		Returns:
			输入文件夹路径字符串
		"""
		path = self.__line_edit_manager.get_line_edit("lineEdit_6").text()
		self.__log(f"获取输入文件夹路径: {path}", level="debug")
		return path
		
	def __collect_files(self) -> List[Tuple[str, str]]:
		"""
		收集输入文件夹中的图像和JSON文件。
		
		Returns:
			包含图像文件和对应JSON文件路径的列表，如[(image_path, json_path), ...]
		"""
		input_folder = self.__get_input_folder_path()
		self.__log(f"开始从 {input_folder} 收集文件", color="blue", level="info")
		
		if not os.path.exists(input_folder):
			self.__log(f"输入文件夹不存在: {input_folder}", color="red", level="error")
			return []
			
		if not os.path.isdir(input_folder):
			self.__log(f"指定的路径不是一个文件夹: {input_folder}", color="red", level="error")
			return []
			
		image_extensions = ('.jpg', '.jpeg', '.png', '.bmp', '.tif', '.tiff')
		file_pairs = []
		
		# 统计数据
		total_image_files = 0
		total_json_files = 0
		valid_pairs = 0
		
		try:
			for filename in os.listdir(input_folder):
				file_path = os.path.join(input_folder, filename)
				
				# 跳过非文件
				if not os.path.isfile(file_path):
					continue
					
				base_name, ext = os.path.splitext(filename)
				ext = ext.lower()  # 转为小写以进行不区分大小写的比较
				
				# 检查是否是图像文件
				if ext in image_extensions:
					total_image_files += 1
					
					# 查找对应的JSON文件
					json_path = os.path.join(input_folder, base_name + '.json')
					if os.path.exists(json_path) and os.path.isfile(json_path):
						total_json_files += 1
						valid_pairs += 1
						file_pairs.append((file_path, json_path))
						self.__log(f"找到文件对: {file_path} - {json_path}", level="debug")
						
			self.__log(
				f"扫描结果: {total_image_files} 个图像文件, {total_json_files} 个JSON文件, {valid_pairs} 对有效配对",
				color="blue"
			)
			
		except Exception as e:
			self.__log(f"扫描文件时出错: {str(e)}", color="red", level="error")
			return []
			
		self.__log(f"找到 {len(file_pairs)} 对图像和JSON文件", color="blue")
		return file_pairs
		
	def __read_json_file(self, json_path: str) -> List[Dict]:
		"""
		读取JSON文件中的标注信息。
		
		Args:
			json_path: JSON文件路径
			
		Returns:
			包含标注信息的列表
		"""
		try:
			with open(json_path, 'r', encoding='utf-8') as f:
				data = json.load(f)
				
			shapes = data.get('shapes', [])
			self.__log(f"从 {json_path} 读取到 {len(shapes)} 个标注形状", level="debug")
			return shapes
		except Exception as e:
			self.__log(f"读取JSON文件出错: {json_path}, 错误: {str(e)}", color="red", level="error")
			return []
			
	def count_annotations(self) -> int:
		"""
		统计标注对象的总数量。
		
		Returns:
			标注对象总数量
		"""
		self.__log("开始统计标注对象总数量...", color="blue", level="info")
		
		# 收集文件对
		file_pairs = self.__collect_files()
		if not file_pairs:
			self.__log("未找到有效的图像和JSON文件对", color="yellow", level="warning")
			self.__update_result_label(0)
			self.__update_valid_files_label(0)
			return 0
			
		# 更新有效文件数量标签
		valid_files_count = len(file_pairs)
		self.__update_valid_files_label(valid_files_count)
		
		# 统计标注对象总数
		total_count = 0
		processed_count = 0
		total_files = len(file_pairs)
		
		self.__log(f"开始处理 {total_files} 个文件...", color="blue", level="info")
		
		try:
			for image_path, json_path in file_pairs:
				processed_count += 1
				
				# 读取JSON文件
				shapes = self.__read_json_file(json_path)
				if shapes:
					total_count += len(shapes)
					
				# 每处理10个文件或者处理完所有文件时，记录一次进度
				if processed_count % 10 == 0 or processed_count == total_files:
					self.__log(
						f"已处理 {processed_count}/{total_files} 个文件，当前共找到 {total_count} 个标注对象",
						level="info"
					)
					
		except Exception as e:
			self.__log(f"统计标注对象时出错: {str(e)}", color="red", level="error")
			
		# 更新结果标签
		self.__update_result_label(total_count)
		
		self.__log(f"统计完成，共找到 {total_count} 个标注对象，有效文件数量: {valid_files_count}", color="green", level="info")
		return total_count

class YAMLConfigGenerator:
	"""
	YAML配置文件生成器，用于生成YOLO训练所需的配置文件。
	
	该类处理以下功能：
	1. 获取输出路径、配置文件路径和标签文件路径
	2. 读取标签文件内容
	3. 生成YAML配置文件
	4. 更新UI中的相关标签显示生成结果
	
	使用示例:
	```python
	# 在HomeUiOperate中使用
	generator = YAMLConfigGenerator(
		self.__line_edit_manager,
		self.__log_output,
		self.__logger,
		self.__label_manager
	)
	generator.generate_yaml_config()
	```
	"""
	
	def __init__(
			self,
			line_edit_manager: LineEditManager,
			log_output: LogOutput = None,
			logger: Logger = None,
			label_manager: QLabelManager = None
	):
		"""
		初始化YAML配置文件生成器。
		
		Args:
			line_edit_manager: 用于获取输入文本的LineEditManager实例
			log_output: 用于输出日志的LogOutput实例，可选
			logger: 用于记录日志的Logger实例，可选
			label_manager: 用于更新UI标签的LabelManager实例，可选
		"""
		self.__line_edit_manager = line_edit_manager
		self.__log_output = log_output
		self.__logger = logger
		self.__label_manager = label_manager
		
	def __log(self, message: str, color: str = None, level: str = "info"):
		"""
		打印日志信息。
		
		Args:
			message: 要打印的信息
			color: 输出颜色，可选
			level: 日志级别，可选，默认为info
		"""
		# 判断是否应该输出到UI界面
		# 1. 带颜色的日志（通常表示重要信息）都输出到UI界面
		# 2. warning和error级别的日志都输出到UI界面
		should_log_to_ui = (
			color is not None or  # 有颜色的日志都显示到UI
			level.lower() == "warning" or
			level.lower() == "error"
		)
		
		# 处理log_output记录UI日志，只输出重要的日志
		if self.__log_output and should_log_to_ui:
			self.__log_output.append(message, color=color)
			
		# 处理logger记录控制台日志，所有级别都记录
		if self.__logger:
			if level.lower() == "debug":
				self.__logger.debug(message)
			elif level.lower() == "warning":
				self.__logger.warning(message)
			elif level.lower() == "error":
				self.__logger.error(message)
			else:
				self.__logger.info(message)
		# 如果两者都不存在，则使用print
		elif not self.__log_output:
			print(message)
	
	def __update_result_label(self, config_file_name: str):
		"""
		更新结果标签显示。
		
		Args:
			config_file_name: 生成的配置文件名
		"""
		try:
			if not self.__label_manager:
				return
				
			result_text = f"已生成: {config_file_name}"
			self.__label_manager.set_text("label_51", result_text)
			
			# 使用淡入淡出动画效果提示用户
			self.__label_manager.start_fade_animation("label_51", duration_ms=200)
			
			self.__log(f"更新结果显示: {result_text}", level="debug")
		except Exception as e:
			self.__log(f"更新结果标签时出错: {str(e)}", color="red", level="error")
			
	def __get_output_path(self) -> str:
		"""
		获取标注数据输出路径。
		
		Returns:
			标注数据输出路径字符串
		"""
		path = self.__line_edit_manager.get_line_edit("lineEdit_7").text()
		self.__log(f"获取标注数据输出路径: {path}", level="debug")
		return path
		
	def __get_config_path(self) -> str:
		"""
		获取配置文件路径。
		
		Returns:
			配置文件路径字符串
		"""
		path = self.__line_edit_manager.get_line_edit("lineEdit_11").text()
		self.__log(f"获取配置文件路径: {path}", level="debug")
		return path
		
	def __get_labels_file_path(self) -> str:
		"""
		获取标签文件路径。
		
		Returns:
			标签文件路径字符串
		"""
		path = self.__line_edit_manager.get_line_edit("lineEdit_9").text()
		self.__log(f"获取标签文件路径: {path}", level="debug")
		return path
		
	def __read_labels_file(self) -> List[str]:
		"""
		读取标签文件内容。
		
		Returns:
			标签列表，如['person', 'car', ...]
		"""
		labels_file = self.__get_labels_file_path()

		self.__log(f"读取标签文件: {labels_file}", color="blue", level="info")

		if not os.path.exists(labels_file):
			self.__log(f"标签文件不存在: {labels_file}", color="red", level="error")
			return []

		if not os.path.isfile(labels_file):
			self.__log(f"指定的标签路径不是一个文件: {labels_file}", color="red", level="error")
			return []

		try:
			with open(labels_file, 'r', encoding='utf-8') as f:
				# 读取所有行并过滤空行
				lines = f.readlines()
				labels = []

				for i, line in enumerate(lines, 1):
					line = line.strip()
					if line:  # 非空行
						if line in labels:
							self.__log(
								f"警告: 标签文件中发现重复标签 '{line}' (第{i}行)", color="yellow", level="warning"
							)
						else:
							labels.append(line)

			if not labels:
				self.__log("警告: 标签文件为空或只包含空行", color="yellow", level="warning")

			self.__log(f"读取到 {len(labels)} 个标签: {', '.join(labels)}", color="blue")
			return labels

		except Exception as e:
			self.__log(f"读取标签文件出错: {labels_file}, 错误: {str(e)}", color="red", level="error")
			return []
			
	def generate_yaml_config(self) -> bool:
		"""
		生成YAML配置文件。
		
		Returns:
			操作成功返回True，否则返回False
		"""
		try:
			self.__log("开始生成YAML配置文件...", color="blue", level="info")
			
			# 获取路径
			output_path = self.__get_output_path()
			config_path = self.__get_config_path()
			
			# 检查路径是否有效
			if not output_path:
				self.__log("错误: 标注数据输出路径不能为空", color="red", level="error")
				return False
				
			if not config_path:
				self.__log("错误: 配置文件路径不能为空", color="red", level="error")
				return False
				
			# 读取标签文件
			labels = self.__read_labels_file()
			if not labels:
				self.__log("错误: 未能读取到有效的标签", color="red", level="error")
				return False
				
			# 创建YAML配置
			yaml_config = {
				'train': os.path.join(output_path, 'train_data', 'images', 'train'),
				'val': os.path.join(output_path, 'train_data', 'images', 'val'),
				'names': {i: label for i, label in enumerate(labels)},
			}
			
			# 确保配置文件目录存在
			os.makedirs(os.path.dirname(config_path) if os.path.dirname(config_path) else '.', exist_ok=True)
			
			# 保存YAML配置文件
			with open(config_path, 'w', encoding='utf-8') as f:
				yaml.dump(yaml_config, f, default_flow_style=False, sort_keys=False)
				
			self.__log(f"YAML配置文件已生成: {config_path}", color="green", level="info")
			
			# 更新结果标签
			self.__update_result_label(os.path.basename(config_path))
			
			return True
			
		except Exception as e:
			import traceback
			self.__log(f"生成YAML配置文件时发生错误: {str(e)}", color="red", level="error")
			self.__log(traceback.format_exc(), color="red", level="error")
			return False
