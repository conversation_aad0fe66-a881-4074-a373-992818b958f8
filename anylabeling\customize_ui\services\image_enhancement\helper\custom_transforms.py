from __future__ import annotations
from typing import Any
import albumentations as A
import numpy as np
from . import custom_functional as CustomF

class CustomAffine(A.<PERSON>ffin<PERSON>):
	"""
	一个继承自 `albumentations.Affine` 的自定义仿射变换类。

	此类重写了 `apply_to_keypoints` 方法，以使用一个自定义的函数
	(`custom_functional.custom_keypoints_affine`) 来处理关键点。
	这个自定义函数确保了在使用如 `cv2.BORDER_REFLECT_101` 这样的
	边框模式时，不会为图像的反射区域生成新的标注坐标。

	所有其他的行为，包括对图像本身、边界框和掩码的处理，都保持
	与原始的 `A.Affine` 变换完全一致。
	"""
	def apply_to_keypoints(self, keypoints: np.ndarray, **params: Any) -> np.ndarray:
		"""
		对关键点应用自定义的仿射变换逻辑。

		参数:
		- keypoints (np.ndarray): 待变换的关键点数组。
		- **params (Any): 从 `get_params_dependent_on_data` 传递来的变换参数，
		  例如 `matrix`, `scale`, `shape` 等。

		返回:
		- np.ndarray: 经过变换后且未生成反射点的关键点数组。
		"""
		# 精确传递 custom_keypoints_affine 所需的参数，以解决 TypeError。
		# 'matrix' 和 'scale' 来自 albumentations 内部计算后传入的 params。
		# 'image_shape' 同样由内部以 'shape' 的键名传入 params。
		# 'border_mode' 是变换对象自身的属性。
		return CustomF.custom_keypoints_affine(
			keypoints,
			matrix=params["matrix"],
			scale=params["scale"],
			image_shape=params["shape"],
			border_mode=self.border_mode,
		)

	def get_transform_init_args_names(self) -> tuple[str, ...]:
		# 继承并返回父类 `Affine` 的初始化参数，以确保序列化和反序列化能够正常工作。
		return super().get_transform_init_args_names() 