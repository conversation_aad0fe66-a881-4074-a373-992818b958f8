import threading
import traceback

import global_tools.utils as global_tools_utils
from PyQt5.QtWidgets import QCheckBox
from global_tools.ui_tools import (
	CheckBoxManager, TextEditMemory, LineEditManager,
	get_widgets_from_layout, LogOutput, Q<PERSON><PERSON><PERSON>uttonManager, QLabelManager,
)
from global_tools.utils import Colors

from anylabeling.customize_ui.helper.const import CONFIG_PATH, X_LABEL_LOG
from anylabeling.customize_ui.helper.helper import TextEditManager
from anylabeling.customize_ui.helper.helper import set_labels_copy_on_click
from anylabeling.customize_ui.layout.layout import Ui_Form
from anylabeling.customize_ui.src.ui_operate.train_model.helper import QuerySyntaxHighlighter
from .helper import ImagePredictor, clear_json_files_if_checked


class PredictImage:

	image_predictor: ImagePredictor

	def __init__( self, ui_form: Ui_Form, log_output: LogOutput ):
		self.ui_form = ui_form
		self.log_output = log_output

		self.logger: global_tools_utils.Logger = global_tools_utils.ClassInstanceManager.get_instance(
			X_LABEL_LOG
			)  # type: ignore

		# ===========================================================================
		# ------------------------- QLineEdit -------------------------
		# ===========================================================================
		self.line_edit_manager = LineEditManager.get_instance()

		# ===========================================================================
		# ------------------------- QPushButton -------------------------
		# ===========================================================================

		self.push_button_manager = QPushButtonManager.get_instance()
		self.push_button_manager.connect_clicked_signal(
			button_name="pushButton_63", slot=self.predict_image_button_function
			)
		self.push_button_manager.connect_clicked_signal(
			button_name="pushButton_37", slot=self.stop_predict_image_button_function
			)
		self.push_button_manager.connect_clicked_signal(
			button_name="pushButton_38", slot=self.filter_prediction_data_callback
			)

		# ===========================================================================
		# ------------------------- QLabel -------------------------
		# ===========================================================================

		self.label_manager = QLabelManager.get_instance()
		value_map = {
			"label_595": "item_count",
			"label_597": "unique_name_count",
			"label_593": "confidence",
			"label_591": "name",
		}
		set_labels_copy_on_click(
			layouts=[ self.ui_form.label_595, self.ui_form.label_593, self.ui_form.label_591, self.ui_form.label_597 ],
			copy_map=value_map,
			log_output=self.log_output,
			logger=self.logger
		)

		# ===========================================================================
		# ------------------------- QTextEdit -------------------------
		# ===========================================================================
		self.text_edit_list = [
			self.ui_form.textEdit_4,
		]
		self.text_edit_manager = TextEditManager( text_edits=self.text_edit_list )

		self.text_edit_memory = TextEditMemory(
			text_edits=self.text_edit_list,
			log_output=self.log_output,
			logger=self.logger,
			config_filename="textEdit_memory.json",
			config_path=CONFIG_PATH
		)
		QuerySyntaxHighlighter( text_edit=self.ui_form.textEdit_4 )

		# ===========================================================================
		# ------------------------- QCheckBox -------------------------
		# ===========================================================================
		self.checkbox_manager = CheckBoxManager.get_instance()
		CheckBoxManager.set_exclusive_selection_static(
			checkboxes=[ *get_widgets_from_layout( self.ui_form.horizontalLayout_521, QCheckBox ) ], logger=self.logger
		)

		self.image_predictor = ImagePredictor(
			line_edit_manager=self.line_edit_manager,
			log_output=self.log_output,
			logger=self.logger,
			label_manager=self.label_manager,
			checkbox_manager=self.checkbox_manager
		)

	def __call__( self ):
		pass

	def predict_image_button_function( self ):
		"""
		启动一个后台线程来执行图像预测，以防止UI冻结。
		"""
		self.push_button_manager.set_enabled( button_name="pushButton_63", enabled=False )
		try:
			# 创建并启动线程
			def thread_function():
				self.log_output.append( message="正在启动预测进程...", color=Colors.SECONDARY )
				self.image_predictor.predict(
					condition_string=self.text_edit_manager.get_text( object_name="textEdit_4" )
					)
				self.push_button_manager.set_enabled( button_name="pushButton_63", enabled=True )

			thread = threading.Thread( target=thread_function )
			thread.start()
		except Exception as e:
			self.log_output.append( f"启动预测线程时发生错误: {e}", color="red" )
			self.logger.error( f"Failed to start prediction thread: {e}\n{traceback.format_exc()}" )

	def stop_predict_image_button_function( self ):
		"""
		向正在运行的预测任务发送停止信号。
		"""
		self.image_predictor.stop()

	def filter_prediction_data_callback( self ):
		"""
		筛选预测数据。
		"""

		def thread_func():
			# 清空 JSON 文件
			clear_json_files_if_checked(
				line_edit_manager=self.line_edit_manager,
				checkbox_manager=self.checkbox_manager,
				label_manager=self.label_manager,
				log_output=self.log_output,
				logger=self.logger
			)

			self.push_button_manager.set_enabled( button_name="pushButton_38", enabled=False )
			condition_string = self.text_edit_manager.get_text( object_name="textEdit_4" )
			self.image_predictor.handle_predict_results( condition_string=condition_string )
			self.push_button_manager.set_enabled( button_name="pushButton_38", enabled=True )

		thread = threading.Thread( target=thread_func )
		thread.start()
