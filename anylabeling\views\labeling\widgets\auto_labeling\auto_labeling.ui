<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>auto_labeling_form</class>
 <widget class="QWidget" name="auto_labeling_form">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1118</width>
    <height>68</height>
   </rect>
  </property>
  <property name="sizePolicy">
   <sizepolicy hsizetype="Minimum" vsizetype="Minimum">
    <horstretch>0</horstretch>
    <verstretch>0</verstretch>
   </sizepolicy>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <property name="styleSheet">
   <string notr="true"/>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <property name="spacing">
    <number>5</number>
   </property>
   <property name="leftMargin">
    <number>0</number>
   </property>
   <property name="topMargin">
    <number>2</number>
   </property>
   <property name="rightMargin">
    <number>2</number>
   </property>
   <property name="bottomMargin">
    <number>2</number>
   </property>
   <item>
    <layout class="QHBoxLayout" name="model_selection">
     <property name="spacing">
      <number>6</number>
     </property>
     <property name="topMargin">
      <number>0</number>
     </property>
    <item>
        <widget class="QPushButton" name="model_selection_button">
            <property name="text">
                <string>No Model</string>
            </property>
        </widget>
    </item>
     <item>
      <widget class="QLabel" name="output_label">
       <property name="text">
        <string>Output</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QComboBox" name="output_select_combobox">
       <item>
        <property name="text">
         <string>polygon</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>rectangle</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>rotation</string>
        </property>
       </item>
      </widget>
     </item>
     <item>
      <widget class="QComboBox" name="upn_select_combobox">
        <property name="currentIndex">
            <number>0</number>
        </property>
       <item>
        <property name="text">
         <string>coarse_grained_prompt</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>fine_grained_prompt</string>
        </property>
       </item>
      </widget>
     </item>
     <item>
      <widget class="QComboBox" name="florence2_select_combobox">
        <property name="currentIndex">
            <number>0</number>
        </property>
       <item>
        <property name="text">
         <string>caption</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>detailed_cap</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>more_detailed_cap</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>od</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>region_proposal</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>dense_region_cap</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>cap_to_pg</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>refer_exp_seg</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>region_to_seg</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>ovd</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>region_to_cat</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>region_to_desc</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>ocr</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>ocr_with_region</string>
        </property>
       </item>
      </widget>
     </item>
     <item>
      <widget class="QComboBox" name="gd_select_combobox">
        <property name="currentIndex">
            <number>0</number>
        </property>
       <item>
        <property name="text">
         <string>GroundingDino_1_6_Pro</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>GroundingDino_1_6_Edge</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>GroundingDino_1_5_Pro</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>GroundingDino_1_5_Edge</string>
        </property>
       </item>
      </widget>
     </item>
     <item>
      <widget class="Line" name="line">
       <property name="orientation">
        <enum>Qt::Vertical</enum>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="button_run">
       <property name="text">
        <string>Run (i)</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QLineEdit" name="edit_text">
        <property name="placeholderText">
            <string>Enter text prompt here, e.g., person.car.bicycle</string>
        </property>
      </widget>
     </item>
    <item>
        <widget class="QPushButton" name="button_send">
            <property name="text">
                <string>Send</string>
            </property>
        </widget>
    </item>
     <item>
      <widget class="QLabel" name="input_box_thres">
       <property name="text">
        <string>Box threshold</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QLabel" name="input_conf">
       <property name="text">
        <string>Confidence</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QDoubleSpinBox" name="edit_conf">
       <property name="decimals">
        <number>2</number>
       </property>
       <property name="minimum">
        <double>0.000000000000000</double>
       </property>
       <property name="maximum">
        <double>1.000000000000000</double>
       </property>
       <property name="singleStep">
        <double>0.010000000000000</double>
       </property>
       <!-- <property name="value">
        <double>0.250000000000000</double>
       </property> -->
      </widget>
     </item>
     <item>
      <widget class="QLabel" name="input_iou">
       <property name="text">
        <string>IoU</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QDoubleSpinBox" name="edit_iou">
       <property name="decimals">
        <number>2</number>
       </property>
       <property name="minimum">
        <double>0.000000000000000</double>
       </property>
       <property name="maximum">
        <double>1.000000000000000</double>
       </property>
       <property name="singleStep">
        <double>0.010000000000000</double>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="button_add_point">
       <property name="text">
        <string>Point (q)</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="button_remove_point">
       <property name="text">
        <string>Point (e)</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="button_add_rect">
       <property name="text">
        <string>+Rect</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="button_clear">
       <property name="text">
        <string>Clear (b)</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="button_finish_object">
       <property name="text">
        <string>Finish (f)</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="toggle_preserve_existing_annotations">
          <property name="text">
              <string>Replace (On)</string>
          </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="button_set_api_token">
       <property name="text">
        <string>Set API Token</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="button_reset_tracker">
       <property name="text">
        <string>Reset Tracker</string>
       </property>
      </widget>
     </item>
     <item>
      <spacer name="horizontalSpacer">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>0</width>
         <height>0</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <widget class="QPushButton" name="button_close">
       <property name="sizePolicy">
        <sizepolicy hsizetype="Minimum" vsizetype="Fixed">
         <horstretch>0</horstretch>
         <verstretch>0</verstretch>
        </sizepolicy>
       </property>
       <property name="text">
        <string/>
       </property>
       <property name="icon">
        <iconset resource="../../../../resources/resources.qrc">
         <normaloff>:/images/images/cancel.png</normaloff>:/images/images/cancel.png</iconset>
       </property>
       <property name="iconSize">
        <size>
         <width>12</width>
         <height>12</height>
        </size>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <item>
    <widget class="QLabel" name="model_status_label">
     <property name="sizePolicy">
      <sizepolicy hsizetype="Expanding" vsizetype="Minimum">
       <horstretch>0</horstretch>
       <verstretch>0</verstretch>
      </sizepolicy>
     </property>
     <property name="styleSheet">
      <string notr="true">margin-top: 0;
margin-bottom: 2px;</string>
     </property>
     <property name="text">
      <string>Ready!</string>
     </property>
    </widget>
   </item>
  </layout>
 </widget>
 <resources>
  <include location="../../../../resources/resources.qrc"/>
 </resources>
 <connections/>
</ui>
