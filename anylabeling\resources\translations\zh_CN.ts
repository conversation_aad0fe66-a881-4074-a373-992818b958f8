<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE TS>
<TS version="2.1">
<context>
    <name></name>
    <message>
        <location filename="../../views/labeling/utils/shape.py" line="28"/>
        <source>Warning</source>
        <translation>警告</translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/shape.py" line="29"/>
        <source>Current annotation will be changed</source>
        <translation>当前标签将被更改</translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/shape.py" line="30"/>
        <source>Are you sure you want to perform this conversion?</source>
        <translation>您确定要执行此转换吗？</translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/shape.py" line="41"/>
        <source>Converting...</source>
        <translation>转换中。。。</translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/crop.py" line="343"/>
        <source>Cancel</source>
        <translation>取消</translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/crop.py" line="351"/>
        <source>Progress</source>
        <translation>进度</translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/shape.py" line="121"/>
        <source>Conversion completed successfully!</source>
        <translation>转换完成！</translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/shape.py" line="133"/>
        <source>Error occurred while converting shapes!</source>
        <translation>转换过程中发生错误！</translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/crop.py" line="203"/>
        <source>Please load an image folder before proceeding!</source>
        <translation>请先加载图像！</translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/crop.py" line="213"/>
        <source>Cropped Image Options</source>
        <translation>裁剪图像选项</translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/crop.py" line="222"/>
        <source>Save Path</source>
        <translation>保存路径</translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/crop.py" line="235"/>
        <source>Select Save Directory</source>
        <translation>选择保存目录</translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/crop.py" line="244"/>
        <source>Browse</source>
        <translation>浏览</translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/crop.py" line="254"/>
        <source>Minimum width:</source>
        <translation>最小宽度：</translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/crop.py" line="270"/>
        <source>Minimum height:</source>
        <translation>最小高度：</translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/crop.py" line="293"/>
        <source>OK</source>
        <translation>确认</translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/crop.py" line="313"/>
        <source>Output Directory Exists!</source>
        <translation>输出目录已存在！</translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/crop.py" line="314"/>
        <source>Directory already exists. Choose an action:</source>
        <translation>目录已存在。选择一个操作：</translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/crop.py" line="315"/>
        <source>• Overwrite - Overwrite existing directory
• Cancel - Abort export</source>
        <translation>• 覆盖 - 覆盖现有目录
• 取消 - 中止导出</translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/crop.py" line="322"/>
        <source>Overwrite</source>
        <translation>覆盖</translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/crop.py" line="343"/>
        <source>Processing...</source>
        <translation>处理中。。。</translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/crop.py" line="411"/>
        <source>Cropped images successfully!
Results have been saved to:
{save_path}</source>
        <translation>{save_path}</translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/crop.py" line="423"/>
        <source>Error occurred while exporting cropped images!</source>
        <translation>导出裁剪图像时发生错误！</translation>
    </message>
</context>
<context>
    <name>AboutDialog</name>
    <message>
        <location filename="../../views/labeling/widgets/about_dialog.py" line="114"/>
        <source>Website</source>
        <translation>网站</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/about_dialog.py" line="118"/>
        <source>Copy App Info</source>
        <translation>复制应用信息</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/about_dialog.py" line="122"/>
        <source>Report Issue</source>
        <translation>报告问题</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/about_dialog.py" line="180"/>
        <source>Changelog</source>
        <translation>更新日志</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/about_dialog.py" line="184"/>
        <source>Check for Updates</source>
        <translation>检查更新</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/about_dialog.py" line="374"/>
        <source>Copied!</source>
        <translation>已复制！</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/about_dialog.py" line="265"/>
        <source>No Updates Available</source>
        <translation>没有可用更新</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/about_dialog.py" line="272"/>
        <source>GitHub API error: {response.status_code}</source>
        <translation>GitHub API错误：{response.status_code}</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/about_dialog.py" line="280"/>
        <source>Check update error: {str(e)}</source>
        <translation>检查更新错误：{str(e)}</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/about_dialog.py" line="317"/>
        <source>Update Available</source>
        <translation>有可用升级</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/about_dialog.py" line="352"/>
        <source>Cancel</source>
        <translation>取消</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/about_dialog.py" line="355"/>
        <source>Download</source>
        <translation>下载</translation>
    </message>
</context>
<context>
    <name>ApiTokenDialog</name>
    <message>
        <location filename="../../views/labeling/widgets/api_token_dialog.py" line="29"/>
        <source>Set API Token</source>
        <translation>设置 API 密钥</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/api_token_dialog.py" line="34"/>
        <source>Enter your API Token:</source>
        <translation>输入您的 API 密钥：</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/api_token_dialog.py" line="41"/>
        <source>Enter API key</source>
        <translation>输入 API 密钥</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/api_token_dialog.py" line="85"/>
        <source>Show</source>
        <translation>显示</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/api_token_dialog.py" line="86"/>
        <source>Hide</source>
        <translation>隐藏</translation>
    </message>
</context>
<context>
    <name>AutoLabelingWidget</name>
    <message>
        <location filename="../../views/labeling/widgets/auto_labeling/auto_labeling.py" line="244"/>
        <source>No Model</source>
        <translation type="obsolete">没有模型</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/auto_labeling/auto_labeling.py" line="245"/>
        <source>...Load Custom Model</source>
        <translation type="obsolete">...加载自定义模型</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/auto_labeling/auto_labeling.py" line="249"/>
        <source>(User) </source>
        <translation type="obsolete">(用户)</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/auto_labeling/auto_labeling.py" line="430"/>
        <source>Coarse Grained</source>
        <translation>粗粒度检测</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/auto_labeling/auto_labeling.py" line="431"/>
        <source>Fine Grained</source>
        <translation>细粒度检测</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/auto_labeling/auto_labeling.py" line="456"/>
        <source>Caption</source>
        <translation>Caption</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/auto_labeling/auto_labeling.py" line="457"/>
        <source>Detailed Caption</source>
        <translation>Detailed Caption</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/auto_labeling/auto_labeling.py" line="458"/>
        <source>More Detailed Caption</source>
        <translation>More Detailed Caption</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/auto_labeling/auto_labeling.py" line="459"/>
        <source>Object Detection</source>
        <translation>Object Detection</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/auto_labeling/auto_labeling.py" line="460"/>
        <source>Region Proposal</source>
        <translation>Region Proposal</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/auto_labeling/auto_labeling.py" line="461"/>
        <source>Dense Region Caption</source>
        <translation>Dense Region Caption</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/auto_labeling/auto_labeling.py" line="462"/>
        <source>Refer-Exp Segmentation</source>
        <translation>Refer-Exp Segmentation</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/auto_labeling/auto_labeling.py" line="463"/>
        <source>Region to Segmentation</source>
        <translation>Region to Segmentation</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/auto_labeling/auto_labeling.py" line="464"/>
        <source>OVD</source>
        <translation>OVD</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/auto_labeling/auto_labeling.py" line="465"/>
        <source>Caption to Parse Grounding</source>
        <translation>Caption to Parse Grounding</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/auto_labeling/auto_labeling.py" line="466"/>
        <source>Region to Category</source>
        <translation>Region to Category</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/auto_labeling/auto_labeling.py" line="467"/>
        <source>Region to Description</source>
        <translation>Region to Description</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/auto_labeling/auto_labeling.py" line="468"/>
        <source>OCR</source>
        <translation>OCR</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/auto_labeling/auto_labeling.py" line="469"/>
        <source>OCR with Region</source>
        <translation>OCR with Region</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/auto_labeling/auto_labeling.py" line="205"/>
        <source>Existing shapes will be preserved during updates. Click to switch to overwriting.</source>
        <translation>现有对象在更新期间将被保留。点击切换到覆盖状态。</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/auto_labeling/auto_labeling.py" line="208"/>
        <source>Existing shapes will be overwritten by new shapes during updates. Click to switch to preserving.</source>
        <translation>现有对象在更新期间将被新对象覆盖。点击切换到保留状态。</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/auto_labeling/auto_labeling.py" line="170"/>
        <source>Keep Shapes</source>
        <translation type="obsolete">保留对象</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/auto_labeling/auto_labeling.py" line="170"/>
        <source>Overwrite Shapes</source>
        <translation type="obsolete">覆盖对象</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/auto_labeling/auto_labeling.py" line="138"/>
        <source>You can set the API token via the GROUNDING_DINO_API_TOKEN environment variable</source>
        <translation>您可以通过 GROUNDING_DINO_API_TOKEN 环境变量设置 API 密钥</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/auto_labeling/auto_labeling.py" line="212"/>
        <source>Replace (Off)</source>
        <translation>标签覆盖（关闭）</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/auto_labeling/auto_labeling.py" line="212"/>
        <source>Replace (On)</source>
        <translation>标签覆盖（开启）</translation>
    </message>
</context>
<context>
    <name>BatchProcessDialog</name>
    <message>
        <location filename="../../views/labeling/chatbot/general.py" line="23"/>
        <source>Batch Process All Images</source>
        <translation>批量处理所有图像</translation>
    </message>
    <message>
        <location filename="../../views/labeling/chatbot/general.py" line="44"/>
        <source>Enter the prompt to apply to all images:</source>
        <translation>输入要应用于所有图像的提示：</translation>
    </message>
    <message>
        <location filename="../../views/labeling/chatbot/general.py" line="60"/>
        <source>Type your prompt here and use `@image` to reference the image.</source>
        <translation>在这里输入您的提示，并使用 `@image` 来引用图像：</translation>
    </message>
    <message>
        <location filename="../../views/labeling/chatbot/general.py" line="108"/>
        <source>Cancel</source>
        <translation>取消</translation>
    </message>
    <message>
        <location filename="../../views/labeling/chatbot/general.py" line="135"/>
        <source>Confirm</source>
        <translation>确认</translation>
    </message>
</context>
<context>
    <name>BrightnessContrastDialog</name>
    <message>
        <location filename="../../views/labeling/widgets/brightness_contrast_dialog.py" line="17"/>
        <source>Brightness/Contrast</source>
        <translation>亮度/对比度</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/brightness_contrast_dialog.py" line="112"/>
        <source>Reset</source>
        <translation>重置</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/brightness_contrast_dialog.py" line="134"/>
        <source>Confirm</source>
        <translation>确认</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/brightness_contrast_dialog.py" line="64"/>
        <source>Brightness:</source>
        <translation>亮度</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/brightness_contrast_dialog.py" line="86"/>
        <source>Contrast:</source>
        <translation>对比度</translation>
    </message>
</context>
<context>
    <name>Canvas</name>
    <message>
        <location filename="../../views/labeling/widgets/canvas.py" line="122"/>
        <source>Loading...</source>
        <translation>加载中...</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/canvas.py" line="250"/>
        <source>Auto Labeling</source>
        <translation>自动标注</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/canvas.py" line="252"/>
        <source>Drawing</source>
        <translation>绘制</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/canvas.py" line="254"/>
        <source>Editing</source>
        <translation>编辑</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/canvas.py" line="256"/>
        <source>Unknown</source>
        <translation>未知</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/canvas.py" line="409"/>
        <source>Image</source>
        <translation>图像</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/canvas.py" line="460"/>
        <source>Click &amp; drag to move shape &apos;%s&apos;</source>
        <translation>点击并拖动以移动对象：'%s'</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/canvas.py" line="424"/>
        <source>Click &amp; drag to move point of shape &apos;%s&apos;</source>
        <translation>点击并拖动以移动对象顶点：'%s'</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/canvas.py" line="439"/>
        <source>Click to create point of shape &apos;%s&apos;</source>
        <translation>点击创建对象顶点：'%s'</translation>
    </message>
</context>
<context>
    <name>ChatMessage</name>
    <message>
        <location filename="../../views/labeling/chatbot/chat.py" line="223"/>
        <source>Resend</source>
        <translation>重新发送</translation>
    </message>
    <message>
        <location filename="../../views/labeling/chatbot/chat.py" line="232"/>
        <source>Cancel</source>
        <translation>取消</translation>
    </message>
    <message>
        <location filename="../../views/labeling/chatbot/chat.py" line="239"/>
        <source>Save</source>
        <translation>保存</translation>
    </message>
    <message>
        <location filename="../../views/labeling/chatbot/chat.py" line="661"/>
        <source>Are you sure to delete this message forever?</source>
        <translation>您确定要永久删除此消息吗？</translation>
    </message>
    <message>
        <location filename="../../views/labeling/chatbot/chat.py" line="746"/>
        <source>Copy message</source>
        <translation>复制消息</translation>
    </message>
    <message>
        <location filename="../../views/labeling/chatbot/chat.py" line="752"/>
        <source>Edit message</source>
        <translation>编辑消息</translation>
    </message>
    <message>
        <location filename="../../views/labeling/chatbot/chat.py" line="757"/>
        <source>Regenerate response</source>
        <translation>重新生成</translation>
    </message>
    <message>
        <location filename="../../views/labeling/chatbot/chat.py" line="763"/>
        <source>Delete message</source>
        <translation>删除消息</translation>
    </message>
</context>
<context>
    <name>ChatbotDialog</name>
    <message>
        <location filename="../../views/labeling/widgets/chatbot_dialog.py" line="89"/>
        <source>Clear Chat</source>
        <translation>清除对话</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/chatbot_dialog.py" line="90"/>
        <source>Open Image Folder</source>
        <translation>打开图像文件夹</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/chatbot_dialog.py" line="92"/>
        <source>Open Video File</source>
        <translation type="obsolete">打开视频文件</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/chatbot_dialog.py" line="93"/>
        <source>Open Image File</source>
        <translation>打开图像文件</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/chatbot_dialog.py" line="96"/>
        <source>Previous Image</source>
        <translation>上一张图像</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/chatbot_dialog.py" line="99"/>
        <source>Next Image</source>
        <translation>下一张图像</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/chatbot_dialog.py" line="100"/>
        <source>Run All Images</source>
        <translation>运行所有图像</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/chatbot_dialog.py" line="103"/>
        <source>Import/Export Dataset</source>
        <translation>导入/导出数据集</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/chatbot_dialog.py" line="238"/>
        <source>Type something and Ctrl+↩︎ to send. Use @image to add an image.</source>
        <translation>输入内容并按 Ctrl+↩︎ 发送。使用 @image 添加图片。</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/chatbot_dialog.py" line="459"/>
        <source>API Address</source>
        <translation>API 地址</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/chatbot_dialog.py" line="500"/>
        <source>API Key</source>
        <translation>API 密钥</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/chatbot_dialog.py" line="533"/>
        <source>Enter API key</source>
        <translation>输入 API 密钥</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/chatbot_dialog.py" line="558"/>
        <source>Model Name</source>
        <translation>模型名称</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/chatbot_dialog.py" line="605"/>
        <source>System instruction</source>
        <translation>系统提示词</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/chatbot_dialog.py" line="623"/>
        <source>Temperature</source>
        <translation>温度</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/chatbot_dialog.py" line="659"/>
        <source>Precise</source>
        <translation>精确</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/chatbot_dialog.py" line="665"/>
        <source>Neutral</source>
        <translation>中性</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/chatbot_dialog.py" line="671"/>
        <source>Creative</source>
        <translation>创意</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/chatbot_dialog.py" line="685"/>
        <source>Max output tokens</source>
        <translation>最大输出长度</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/chatbot_dialog.py" line="711"/>
        <source>Backend</source>
        <translation>后端</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/chatbot_dialog.py" line="712"/>
        <source>Generation</source>
        <translation>生成</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/chatbot_dialog.py" line="965"/>
        <source>Image not available</source>
        <translation>图像不支持</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/chatbot_dialog.py" line="1367"/>
        <source>Inferencing...</source>
        <translation>推理中。。。</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/chatbot_dialog.py" line="1367"/>
        <source>Cancel</source>
        <translation>取消</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/chatbot_dialog.py" line="1375"/>
        <source>Progress</source>
        <translation>进度</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/chatbot_dialog.py" line="1488"/>
        <source>Dataset Operations</source>
        <translation>数据操作</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/chatbot_dialog.py" line="1499"/>
        <source>Import Dataset</source>
        <translation>导入数据</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/chatbot_dialog.py" line="1502"/>
        <source>Export Dataset</source>
        <translation>导出数据</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/chatbot_dialog.py" line="1643"/>
        <source>Export Error</source>
        <translation>导出错误</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/chatbot_dialog.py" line="1655"/>
        <source>No file is currently open.</source>
        <translation>当前没有打开的文件。</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/chatbot_dialog.py" line="1521"/>
        <source>Select Export Directory</source>
        <translation>选择导出目录</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/chatbot_dialog.py" line="1552"/>
        <source>No labeling files found in the current directory.</source>
        <translation>当前目录中未找到标注文件。</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/chatbot_dialog.py" line="1603"/>
        <source>Error processing {json_file}: {str(e)}</source>
        <translation>处理 {json_file} 时出错: {str(e)}。</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/chatbot_dialog.py" line="1608"/>
        <source>No valid chat data found to export.</source>
        <translation>未找到可导出的有效聊天数据。</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/chatbot_dialog.py" line="1633"/>
        <source>Export Successful</source>
        <translation>导出成功</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/chatbot_dialog.py" line="1633"/>
        <source>Dataset exported successfully to:
{zip_path}</source>
        <translation>数据集成功导出至：
{zip_path}</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/chatbot_dialog.py" line="1643"/>
        <source>An error occurred during export:
{str(e)}</source>
        <translation>在导出过程中出现出现：
{str(e)}</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/chatbot_dialog.py" line="1802"/>
        <source>Import Error</source>
        <translation>导入错误</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/chatbot_dialog.py" line="1663"/>
        <source>Select Dataset File</source>
        <translation>选择数据文件</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/chatbot_dialog.py" line="1678"/>
        <source>Invalid dataset format. Expected a list of records.</source>
        <translation>数据集格式无效。</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/chatbot_dialog.py" line="1781"/>
        <source>Import Successful</source>
        <translation>导入成功</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/chatbot_dialog.py" line="1790"/>
        <source>Import Notice</source>
        <translation>导入通知</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/chatbot_dialog.py" line="1790"/>
        <source>No valid items were found to import. Make sure images are available.</source>
        <translation>未找到可导入的有效项目。请确保图像可用。</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/chatbot_dialog.py" line="1802"/>
        <source>An error occurred during import:
{str(e)}</source>
        <translation>在导入过程中发生错误：
{str(e)}</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/chatbot_dialog.py" line="2257"/>
        <source>Are you sure you want to clear the entire conversation?</source>
        <translation>您确定要清除整个对话吗？</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/chatbot_dialog.py" line="1401"/>
        <source>Processing image %d/%d...</source>
        <translation>处理图像 %d/%d...</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/chatbot_dialog.py" line="1777"/>
        <source>Successfully imported {0} items to:
{1}</source>
        <translation>成功导入 {0} 项到：
{1}</translation>
    </message>
</context>
<context>
    <name>CrosshairSettingsDialog</name>
    <message>
        <location filename="../../views/labeling/widgets/crosshair_settings_dialog.py" line="17"/>
        <source>Crosshair Settings</source>
        <translation>十字线设置</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/crosshair_settings_dialog.py" line="118"/>
        <source>Show Crosshair:</source>
        <translation>显示十字线：</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/crosshair_settings_dialog.py" line="129"/>
        <source>Line width:</source>
        <translation>线条宽度：</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/crosshair_settings_dialog.py" line="151"/>
        <source>Line Opacity:</source>
        <translation>线条透明度：</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/crosshair_settings_dialog.py" line="174"/>
        <source>Line Color:</source>
        <translation>线条颜色：</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/crosshair_settings_dialog.py" line="192"/>
        <source>Choose Color</source>
        <translation>选取颜色</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/crosshair_settings_dialog.py" line="221"/>
        <source>Reset</source>
        <translation>重置</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/crosshair_settings_dialog.py" line="242"/>
        <source>OK</source>
        <translation>确认</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/crosshair_settings_dialog.py" line="263"/>
        <source>Cancel</source>
        <translation>取消</translation>
    </message>
</context>
<context>
    <name>DigitShortcutDialog</name>
    <message>
        <location filename="../../views/labeling/widgets/label_dialog.py" line="110"/>
        <source>Digit Shortcut Manager</source>
        <translation>数字快捷键管理器</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/label_dialog.py" line="127"/>
        <source>配置数字键 (0-9) 用于快速创建对象：</source>
        <translation type="obsolete">数字快捷键管理器</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/label_dialog.py" line="183"/>
        <source>Digit</source>
        <translation>数字</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/label_dialog.py" line="183"/>
        <source>Drawing Mode</source>
        <translation>绘制模式</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/label_dialog.py" line="183"/>
        <source>Label</source>
        <translation>标签</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/label_dialog.py" line="208"/>
        <source>None</source>
        <translation>空</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/label_dialog.py" line="252"/>
        <source>Reset</source>
        <translation>重置</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/label_dialog.py" line="273"/>
        <source>OK</source>
        <translation>确认</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/label_dialog.py" line="294"/>
        <source>Cancel</source>
        <translation>取消</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/label_dialog.py" line="344"/>
        <source>Required</source>
        <translation>必须填写</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/label_dialog.py" line="349"/>
        <source>Confirm Reset</source>
        <translation>确认重置</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/label_dialog.py" line="349"/>
        <source>Are you sure you want to reset all shortcuts? This cannot be undone.</source>
        <translation>您确定要重置所有快捷键吗？此操作无法撤销。</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/label_dialog.py" line="398"/>
        <source>Validation Error</source>
        <translation>验证错误</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/label_dialog.py" line="398"/>
        <source>Please provide a label for each enabled drawing mode.</source>
        <translation>请为每个已启用的绘制模式提供标签名称。</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/label_dialog.py" line="173"/>
        <source>Configure digit keys (0-9) for quick shape creation:</source>
        <translation>配置数字键（0-9）用于快速创建对象</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/label_dialog.py" line="414"/>
        <source>Digit shortcuts saved successfully</source>
        <translation>保存成功</translation>
    </message>
</context>
<context>
    <name>ExportThread</name>
    <message>
        <location filename="../../views/labeling/utils/export.py" line="83"/>
        <source>Please load an image folder before proceeding!</source>
        <translation>请先加载图像！</translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/export.py" line="101"/>
        <source>Select a specific yolo-pose config file</source>
        <translation>选择一个特定的 YOLO-Pose 配置文件</translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/export.py" line="1043"/>
        <source>Please select a specific config file!</source>
        <translation type="obsolete">请选择一个特定的配置文件</translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/export.py" line="1445"/>
        <source>Select a specific classes file</source>
        <translation>请选择一个具体的标签文件</translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/export.py" line="1165"/>
        <source>Export options</source>
        <translation>导出选项</translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/export.py" line="1174"/>
        <source>Export path</source>
        <translation>导出路径</translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/export.py" line="1191"/>
        <source>Select Export Directory</source>
        <translation>选择导出目录</translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/export.py" line="1379"/>
        <source>Browse</source>
        <translation>浏览</translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/export.py" line="345"/>
        <source>Export Options</source>
        <translation>导出选项</translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/export.py" line="348"/>
        <source>Save with images?</source>
        <translation>保存图像？</translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/export.py" line="352"/>
        <source>Skip empty labels?</source>
        <translation>跳过空标签文件？</translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/export.py" line="1549"/>
        <source>Cancel</source>
        <translation>取消</translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/export.py" line="1485"/>
        <source>OK</source>
        <translation>确认</translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/export.py" line="1236"/>
        <source>Output Directory Exists!</source>
        <translation>输出目录已存在！</translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/export.py" line="1237"/>
        <source>Directory already exists. Choose an action:</source>
        <translation>目录已存在。选择一个操作：</translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/export.py" line="741"/>
        <source>• Yes    - Merge with existing files
• No     - Delete existing directory
• Cancel - Abort export</source>
        <translation>• 是 - 合并现有文件
• 否 - 删除现有目录
• 取消 - 中止导出</translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/export.py" line="749"/>
        <source>Yes</source>
        <translation>是</translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/export.py" line="750"/>
        <source>No</source>
        <translation>否</translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/export.py" line="1549"/>
        <source>Exporting...</source>
        <translation>导出中。。。</translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/export.py" line="1553"/>
        <source>Progress</source>
        <translation>进度</translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/export.py" line="1604"/>
        <source>Error occurred while exporting annotations!</source>
        <translation>导出标签时发生错误！</translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/export.py" line="489"/>
        <source>Select a specific coco-pose config file</source>
        <translation>选择一个特定的 COCO-Pose 配置文件</translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/export.py" line="1238"/>
        <source>• Overwrite - Overwrite existing directory
• Cancel - Abort export</source>
        <translation>• 覆盖 - 覆盖现有目录
• 取消 - 中止导出</translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/export.py" line="1514"/>
        <source>Overwrite</source>
        <translation>覆盖</translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/export.py" line="833"/>
        <source>Select a specific color_map file</source>
        <translation>选择一个特定的颜色映射表</translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/export.py" line="862"/>
        <source>Please select a specific color_map file!</source>
        <translation type="obsolete">请选择一个特定的颜色映射表</translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/export.py" line="1572"/>
        <source>Exporting annotations successfully!
Results have been saved to:
%s</source>
        <translation>导出标签成功！
结果已保存到：
%s</translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/export.py" line="1338"/>
        <source>Export VLM-R1 OVD Annotation</source>
        <translation>导出 VLM-R1 OVD 标签</translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/export.py" line="1348"/>
        <source>Export to</source>
        <translation>导出到</translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/export.py" line="1367"/>
        <source>Select Export File</source>
        <translation>选择导出文件</translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/export.py" line="1395"/>
        <source>Prefix:</source>
        <translation>前缀：</translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/export.py" line="1406"/>
        <source>Optional prefix for image filenames (e.g., &apos;path/to/images/&apos;)</source>
        <translation>可选的图像文件名前缀（例如：'path/to/images/'）</translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/export.py" line="1421"/>
        <source>{}</source>
        <translation>{}</translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/export.py" line="1431"/>
        <source>Use specific classes? (Optional)</source>
        <translation>使用特定的标签？（可选）</translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/export.py" line="1453"/>
        <source>Upload</source>
        <translation>上传</translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/export.py" line="1465"/>
        <source>Hint: If you don&apos;t upload a specific classes file, all unique labels found in one of the annotations will be used for the export.</source>
        <translation>提示：如果您不上传特定的标签文件，则将在一个注释中找到的所有唯一标签将被用于导出。</translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/export.py" line="1506"/>
        <source>File Exists!</source>
        <translation>文件已存在！</translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/export.py" line="1507"/>
        <source>File already exists. Choose an action:</source>
        <translation>文件已存在。选择一个操作：</translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/export.py" line="1508"/>
        <source>• Overwrite - Replace existing file
• Cancel - Abort export</source>
        <translation>• 覆盖 - 替换现有文件
• 取消 - 中止导出</translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/export.py" line="1539"/>
        <source>Error initializing export: %s</source>
        <translation>导出初始化错误：%s</translation>
    </message>
</context>
<context>
    <name>FrameExtractionDialog</name>
    <message>
        <location filename="../../views/labeling/utils/video.py" line="43"/>
        <source>Frame Extraction Settings</source>
        <translation>帧提取设置</translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/video.py" line="49"/>
        <source>Extract every N frames (fps: %.2f):</source>
        <translation>每秒提取 N 帧（fps: %.2f）：</translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/video.py" line="66"/>
        <source>Filename prefix:</source>
        <translation>文件名前缀：</translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/video.py" line="77"/>
        <source>Number sequence length:</source>
        <translation>序列长度：</translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/video.py" line="102"/>
        <source>OK</source>
        <translation>确认</translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/video.py" line="374"/>
        <source>Cancel</source>
        <translation>取消</translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/video.py" line="123"/>
        <source>Example output: {example}</source>
        <translation>示例输出：{example}</translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/video.py" line="226"/>
        <source>Extracting frames using ffmpeg...</source>
        <translation>使用 ffmpeg 提取帧...</translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/video.py" line="382"/>
        <source>Progress</source>
        <translation>进度</translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/video.py" line="308"/>
        <source>ffmpeg failed. Check logs.</source>
        <translation>ffmpeg 失败。检查日志。</translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/video.py" line="342"/>
        <source>ffmpeg not found.</source>
        <translation>ffmpeg 未找到。</translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/video.py" line="374"/>
        <source>Extracting frames (OpenCV)... Please wait...</source>
        <translation>提取帧（OpenCV）... 请稍候...</translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/video.py" line="500"/>
        <source>Open Video file</source>
        <translation>打开视频文件</translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/video.py" line="520"/>
        <source>Warning</source>
        <translation>警告</translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/video.py" line="521"/>
        <source>Directory Already Exists</source>
        <translation>目录已存在</translation>
    </message>
</context>
<context>
    <name>GroupIDFilterComboBox</name>
    <message>
        <location filename="../../views/labeling/widgets/filter_label_widget.py" line="9"/>
        <source>Group ID Filter</source>
        <translation>群组编号过滤器</translation>
    </message>
</context>
<context>
    <name>GroupIDModifyDialog</name>
    <message>
        <location filename="../../views/labeling/widgets/label_dialog.py" line="473"/>
        <source>Group ID Change Manager</source>
        <translation>群组编号管理器</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/label_dialog.py" line="577"/>
        <source>Cancel</source>
        <translation>取消</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/label_dialog.py" line="599"/>
        <source>Confirm</source>
        <translation>确认</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/label_dialog.py" line="701"/>
        <source>Group IDs modified successfully!</source>
        <translation>群组编号修改成功！</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/label_dialog.py" line="709"/>
        <source>An error occurred while updating the Group IDs.</source>
        <translation>更新群组编号时发生错误。</translation>
    </message>
</context>
<context>
    <name>LabelDialog</name>
    <message>
        <location filename="../../views/labeling/widgets/label_dialog.py" line="1153"/>
        <source>Enter object label</source>
        <translation>输入对象标签</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/label_dialog.py" line="1169"/>
        <source>Group ID</source>
        <translation>群组编号</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/label_dialog.py" line="1178"/>
        <source>useDifficult</source>
        <translation>困难标志</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/label_dialog.py" line="1183"/>
        <source>Enter linking, e.g., [0,1]</source>
        <translation>输入链接，如：[0,1]</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/label_dialog.py" line="1197"/>
        <source>Add</source>
        <translation>新增</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/label_dialog.py" line="1228"/>
        <source>Label description</source>
        <translation>标签描述</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/label_dialog.py" line="1296"/>
        <source>Duplicate Entry</source>
        <translation>重复输入</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/label_dialog.py" line="1296"/>
        <source>This linking pair already exists.</source>
        <translation>当前链接已存在。</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/label_dialog.py" line="1310"/>
        <source>Invalid Input</source>
        <translation>无效输入</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/label_dialog.py" line="1310"/>
        <source>Please enter a valid list of linking pairs like [1,2].</source>
        <translation>请输入有效链接，如：[1,2]</translation>
    </message>
</context>
<context>
    <name>LabelFilterComboBox</name>
    <message>
        <location filename="../../views/labeling/widgets/filter_label_widget.py" line="28"/>
        <source>Label Filter</source>
        <translation>标签过滤器</translation>
    </message>
</context>
<context>
    <name>LabelModifyDialog</name>
    <message>
        <location filename="../../views/labeling/widgets/label_dialog.py" line="802"/>
        <source>Label Change Manager</source>
        <translation>标签管理器</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/label_dialog.py" line="1112"/>
        <source>Invalid Range</source>
        <translation>无效范围</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/label_dialog.py" line="1112"/>
        <source>Please enter a valid range.</source>
        <translation>请输入有效范围。</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/label_dialog.py" line="992"/>
        <source>Labels modified successfully!</source>
        <translation>标签修改成功！</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/label_dialog.py" line="1000"/>
        <source>An error occurred while updating the labels.</source>
        <translation>更新标签时发生错误。</translation>
    </message>
</context>
<context>
    <name>LabelingWidget</name>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="164"/>
        <source>Flags</source>
        <translation>标志</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="188"/>
        <source>Objects</source>
        <translation>对象</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="196"/>
        <source>Select label to start annotating for it. Press &apos;Esc&apos; to deselect.</source>
        <translation>选择标签以开始为其进行标注，可按 'Esc' 键取消选择</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="210"/>
        <source>Labels</source>
        <translation>标签</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="218"/>
        <source>Search Filename</source>
        <translation>搜索文件名</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="227"/>
        <source>Files</source>
        <translation type="obsolete">文件</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="291"/>
        <source>Open image or label file</source>
        <translation>打开图像或标签文件</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="305"/>
        <source>&amp;Open Dir</source>
        <translation>打开文件夹</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="305"/>
        <source>Open Dir</source>
        <translation>打开文件夹</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="312"/>
        <source>&amp;Next Image</source>
        <translation>下一张图像</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="320"/>
        <source>&amp;Prev Image</source>
        <translation>上一张图像</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="344"/>
        <source>&amp;Save</source>
        <translation>保存</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="344"/>
        <source>Save labels to file</source>
        <translation>将标签保存到文件</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="352"/>
        <source>&amp;Save As</source>
        <translation>另存为</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="352"/>
        <source>Save labels to a different file</source>
        <translation>将标签保存到不同的文件中</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="369"/>
        <source>&amp;Delete File</source>
        <translation>删除文件</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="369"/>
        <source>Delete current label file</source>
        <translation>删除当前的标签文件</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="386"/>
        <source>&amp;Change Output Dir</source>
        <translation>更改输出目录</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="386"/>
        <source>Change where annotations are loaded/saved</source>
        <translation>更改注释加载/保存的位置</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="394"/>
        <source>Save &amp;Automatically</source>
        <translation>自动保存</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="394"/>
        <source>Save automatically</source>
        <translation>自动保存</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="404"/>
        <source>Save With Image Data</source>
        <translation>保存包括图像数据在内的文件</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="404"/>
        <source>Save image data in label file</source>
        <translation>将图像数据保存在标签文件中</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="413"/>
        <source>&amp;Close</source>
        <translation>关闭</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="413"/>
        <source>Close current file</source>
        <translation>关闭当前文件</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="441"/>
        <source>Use System Clipboard</source>
        <translation>使用系统剪贴板</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="441"/>
        <source>Use system clipboard for copy and paste</source>
        <translation>使用系统剪贴板进行复制和粘贴</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="421"/>
        <source>Keep Previous Annotation</source>
        <translation>保留上一张图像的标注</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="460"/>
        <source>Create Polygons</source>
        <translation>创建多边形</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="460"/>
        <source>Start drawing polygons</source>
        <translation>开始绘制多边形</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="468"/>
        <source>Create Rectangle</source>
        <translation>创建矩形</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="468"/>
        <source>Start drawing rectangles</source>
        <translation>开始绘制矩形</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="484"/>
        <source>Create Circle</source>
        <translation>创建圆形</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="484"/>
        <source>Start drawing circles</source>
        <translation>开始绘制圆形</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="492"/>
        <source>Create Line</source>
        <translation>创建线条</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="492"/>
        <source>Start drawing lines</source>
        <translation>开始绘制线条</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="500"/>
        <source>Create Point</source>
        <translation>创建点</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="500"/>
        <source>Start drawing points</source>
        <translation>开始绘制点</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="508"/>
        <source>Create LineStrip</source>
        <translation>创建线条序列</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="508"/>
        <source>Start drawing linestrip. Ctrl+LeftClick ends creation.</source>
        <translation>开始绘制线条序列。使用 Ctrl+左键单击结束创建。</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="576"/>
        <source>Edit Object</source>
        <translation>编辑对象</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="576"/>
        <source>Move and edit the selected polygons</source>
        <translation>移动和编辑选定的多边形</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="584"/>
        <source>Group Selected Shapes</source>
        <translation>将选定的对象分组</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="584"/>
        <source>Group shapes by assigning a same group_id</source>
        <translation>通过分配相同的 group_id 来将对象分组</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="592"/>
        <source>Ungroup Selected Shapes</source>
        <translation>取消选定对象的分组</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="592"/>
        <source>Ungroup shapes</source>
        <translation>取消分组的对象</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="601"/>
        <source>Delete</source>
        <translation>删除</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="601"/>
        <source>Delete the selected polygons</source>
        <translation>删除所选多边形</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="609"/>
        <source>Duplicate Polygons</source>
        <translation>复制多边形</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="609"/>
        <source>Create a duplicate of the selected polygons</source>
        <translation>创建所选多边形的副本</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="617"/>
        <source>Copy Object</source>
        <translation>复制对象</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="617"/>
        <source>Copy selected polygons to clipboard</source>
        <translation>将所选多边形复制到剪贴板</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="625"/>
        <source>Paste Object</source>
        <translation>粘贴对象</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="625"/>
        <source>Paste copied polygons</source>
        <translation>粘贴复制的多边形</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="633"/>
        <source>Undo last point</source>
        <translation>撤销上一个点</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="633"/>
        <source>Undo last drawn point</source>
        <translation>撤销上一个绘制的点</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="641"/>
        <source>Remove Selected Point</source>
        <translation>删除所选点</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="641"/>
        <source>Remove selected point from polygon</source>
        <translation>从多边形中删除所选点</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="650"/>
        <source>Undo</source>
        <translation>撤销</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="650"/>
        <source>Undo last add and edit of shape</source>
        <translation>撤销上一次添加和编辑对象操作</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="760"/>
        <source>&amp;Documentation</source>
        <translation>帮助文档</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="760"/>
        <source>Show documentation</source>
        <translation>显示帮助文档</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="784"/>
        <source>Zoom in or out of the image. Also accessible with {} and {} from the canvas.</source>
        <translation>放大或缩小图像。也可从画布上使用 {} 和 {} 进行访问</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="784"/>
        <source>Ctrl+Wheel</source>
        <translation>Ctrl + 滚轮</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="799"/>
        <source>Zoom &amp;In</source>
        <translation>放大</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="799"/>
        <source>Increase zoom level</source>
        <translation>增加缩放级别</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="807"/>
        <source>&amp;Zoom Out</source>
        <translation>缩小</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="807"/>
        <source>Decrease zoom level</source>
        <translation>减小缩放级别</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="815"/>
        <source>&amp;Original size</source>
        <translation>原始大小</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="815"/>
        <source>Zoom to original size</source>
        <translation>缩放到原始大小</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="823"/>
        <source>&amp;Keep Previous Scale</source>
        <translation>保留先前的缩放比例</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="823"/>
        <source>Keep previous zoom scale</source>
        <translation>保留先前的缩放比例</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="847"/>
        <source>&amp;Fit Window</source>
        <translation>适应窗口</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="847"/>
        <source>Zoom follows window size</source>
        <translation>缩放跟随窗口大小</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="856"/>
        <source>Fit &amp;Width</source>
        <translation>适应宽度</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="856"/>
        <source>Zoom follows window width</source>
        <translation>缩放跟随窗口宽度</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="879"/>
        <source>&amp;Show Groups</source>
        <translation>显示分组</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="879"/>
        <source>Show shape groups</source>
        <translation>显示对象分组</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="889"/>
        <source>&amp;Show Texts</source>
        <translation>显示文本</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="889"/>
        <source>Show text above shapes</source>
        <translation>在对象上方显示文本</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1257"/>
        <source>&amp;Edit Label</source>
        <translation>编辑标签</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1257"/>
        <source>Modify the label of the selected polygon</source>
        <translation>修改所选多边形的标签</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1266"/>
        <source>Fill Drawing Polygon</source>
        <translation>填充绘制的多边形</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1266"/>
        <source>Fill polygon while drawing</source>
        <translation>绘制时填充多边形</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1278"/>
        <source>&amp;Auto Labeling</source>
        <translation>自动标注</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1278"/>
        <source>Auto Labeling</source>
        <translation>自动标注</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1478"/>
        <source>&amp;File</source>
        <translation>文件</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1478"/>
        <source>&amp;Edit</source>
        <translation>编辑</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1478"/>
        <source>&amp;View</source>
        <translation>视图</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1478"/>
        <source>&amp;Help</source>
        <translation>帮助</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1478"/>
        <source>Open &amp;Recent</source>
        <translation>最近打开的文件</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1884"/>
        <source>Mode:</source>
        <translation>当前模式：</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1885"/>
        <source>Shortcuts:</source>
        <translation>快捷键：</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="4207"/>
        <source>Invalid label</source>
        <translation>无效标签</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="4207"/>
        <source>Invalid label &apos;{}&apos; with validation type &apos;{}&apos;</source>
        <translation>无效标签'{}'，验证类型'{}'</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="2910"/>
        <source>Error saving label data</source>
        <translation>保存标签数据时出错</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="2910"/>
        <source>&lt;b&gt;%s&lt;/b&gt;</source>
        <translation>&lt;b&gt;%s&lt;/b&gt;</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="3375"/>
        <source>Error opening file</source>
        <translation>打开文件时出错</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="3313"/>
        <source>No such file: &lt;b&gt;%s&lt;/b&gt;</source>
        <translation>没有这个文件：&lt;b&gt;%s&lt;/b&gt;</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="3320"/>
        <source>Loading %s...</source>
        <translation>正在加载%s...</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="3335"/>
        <source>&lt;p&gt;&lt;b&gt;%s&lt;/b&gt;&lt;/p&gt;&lt;p&gt;Make sure &lt;i&gt;%s&lt;/i&gt; is a valid label file.</source>
        <translation>&lt;p&gt;&lt;b&gt;%s&lt;/b&gt;&lt;/p&gt;&lt;p&gt;确保&lt;i&gt;%s&lt;/i&gt;是一个有效的标签文件。</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="3382"/>
        <source>Error reading %s</source>
        <translation>读取%s时出错</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="3375"/>
        <source>&lt;p&gt;Make sure &lt;i&gt;{0}&lt;/i&gt; is a valid image file.&lt;br/&gt;Supported image formats: {1}&lt;/p&gt;</source>
        <translation>&lt;p&gt;确保&lt;i&gt;{0}&lt;/i&gt;是一个有效的图像文件。&lt;br/&gt;支持的图像格式：{1}&lt;/p&gt;</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="3463"/>
        <source>Loaded %s</source>
        <translation>已加载%s</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="3648"/>
        <source>Image &amp; Label files (%s)</source>
        <translation>图像和标签文件（%s）</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="3654"/>
        <source>%s - Choose Image or Label file</source>
        <translation>%s - 选择图像或标签文件</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="3672"/>
        <source>%s - Save/Load Annotations in Directory</source>
        <translation>%s - 在目录中保存/加载注释</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="3686"/>
        <source>%s . Annotations will be saved/loaded in %s</source>
        <translation>%s . 注释将保存/加载在%s中</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="3718"/>
        <source>%s - Choose File</source>
        <translation>%s - 选择文件</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="3743"/>
        <source>Label files (*%s)</source>
        <translation>标签文件（*%s）</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="3743"/>
        <source>Choose File</source>
        <translation>选择文件</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="3785"/>
        <source>You are about to permanently delete this label file, proceed anyway?</source>
        <translation>您即将永久删除此标签文件，是否继续？</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="3816"/>
        <source>Attention</source>
        <translation>注意</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="3880"/>
        <source>Save annotations to &quot;{self.filename!r}&quot; before closing?</source>
        <translation>在关闭之前将注释保存到&quot;{self.filename!r}&quot;？</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="3883"/>
        <source>Save annotations?</source>
        <translation>保存注释？</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="3953"/>
        <source>%s - Open Directory</source>
        <translation>%s - 打开目录</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1478"/>
        <source>&amp;Language</source>
        <translation>语言</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1713"/>
        <source>Please wait...</source>
        <translation>请稍候...</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1877"/>
        <source>Please restart the application to apply changes.</source>
        <translation>请重新启动应用程序以应用更改。</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="431"/>
        <source>Auto Use Last Label</source>
        <translation>自动使用上一个标签</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="431"/>
        <source>Toggle &quot;Auto Use Last Label&quot; mode</source>
        <translation>切换“自动使用上一个标签”模式</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="421"/>
        <source>Toggle &quot;Keep Previous Annotation&quot; mode</source>
        <translation>切换“保留上一个注释”模式</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="298"/>
        <source>&amp;Open Video</source>
        <translation>打开视频文件</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="298"/>
        <source>Open video file</source>
        <translation>打开视频文件</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="360"/>
        <source>&amp;Auto Run</source>
        <translation>一键运行</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="360"/>
        <source>Auto run all images at once</source>
        <translation>一次运行所有图片</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="291"/>
        <source>&amp;Open File</source>
        <translation>打开文件</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="476"/>
        <source>Create Rotation</source>
        <translation>创建旋转框</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="476"/>
        <source>Start drawing rotations</source>
        <translation>开始绘制旋转框</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="921"/>
        <source>&amp;Show Degress</source>
        <translation>显示旋转角度</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="921"/>
        <source>Show degrees above rotated shapes</source>
        <translation>显示旋转框上方的角度。</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="911"/>
        <source>&amp;Show Scores</source>
        <translation>显示置信度</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="911"/>
        <source>Show score inside shapes</source>
        <translation>在对象内部显示置信度</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="931"/>
        <source>&amp;Show KIE Linking</source>
        <translation>显示KIE链接</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="931"/>
        <source>Show KIE linking between key and value</source>
        <translation>显示键值之间的KIE链接</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="865"/>
        <source>&amp;Set Brightness Contrast</source>
        <translation>设置亮度对比度</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="873"/>
        <source>&amp;Set Cross Line</source>
        <translation>设置十字线</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="873"/>
        <source>Adjust cross line for mouse position</source>
        <translation>调整鼠标位置的十字线</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="831"/>
        <source>&amp;Keep Previous Brightness</source>
        <translation>保持当前亮度</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="831"/>
        <source>Keep previous brightness</source>
        <translation>保持当前亮度</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="839"/>
        <source>&amp;Keep Previous Contrast</source>
        <translation>保持当前对比度</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="839"/>
        <source>Keep previous contrast</source>
        <translation>保持当前对比度</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1743"/>
        <source>Attributes</source>
        <translation>属性</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1887"/>
        <source>Previous</source>
        <translation>上一张</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1888"/>
        <source>Next</source>
        <translation>下一张</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1889"/>
        <source>Rectangle</source>
        <translation>矩形框</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1890"/>
        <source>Polygon</source>
        <translation>多边形</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1891"/>
        <source>Rotation</source>
        <translation>旋转框</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="450"/>
        <source>Visibility Shapes</source>
        <translation>显示所有对象</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="450"/>
        <source>Toggle &quot;Visibility Shapes&quot; mode</source>
        <translation>切换“对象可见”模式</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="658"/>
        <source>Hide Selected Polygons</source>
        <translation>隐藏选中对象</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="658"/>
        <source>Hide selected polygons</source>
        <translation>隐藏选中对象</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="976"/>
        <source>&amp;Upload Attributes File</source>
        <translation>上传属性文件</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="976"/>
        <source>Upload Custom Attributes File</source>
        <translation>导入自定义属性文件</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1478"/>
        <source>&amp;Upload</source>
        <translation>导入</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="2049"/>
        <source>Invalid label &apos;{}&apos; with validation type: {}!
Reset the label as {}.</source>
        <translation>无效标签'{}'，有效标签'{}'！标签重置为'{}'.</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="3105"/>
        <source>X: %d, Y: %d | H: %d, W: %d</source>
        <translation>X: %d, Y: %d | H: %d, W: %d</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="3123"/>
        <source>X: %d, Y: %d</source>
        <translation>X: %d, Y: %d</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="4290"/>
        <source>Object Description</source>
        <translation>对象文本描述</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="4299"/>
        <source>Image Description</source>
        <translation>图像文本描述</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="4310"/>
        <source>Switch to Edit mode for description editing</source>
        <translation>切换到编辑模式以进行文本编辑</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="666"/>
        <source>Show Hidden Polygons</source>
        <translation>显示隐藏对象</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="666"/>
        <source>Show hidden polygons</source>
        <translation>显示隐藏对象</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="675"/>
        <source>&amp;Overview</source>
        <translation>统计总览</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1052"/>
        <source>&amp;Upload DOTA Annotations</source>
        <translation>导入 DOTA 标签</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1052"/>
        <source>Upload Custom DOTA Annotations</source>
        <translation>导入自定义 DOTA 标签</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1059"/>
        <source>&amp;Upload MASK Annotations</source>
        <translation>导入 MASK 标签</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1059"/>
        <source>Upload Custom MASK Annotations</source>
        <translation>导入自定义 MASK 标签</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1066"/>
        <source>&amp;Upload MOT Annotations</source>
        <translation>导入 MOT 标签</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1066"/>
        <source>Upload Custom Multi-Object-Tracking Annotations</source>
        <translation>导入自定义 MOT 标签</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1176"/>
        <source>&amp;Export DOTA Annotations</source>
        <translation>导出 DOTA 标签</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1176"/>
        <source>Export Custom DOTA Annotations</source>
        <translation>导出自定义 DOTA 标签</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1183"/>
        <source>&amp;Export MASK Annotations</source>
        <translation>导出 MASK 标签</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1190"/>
        <source>&amp;Export MOT Annotations</source>
        <translation>导出 MOT 标签</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1190"/>
        <source>Export Custom Multi-Object-Tracking Annotations</source>
        <translation>导出自定义 MOT 标签</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1478"/>
        <source>&amp;Export</source>
        <translation>导出</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="3092"/>
        <source>X: %d, Y: %d | H: %d, W: %d [%s: %d/%d]</source>
        <translation>X: %d, Y: %d | H: %d, W: %d [%s: %d/%d]</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="3112"/>
        <source>X: %d, Y: %d [%s: %d/%d]</source>
        <translation>X: %d, Y: %d [%s: %d/%d]</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="377"/>
        <source>&amp;Delete Image File</source>
        <translation>删除图像文件</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="377"/>
        <source>Delete current image file</source>
        <translation>删除当前图像文件</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1478"/>
        <source>&amp;Tool</source>
        <translation>工具</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="3812"/>
        <source>You are about to permanently delete this image file, proceed anyway?</source>
        <translation>您即将永久删除此图像文件，是否继续？</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="720"/>
        <source>&amp;Convert HBB to OBB</source>
        <translation>水平框转旋转框</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="720"/>
        <source>Perform conversion from horizontal bounding box to oriented bounding box</source>
        <translation>执行从水平边界框到旋转边界框的转换</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="675"/>
        <source>Show annotations statistics</source>
        <translation>展示标注统计结果</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="682"/>
        <source>&amp;Save Cropped Image</source>
        <translation>保存子图</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="983"/>
        <source>&amp;Upload YOLO-Hbb Annotations</source>
        <translation>导入 YOLO 水平框标签</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="983"/>
        <source>Upload Custom YOLO Horizontal Bounding Boxes Annotations</source>
        <translation>导入 YOLO 水平框标签</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="992"/>
        <source>&amp;Upload YOLO-Obb Annotations</source>
        <translation>导入 YOLO 旋转框标签</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="992"/>
        <source>Upload Custom YOLO Oriented Bounding Boxes Annotations</source>
        <translation>导入 YOLO 旋转框标签</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1001"/>
        <source>&amp;Upload YOLO-Seg Annotations</source>
        <translation>导入 YOLO 分割标签</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1001"/>
        <source>Upload Custom YOLO Segmentation Annotations</source>
        <translation>导入 YOLO 分割标签</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="900"/>
        <source>&amp;Show Labels</source>
        <translation>显示标签</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="900"/>
        <source>Show label inside shapes</source>
        <translation>显示标签</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="728"/>
        <source>&amp;Convert OBB to HBB</source>
        <translation>旋转框转水平框</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="728"/>
        <source>Perform conversion from oriented bounding box to horizontal bounding box</source>
        <translation>执行从旋转边界框到水平边界框的转换</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="682"/>
        <source>Save cropped image. (Support rectangle/rotation/polygon shape_type)</source>
        <translation>保存截取的子图（支持矩形框/多边形/旋转框对象）</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="699"/>
        <source>&amp;Label Manager</source>
        <translation>标签管理器</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="712"/>
        <source>&amp;Union Selection</source>
        <translation>合并选中对象</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="712"/>
        <source>Union multiple selected rectangle shapes</source>
        <translation>合并多个选中的矩形框对象</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="736"/>
        <source>&amp;Convert Polygon to HBB</source>
        <translation>多边形转水平框</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="736"/>
        <source>Perform conversion from polygon to horizontal bounding box</source>
        <translation>执行从多边形到水平边界框的转换</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="962"/>
        <source>&amp;Upload Image Flags File</source>
        <translation>上传图像标记文件</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="962"/>
        <source>Upload Custom Image Flags File</source>
        <translation>上传自定义图像标记文件</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="969"/>
        <source>&amp;Upload Label Flags File</source>
        <translation>上传标签标记文件</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="969"/>
        <source>Upload Custom Label Flags File</source>
        <translation>上传自定义标签标记文件</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1008"/>
        <source>&amp;Upload YOLO-Pose Annotations</source>
        <translation>导入 YOLO 关键点标签</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1008"/>
        <source>Upload Custom YOLO Pose Annotations</source>
        <translation>导入自定义 YOLO 关键点标签</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1015"/>
        <source>&amp;Upload VOC Detection Annotations</source>
        <translation>导入 VOC 检测框标签</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1015"/>
        <source>Upload Custom Pascal VOC Detection Annotations</source>
        <translation>导入自定义 VOC 检测框标签</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1022"/>
        <source>&amp;Upload VOC Segmentation Annotations</source>
        <translation>导入 VOC 分割标签</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1022"/>
        <source>Upload Custom Pascal VOC Segmentation Annotations</source>
        <translation>导入自定义 VOC 分割标签</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1029"/>
        <source>&amp;Upload COCO Detection Annotations</source>
        <translation>导入 COCO 检测框标签</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1029"/>
        <source>Upload Custom COCO Detection Annotations</source>
        <translation>导入自定义 COCO 检测框标签</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="960"/>
        <source>&amp;Upload COCO Segmentation Annotations</source>
        <translation type="obsolete">导入 COCO 分割标签</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="960"/>
        <source>Upload Custom COCO Segmentation Annotations</source>
        <translation type="obsolete">导入自定义 COCO 分割标签</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1073"/>
        <source>&amp;Upload ODVG Annotations</source>
        <translation>导入 ODVG 标签</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1073"/>
        <source>Upload Custom Object Detection Visual Grounding Annotations</source>
        <translation>导入自定义目标检测视觉 Grounding 标签</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1082"/>
        <source>&amp;Upload PPOCR-Rec Annotations</source>
        <translation>导入 PPOCR-Rec 标签</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1082"/>
        <source>Upload Custom PPOCR Recognition Annotations</source>
        <translation>导入自定义 PPOCR 识别标签</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1089"/>
        <source>&amp;Upload PPOCR-KIE Annotations</source>
        <translation>导入 PPOCR-KIE 标签</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1089"/>
        <source>Upload Custom PPOCR Key Information Extraction (KIE - Semantic Entity Recognition &amp; Relation Extraction) Annotations</source>
        <translation>导入自定义 PPOCR 关键信息提取标签（包含语义实体识别和关系提取）</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1107"/>
        <source>&amp;Export YOLO-Hbb Annotations</source>
        <translation>导出 YOLO 水平框标签</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1107"/>
        <source>Export Custom YOLO Horizontal Bounding Boxes Annotations</source>
        <translation>导入自定义 YOLO 水平边界框标签</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1116"/>
        <source>&amp;Export YOLO-Obb Annotations</source>
        <translation>导出 YOLO 旋转框标签</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1116"/>
        <source>Export Custom YOLO Oriented Bounding Boxes Annotations</source>
        <translation>导入自定义 YOLO 有向边界框标签</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1125"/>
        <source>&amp;Export YOLO-Seg Annotations</source>
        <translation>导出 YOLO 分割标签</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1125"/>
        <source>Export Custom YOLO Segmentation Annotations</source>
        <translation>导出自定义 YOLO 分割标签</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1132"/>
        <source>&amp;Export YOLO-Pose Annotations</source>
        <translation>导出 YOLO 关键点标签</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1132"/>
        <source>Export Custom YOLO Pose Annotations</source>
        <translation>导出自定义 YOLO 关键点标签</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1139"/>
        <source>&amp;Export VOC Detection Annotations</source>
        <translation>导出 VOC 检测框标签</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1139"/>
        <source>Export Custom PASCAL VOC Detection Annotations</source>
        <translation>导出自定义 VOC 检测框标签</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1146"/>
        <source>&amp;Export VOC Segmentation Annotations</source>
        <translation>导出 VOC 分割标签</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1146"/>
        <source>Export Custom PASCAL VOC Segmentation Annotations</source>
        <translation>导出自定义 PASCAL VOC 分割标签</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1153"/>
        <source>&amp;Export COCO Detection Annotations</source>
        <translation>导出 COCO 检测框标签</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1153"/>
        <source>Export Custom COCO Rectangle Annotations</source>
        <translation>导出自定义 COCO 检测框标签</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1082"/>
        <source>&amp;Export COCO Segmentation Annotations</source>
        <translation type="obsolete">导出 COCO 分割标签</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1082"/>
        <source>Export Custom COCO Segmentation Annotations</source>
        <translation type="obsolete">导出自定义 COCO 分割标签</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1183"/>
        <source>Export Custom MASK Annotations - RGB/Gray</source>
        <translation>导出自定义掩码标签（支持彩色和灰度图）</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1206"/>
        <source>&amp;Export ODVG Annotations</source>
        <translation>导出 ODVG 标签</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1206"/>
        <source>Export Custom Object Detection Visual Grounding Annotations</source>
        <translation>导出自定义目标检测视觉 Grounding 标签</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1215"/>
        <source>&amp;Export PPOCR-Rec Annotations</source>
        <translation>导出 PPOCR-Rec 标签</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1215"/>
        <source>Export Custom PPOCR Recognition Annotations</source>
        <translation>导出自定义 PPOCR 识别标签</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1222"/>
        <source>&amp;Export PPOCR-KIE Annotations</source>
        <translation>导出 PPOCR-KIE 标签</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1222"/>
        <source>Export Custom PPOCR Key Information Extraction (KIE - Semantic Entity Recognition &amp; Relation Extraction) Annotations</source>
        <translation>导出自定义 PPOCR 关键信息提取标签（包括语义实体识别和关系提取）</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="2932"/>
        <source>Error pasting shapes</source>
        <translation>粘贴对象失败</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="2932"/>
        <source>Error decoding shapes: %s</source>
        <translation>对象解码失败：%s</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="2285"/>
        <source>Copied</source>
        <translation>已复制</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="2285"/>
        <source>The information has been copied to the clipboard.</source>
        <translation>环境信息已复制到剪切板</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="699"/>
        <source>Manage Labels: Rename, Delete, Adjust Color</source>
        <translation>标签管理：支持重命名、删除、色彩调整</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="705"/>
        <source>&amp;Group ID Manager</source>
        <translation>群组编号管理器</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="705"/>
        <source>Manage Group ID</source>
        <translation>管理群组编号</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1197"/>
        <source>&amp;Export MOTS Annotations</source>
        <translation>导出 MOTS 标签</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1197"/>
        <source>Export Custom Multi-Object-Tracking-Segmentation Annotations</source>
        <translation>导出自定义多目标追踪分割标签</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="312"/>
        <source>Open next image</source>
        <translation>打开下一张图像</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="320"/>
        <source>Open prev image</source>
        <translation>打开上一张图像</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="328"/>
        <source>&amp;Next Unchecked Image</source>
        <translation>打开下一张未检查的图像</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="328"/>
        <source>Open next unchecked image</source>
        <translation>打开下一张未检查的图像</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="336"/>
        <source>&amp;Prev Unchecked Image</source>
        <translation>打开上一张未检查的图像</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="336"/>
        <source>Open previous unchecked image</source>
        <translation>打开上一张未检查的图像</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="773"/>
        <source>&amp;Loop through labels</source>
        <translation>遍历标签</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="773"/>
        <source>Loop through labels</source>
        <translation>遍历标签</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1045"/>
        <source>&amp;Upload COCO Keypoint Annotations</source>
        <translation>导入 COCO 关键点标签</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1045"/>
        <source>Upload Custom COCO Keypoint Annotations</source>
        <translation>导入自定义 COCO 关键点标签</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1169"/>
        <source>&amp;Export COCO Keypoint Annotations</source>
        <translation>导出 COCO 关键点标签</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1169"/>
        <source>Export Custom COCO Keypoint Annotations</source>
        <translation>导出自定义 COCO 关键点标签</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="752"/>
        <source>ChatBot</source>
        <translation>聊天机器人</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="752"/>
        <source>Open chatbot dialog</source>
        <translation>打开聊天机器人对话框</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1886"/>
        <source>Chatbot</source>
        <translation>聊天机器人</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="744"/>
        <source>&amp;Convert Polygon to OBB</source>
        <translation>多边形转旋转框</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="744"/>
        <source>Perform conversion from polygon to oriented bounding box</source>
        <translation>执行从多边形到定向边界框的转换</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="766"/>
        <source>&amp;About</source>
        <translation>关于</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="766"/>
        <source>Open about dialog</source>
        <translation>打开关于对话框</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1098"/>
        <source>&amp;Upload VLM-R1 OVD Annotations</source>
        <translation>导入 VLM-R1 OVD 标签</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1098"/>
        <source>Upload Custom VLM-R1 OVD Annotations</source>
        <translation>导入自定义 VLM-R1 OVD 标签</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1231"/>
        <source>&amp;Export VLM-R1 OVD Annotations</source>
        <translation>导出 VLM-R1 OVD 标签</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1231"/>
        <source>Export Custom VLM-R1 OVD Annotations</source>
        <translation>导出自定义 VLM-R1 OVD 标签</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1036"/>
        <source>&amp;Upload COCO Instance Segmentation Annotations</source>
        <translation>上传 COCO 实例分割标签</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1036"/>
        <source>Upload Custom COCO Instance Segmentation Annotations</source>
        <translation>上传自定义 COCO 实例分割标签</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1160"/>
        <source>&amp;Export COCO Instance Segmentation Annotations</source>
        <translation>导出 COCO 实例分割标签</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="1160"/>
        <source>Export Custom COCO Instance Segmentation Annotations</source>
        <translation>导出自定义 COCO 实例分割标签</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="690"/>
        <source>&amp;Digit Shortcut Manager</source>
        <translation>数字快捷键管理器</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="690"/>
        <source>Manage Digit Shortcuts: Assign Drawing Modes and Labels to Number Keys</source>
        <translation>数字快捷键管理器：为数字键分配绘制模式和标签</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="516"/>
        <source>Digit Shortcut 0</source>
        <translation>数字快捷键 0</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="522"/>
        <source>Digit Shortcut 1</source>
        <translation>数字快捷键 1</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="528"/>
        <source>Digit Shortcut 2</source>
        <translation>数字快捷键 2</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="534"/>
        <source>Digit Shortcut 3</source>
        <translation>数字快捷键 3</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="540"/>
        <source>Digit Shortcut 4</source>
        <translation>数字快捷键 4</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="546"/>
        <source>Digit Shortcut 5</source>
        <translation>数字快捷键 5</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="552"/>
        <source>Digit Shortcut 6</source>
        <translation>数字快捷键 6</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="558"/>
        <source>Digit Shortcut 7</source>
        <translation>数字快捷键 7</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="564"/>
        <source>Digit Shortcut 8</source>
        <translation>数字快捷键 8</translation>
    </message>
    <message>
        <location filename="../../views/labeling/label_widget.py" line="570"/>
        <source>Digit Shortcut 9</source>
        <translation>数字快捷键 9</translation>
    </message>
</context>
<context>
    <name>Model</name>
    <message>
        <location filename="../../services/auto_labeling/__base__/yolo.py" line="52"/>
        <source>Rectangle</source>
        <translation>矩形框</translation>
    </message>
    <message>
        <location filename="../../services/auto_labeling/__base__/yolo.py" line="51"/>
        <source>Polygon</source>
        <translation>多边形</translation>
    </message>
    <message>
        <location filename="../../services/auto_labeling/__base__/yolo.py" line="50"/>
        <source>Point</source>
        <translation>点</translation>
    </message>
    <message>
        <location filename="../../services/auto_labeling/yolox_dwpose.py" line="54"/>
        <source>Could not download or initialize YOLOX-L model.</source>
        <translation>无法下载或初始化 YOLOX-L 模型。</translation>
    </message>
    <message>
        <location filename="../../services/auto_labeling/yolox_dwpose.py" line="63"/>
        <source>Could not download or initialize DWPose model.</source>
        <translation>无法下载或初始化 DWPose 模型。</translation>
    </message>
    <message>
        <location filename="../../services/auto_labeling/grounding_sam2.py" line="60"/>
        <source>Rotation</source>
        <translation>旋转框</translation>
    </message>
    <message>
        <location filename="../../services/auto_labeling/clrnet.py" line="40"/>
        <source>Line</source>
        <translation>线段</translation>
    </message>
    <message>
        <location filename="../../services/auto_labeling/clrnet.py" line="50"/>
        <source>Could not download or initialize CLRNet model.</source>
        <translation>无法下载或初始化 CLRNet 模型。</translation>
    </message>
    <message>
        <location filename="../../services/auto_labeling/yolov8_sahi.py" line="46"/>
        <source>Could not download or initialize YOLOv8 model.</source>
        <translation>无法下载或初始化 YOLOv8 模型。</translation>
    </message>
    <message>
        <location filename="../../services/auto_labeling/grounding_sam2.py" line="527"/>
        <source>Invalid model_type in GroundingDINO model.</source>
        <translation>GroundingDINO模型中的无效model_type。</translation>
    </message>
    <message>
        <location filename="../../services/auto_labeling/model.py" line="54"/>
        <source>Config file not found: {model_config}</source>
        <translation>配置文件未找到：{model_config}。</translation>
    </message>
    <message>
        <location filename="../../services/auto_labeling/model.py" line="64"/>
        <source>Unknown config type: {type}</source>
        <translation>未知的配置类型：{type}。</translation>
    </message>
    <message>
        <location filename="../../services/auto_labeling/model.py" line="150"/>
        <source>Model path not found: {model_path}</source>
        <translation>模型路径未找到：{model_path}。</translation>
    </message>
    <message>
        <location filename="../../services/auto_labeling/model.py" line="155"/>
        <source>Downloading model from registry...</source>
        <translation>从注册表下载模型...</translation>
    </message>
    <message>
        <location filename="../../services/auto_labeling/model.py" line="237"/>
        <source>Downloading {download_url}: {percent}%</source>
        <translation>正在下载 {download_url}：{percent}%</translation>
    </message>
    <message>
        <location filename="../../services/auto_labeling/yolov5_sam.py" line="59"/>
        <source>Could not download or initialize YOLOv5 model.</source>
        <translation>无法下载或初始化 YOLOv5 模型。</translation>
    </message>
    <message>
        <location filename="../../services/auto_labeling/rtmdet_pose.py" line="41"/>
        <source>Could not download or initialize RTMDet model.</source>
        <translation>无法下载或初始化 RTMDet 模型。</translation>
    </message>
    <message>
        <location filename="../../services/auto_labeling/rtmdet_pose.py" line="50"/>
        <source>Could not download or initialize Pose model.</source>
        <translation>无法下载或初始化 Pose 模型。</translation>
    </message>
</context>
<context>
    <name>ModelManager</name>
    <message>
        <location filename="../../services/auto_labeling/model_manager.py" line="135"/>
        <source>Model loaded. Ready for labeling.</source>
        <translation>模型已加载。准备好进行数据标注。</translation>
    </message>
    <message>
        <location filename="../../services/auto_labeling/model_manager.py" line="164"/>
        <source>Error in loading custom model: Invalid path.</source>
        <translation>加载自定义模型时出错：无效路径。</translation>
    </message>
    <message>
        <location filename="../../services/auto_labeling/model_manager.py" line="180"/>
        <source>Error in loading custom model: Invalid config file.</source>
        <translation>加载自定义模型时出错：无效的配置文件。</translation>
    </message>
    <message>
        <location filename="../../services/auto_labeling/model_manager.py" line="211"/>
        <source>Error in loading custom model: Invalid config file format.</source>
        <translation>加载自定义模型时出错：无效的配置文件格式。</translation>
    </message>
    <message>
        <location filename="../../services/auto_labeling/model_manager.py" line="270"/>
        <source>No model selected.</source>
        <translation>未选择模型。</translation>
    </message>
    <message>
        <location filename="../../services/auto_labeling/model_manager.py" line="284"/>
        <source>Error in loading model: Invalid model name.</source>
        <translation>加载模型时出错：无效的模型名称。</translation>
    </message>
    <message>
        <location filename="../../services/auto_labeling/model_manager.py" line="1991"/>
        <source>Model is not loaded. Choose a mode to continue.</source>
        <translation>模型尚未加载。选择一个模式以继续。</translation>
    </message>
    <message>
        <location filename="../../services/auto_labeling/model_manager.py" line="1970"/>
        <source>Finished inferencing AI model. Check the result.</source>
        <translation>完成 AI 模型推理，请检查结果。</translation>
    </message>
    <message>
        <location filename="../../services/auto_labeling/model_manager.py" line="1995"/>
        <source>Inferencing AI model. Please wait...</source>
        <translation>正在进行 AI 模型推理，请稍等...</translation>
    </message>
    <message>
        <location filename="../../services/auto_labeling/model_manager.py" line="2005"/>
        <source>Another model is being executed. Please wait for it to finish.</source>
        <translation>另一个模型正在执行，请耐心等待。</translation>
    </message>
</context>
<context>
    <name>OverviewDialog</name>
    <message>
        <location filename="../../views/labeling/widgets/overview_dialog.py" line="109"/>
        <source>Overview</source>
        <translation>统计总览</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/overview_dialog.py" line="160"/>
        <source>Export</source>
        <translation>导出</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/overview_dialog.py" line="491"/>
        <source>Show Shape Infos</source>
        <translation>显示对象信息</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/overview_dialog.py" line="211"/>
        <source>Loading...</source>
        <translation>加载中。。。</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/overview_dialog.py" line="211"/>
        <source>Cancel</source>
        <translation>取消</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/overview_dialog.py" line="219"/>
        <source>Progress</source>
        <translation>进度</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/overview_dialog.py" line="405"/>
        <source>Select Directory</source>
        <translation>选择目录</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/overview_dialog.py" line="476"/>
        <source>Error occurred while exporting annotations statistics file.</source>
        <translation>导出标签统计文件时发生错误。</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/overview_dialog.py" line="493"/>
        <source>Show Label Infos</source>
        <translation>显示标签信息</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/overview_dialog.py" line="459"/>
        <source>Exporting annotations successfully!
Results have been saved to:
%s</source>
        <translation>导出标签成功！
结果已保存到：
%s</translation>
    </message>
</context>
<context>
    <name>TextInputDialog</name>
    <message>
        <location filename="../../views/labeling/utils/batch.py" line="311"/>
        <source>Cancel</source>
        <translation>取消</translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/batch.py" line="57"/>
        <source>Enter Text Prompt</source>
        <translation>输入文本提示词</translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/batch.py" line="65"/>
        <source>Please enter your text prompt:</source>
        <translation>请输入您的文本提示词：</translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/batch.py" line="72"/>
        <source>Enter prompt here...</source>
        <translation>请输入您的文本提示词：</translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/batch.py" line="168"/>
        <source>Processing completed successfully!</source>
        <translation>处理完成！</translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/batch.py" line="300"/>
        <source>Error occurred while processing images!</source>
        <translation>处理图片时发生错误！</translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/batch.py" line="311"/>
        <source>Processing...</source>
        <translation>处理中。。。</translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/batch.py" line="319"/>
        <source>Batch Processing</source>
        <translation>批量推理</translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/batch.py" line="402"/>
        <source>Model is not loaded. Choose a mode to continue.</source>
        <translation>模型尚未加载。选择一个模型以继续。</translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/batch.py" line="416"/>
        <source>Invalid model type, please choose a valid model_type to run.</source>
        <translation>无效的模型类型，请重新选择一个有效的模型运行。</translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/batch.py" line="425"/>
        <source>Confirmation</source>
        <translation>确认</translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/batch.py" line="426"/>
        <source>Do you want to process all images?</source>
        <translation>是否选择一次运行所有图片？</translation>
    </message>
</context>
<context>
    <name>UploadCocoThread</name>
    <message>
        <location filename="../../views/labeling/utils/upload.py" line="1074"/>
        <source>Please load an image folder before proceeding!</source>
        <translation type="obsolete">请先加载图像！</translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/upload.py" line="234"/>
        <source>Select a custom annotation file (Label.txt)</source>
        <translation>选择一个自定义标签文件（Label.txt）</translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/upload.py" line="246"/>
        <source>Select a custom annotation file (ppocr_kie.json)</source>
        <translation>选择一个自定义标签文件（ppocr_kie.json）</translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/upload.py" line="166"/>
        <source>Please select a specific kie file!</source>
        <translation type="obsolete">请选择一个特定的 kie 文件！</translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/upload.py" line="1186"/>
        <source>Warning</source>
        <translation>警告</translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/upload.py" line="1187"/>
        <source>Current annotation will be lost</source>
        <translation>当前标注将会丢失！</translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/upload.py" line="1188"/>
        <source>You are going to upload new annotations to this task. Continue?</source>
        <translation>您将要上传新的标签到此任务。是否覆盖？</translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/upload.py" line="1201"/>
        <source>Uploading...</source>
        <translation>导入中。。。</translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/upload.py" line="1201"/>
        <source>Cancel</source>
        <translation>取消</translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/upload.py" line="1209"/>
        <source>Progress</source>
        <translation>进度</translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/upload.py" line="901"/>
        <source>Uploading annotations successfully!</source>
        <translation>导入标签成功！</translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/upload.py" line="1274"/>
        <source>Error occurred while uploading annotations!</source>
        <translation>导入标签时发生错误！</translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/upload.py" line="328"/>
        <source>Select a specific OD file</source>
        <translation>选择一个特定的 OD 文件</translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/upload.py" line="263"/>
        <source>Please select a specific OD file!</source>
        <translation type="obsolete">请选择一个特定的 OD 文件！</translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/upload.py" line="1102"/>
        <source>Select a specific classes file</source>
        <translation>请选择一个具体的标签文件</translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/upload.py" line="357"/>
        <source>Please select a specific classes file!</source>
        <translation type="obsolete">请选择一个具体的标签文件</translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/upload.py" line="418"/>
        <source>Select a specific gt file</source>
        <translation>选择一个特定的 gt 文件</translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/upload.py" line="426"/>
        <source>Please select a specific gt file!</source>
        <translation>请选择一个特定的 gt 文件！</translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/upload.py" line="518"/>
        <source>Select a specific color_map file</source>
        <translation>选择一个特定的颜色映射表</translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/upload.py" line="483"/>
        <source>Please select a specific color_map file!</source>
        <translation type="obsolete">请选择一个特定的颜色映射表</translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/upload.py" line="1116"/>
        <source>Upload Options</source>
        <translation>上传选项</translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/upload.py" line="1135"/>
        <source>Select Upload Folder</source>
        <translation>选择上传文件夹</translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/upload.py" line="1146"/>
        <source>Browse</source>
        <translation>浏览</translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/upload.py" line="1163"/>
        <source>OK</source>
        <translation>确认</translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/upload.py" line="850"/>
        <source>Select a custom coco annotation file</source>
        <translation>请选择自定义的 coco 标签文件</translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/upload.py" line="835"/>
        <source>Please select a specific coco annotation file!</source>
        <translation type="obsolete">请选择一个特定的 coco 标签文件！</translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/upload.py" line="1083"/>
        <source>Select a specific yolo-pose config file</source>
        <translation>选择一个特定的 YOLO-Pose 配置文件</translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/upload.py" line="1116"/>
        <source>Please select a specific config file!</source>
        <translation type="obsolete">请选择一个特定的配置文件</translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/upload.py" line="1254"/>
        <source>Upload completed successfully!</source>
        <translation>上传完成！</translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/upload.py" line="1284"/>
        <source>Select a specific shape attributes file</source>
        <translation>选择一个特定的对象属性文件</translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/upload.py" line="1304"/>
        <source>Please select a specific shape attributes file!</source>
        <translation type="obsolete">请选择一个特定的对象属性文件！</translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/upload.py" line="1311"/>
        <source>Uploading shape attributes file successfully!</source>
        <translation>上传对象属性文件成功！</translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/upload.py" line="1322"/>
        <source>Error occurred while uploading shape attributes file!</source>
        <translation>上传对象属性文件时发生错误！</translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/upload.py" line="1376"/>
        <source>Select a specific flags file</source>
        <translation>选择一个特定的标志文件</translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/upload.py" line="1408"/>
        <source>Please select a specific flags file!</source>
        <translation type="obsolete">请选择一个特定的标志文件！</translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/upload.py" line="1395"/>
        <source>Uploading flags file successfully!</source>
        <translation>上传标志文件成功！</translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/upload.py" line="1404"/>
        <source>Error occurred while uploading flags file!</source>
        <translation>上传标志文件时发生错误！</translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/upload.py" line="1051"/>
        <source>Uploading annotations successfully!
Results have been saved to:
%s</source>
        <translation>上传标签成功！
结果已保存到：
%s</translation>
    </message>
    <message>
        <location filename="../../views/labeling/utils/upload.py" line="133"/>
        <source>Select a custom vlm_r1_ovd annotation file</source>
        <translation>选择自定义的 vlm_r1_ovd 标签文件</translation>
    </message>
</context>
<context>
    <name>ZoomWidget</name>
    <message>
        <location filename="../../views/labeling/widgets/zoom_widget.py" line="11"/>
        <source>Zoom Level</source>
        <translation>缩放级别</translation>
    </message>
</context>
<context>
    <name>auto_labeling_form</name>
    <message>
        <location filename="../../views/labeling/widgets/auto_labeling/auto_labeling_ui.py" line="169"/>
        <source>Form</source>
        <translation>表格</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/auto_labeling/auto_labeling_ui.py" line="165"/>
        <source>Auto</source>
        <translation type="obsolete">自动</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/auto_labeling/auto_labeling_ui.py" line="170"/>
        <source>No Model</source>
        <translation>没有模型</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/auto_labeling/auto_labeling_ui.py" line="171"/>
        <source>Output</source>
        <translation>输出</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/auto_labeling/auto_labeling_ui.py" line="172"/>
        <source>polygon</source>
        <translation>多边形</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/auto_labeling/auto_labeling_ui.py" line="173"/>
        <source>rectangle</source>
        <translation>矩形</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/auto_labeling/auto_labeling_ui.py" line="195"/>
        <source>Run (i)</source>
        <translation>运行（i）</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/auto_labeling/auto_labeling_ui.py" line="201"/>
        <source>Point (q)</source>
        <translation>+点（q）</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/auto_labeling/auto_labeling_ui.py" line="202"/>
        <source>Point (e)</source>
        <translation>-点（e）</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/auto_labeling/auto_labeling_ui.py" line="203"/>
        <source>+Rect</source>
        <translation>+矩形</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/auto_labeling/auto_labeling_ui.py" line="209"/>
        <source>Ready!</source>
        <translation>准备好了！</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/auto_labeling/auto_labeling_ui.py" line="174"/>
        <source>rotation</source>
        <translation>旋转框</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/auto_labeling/auto_labeling_ui.py" line="204"/>
        <source>Clear (b)</source>
        <translation>清除（b）</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/auto_labeling/auto_labeling_ui.py" line="196"/>
        <source>Enter text prompt here, e.g., person.car.bicycle</source>
        <translation>在此输入文本提示，例如：person.car.bicycle</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/auto_labeling/auto_labeling_ui.py" line="197"/>
        <source>Send</source>
        <translation>发送</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/auto_labeling/auto_labeling_ui.py" line="198"/>
        <source>Box threshold</source>
        <translation>边框阈值</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/auto_labeling/auto_labeling_ui.py" line="199"/>
        <source>Confidence</source>
        <translation>置信度</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/auto_labeling/auto_labeling_ui.py" line="200"/>
        <source>IoU</source>
        <translation>交并比</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/auto_labeling/auto_labeling_ui.py" line="208"/>
        <source>Reset Tracker</source>
        <translation>重置跟踪器</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/auto_labeling/auto_labeling_ui.py" line="175"/>
        <source>coarse_grained_prompt</source>
        <translation>coarse_grained_prompt</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/auto_labeling/auto_labeling_ui.py" line="176"/>
        <source>fine_grained_prompt</source>
        <translation>fine_grained_prompt</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/auto_labeling/auto_labeling_ui.py" line="177"/>
        <source>caption</source>
        <translation>caption</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/auto_labeling/auto_labeling_ui.py" line="178"/>
        <source>detailed_cap</source>
        <translation>detailed_cap</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/auto_labeling/auto_labeling_ui.py" line="179"/>
        <source>more_detailed_cap</source>
        <translation>more_detailed_cap</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/auto_labeling/auto_labeling_ui.py" line="180"/>
        <source>od</source>
        <translation>od</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/auto_labeling/auto_labeling_ui.py" line="181"/>
        <source>region_proposal</source>
        <translation>region_proposal</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/auto_labeling/auto_labeling_ui.py" line="182"/>
        <source>dense_region_cap</source>
        <translation>dense_region_cap</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/auto_labeling/auto_labeling_ui.py" line="183"/>
        <source>cap_to_pg</source>
        <translation>cap_to_pg</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/auto_labeling/auto_labeling_ui.py" line="184"/>
        <source>refer_exp_seg</source>
        <translation>refer_exp_seg</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/auto_labeling/auto_labeling_ui.py" line="185"/>
        <source>region_to_seg</source>
        <translation>region_to_seg</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/auto_labeling/auto_labeling_ui.py" line="186"/>
        <source>ovd</source>
        <translation>ovd</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/auto_labeling/auto_labeling_ui.py" line="187"/>
        <source>region_to_cat</source>
        <translation>region_to_cat</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/auto_labeling/auto_labeling_ui.py" line="188"/>
        <source>region_to_desc</source>
        <translation>region_to_desc</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/auto_labeling/auto_labeling_ui.py" line="189"/>
        <source>ocr</source>
        <translation>ocr</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/auto_labeling/auto_labeling_ui.py" line="190"/>
        <source>ocr_with_region</source>
        <translation>ocr_with_region</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/auto_labeling/auto_labeling_ui.py" line="205"/>
        <source>Finish (f)</source>
        <translation>完成（f）</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/auto_labeling/auto_labeling_ui.py" line="198"/>
        <source>Overwrite Shapes</source>
        <translation type="obsolete">覆盖对象</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/auto_labeling/auto_labeling_ui.py" line="191"/>
        <source>GroundingDino_1_6_Pro</source>
        <translation>GroundingDino_1_6_Pro</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/auto_labeling/auto_labeling_ui.py" line="192"/>
        <source>GroundingDino_1_6_Edge</source>
        <translation>GroundingDino_1_6_Edge</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/auto_labeling/auto_labeling_ui.py" line="193"/>
        <source>GroundingDino_1_5_Pro</source>
        <translation>GroundingDino_1_5_Pro</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/auto_labeling/auto_labeling_ui.py" line="194"/>
        <source>GroundingDino_1_5_Edge</source>
        <translation>GroundingDino_1_5_Edge</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/auto_labeling/auto_labeling_ui.py" line="206"/>
        <source>Replace (On)</source>
        <translation>标签覆盖（开启）</translation>
    </message>
    <message>
        <location filename="../../views/labeling/widgets/auto_labeling/auto_labeling_ui.py" line="207"/>
        <source>Set API Token</source>
        <translation>设置 API 密钥</translation>
    </message>
</context>
</TS>
