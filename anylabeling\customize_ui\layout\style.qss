/*
 * X-AnyLabeling - 全局按钮样式表
 *
 * 这份 QSS 文件为 QPushButton 提供了统一且现代的外观。
 * 样式涵盖了按钮的各种交互状态，如悬停、按下、聚焦和禁用。
 *
 * 主色调:
 * - 主色 (蓝色): #3498d1
 * - 悬停色: #5dade2
 * - 按下色: #2874a6
 * - 禁用背景色: #d5d8dc
 * - 禁用文字色: #a1a1a1
 * - 边框色: #aab7b8
 * - 聚焦轮廓色: #f39c12
 */

/* --- QPushButton 基础样式 --- */
QPushButton {
    background-color: #053d62 !important; /* 主背景色 - 蓝色 */
    color: white !important; /* 文字颜色 - 白色 */
    border: 1px solid #043353 !important; /* 边框颜色 */
    border-radius: 5px !important; /* 圆角大小 */
    padding: 8px 16px !important; /* 内边距 (垂直8px, 水平16px) */
    font-size: 20px !important; /* 字体大小 */
    font-weight: bold !important; /* 字体加粗 */
    outline: none !important; /* 移除默认轮廓 */
}



/* --- 悬停状态 --- */
#pushButton_32:hover {
    background-color: #f611c4 !important; /* 悬停时变亮的背景色 */
    border: 1px solid #5dade2 !important;
}

/* --- 按下状态 --- */
QPushButton:pressed {
    background-color: #2874a6; /* 按下时变深的背景色 */
    border: 1px solid #21618c;
    padding-top: 9px; /* 向下轻微移动，模拟按下效果 */
    padding-bottom: 7px;
}

/* --- 聚焦状态 --- */
/* 当通过键盘Tab键选中时，显示一个橙色轮廓 */
QPushButton:focus {
    border: 2px solid #f39c12; /* 聚焦时的轮廓颜色 - 橙色 */
}

/* --- 禁用状态 --- */
QPushButton:disabled {
    background-color: #d5d8d1; /* 禁用时的背景色 - 灰色 */
    color: #a1a1a1; /* 禁用时的文字颜色 - 深灰色 */
    border: 1px solid #bdc3c7; /* 禁用时的边框颜色 */
    cursor: not-allowed; /* 鼠标指针变为禁用图标 */

}

#label_230 {
    color: #7d5003;
}


/* --- 带特定 objectName 的按钮示例 --- */
/* 
   您可以为特定的按钮设置 objectName，然后单独为它定义样式。
   例如, 在 Qt Designer 中设置一个按钮的 objectName 为 "primaryButton"。
*/

/*
#primaryButton {
    background-color: #27ae60; 
    border: 1px solid #229954;
}

#primaryButton:hover {
    background-color: #2ecc72;
}

#primaryButton:pressed {
    background-color: #1e8449;
}
*/
