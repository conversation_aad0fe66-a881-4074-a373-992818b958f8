# utils 模块综合文档

本模块提供了一套功能强大的 Python 工具集，旨在简化和增强常见的开发任务，涵盖了从彩色日志、事件驱动编程到高级多进程管理的方方面面。本文档旨在为开发者提供一份详尽的、全面的使用指南，帮助您充分利用本模块提供的所有功能。

## 目录
- [一、基础工具类](#一基础工具类)
  - [1.1. Colors (颜色常量)](#11-colors-颜色常量)
  - [1.2. Singleton (单例模式装饰器)](#12-singleton-单例模式装饰器)
  - [1.3. ClassInstanceManager (类实例管理器)](#13-classinstancemanager-类实例管理器)
  - [1.4. OptimalSorter (优化排序器)](#14-optimalsorter-优化排序器)
- [二、核心功能模块](#二核心功能模块)
  - [2.1. <PERSON><PERSON> (高级日志记录器)](#21-logger-高级日志记录器)
  - [2.2. EventEmitter (事件发射器)](#22-eventemitter-事件发射器)
- [三、独立工具函数](#三独立工具函数)
  - [3.1. 列表与容器操作](#31-列表与容器操作)
    - [`find_same_elements_groups`](#find_same_elements_groups)
    - [`flatten_preserve_types`](#flatten_preserve_types)
    - [`flatten_preserve_types_no_dict`](#flatten_preserve_types_no_dict)
    - [`split_list_evenly`](#split_list_evenly)
  - [3.2. 文件系统操作](#32-文件系统操作)
    - [`clean_directory`](#clean_directory)
    - [`delete_files`](#delete_files)
    - [`get_file_info`](#get_file_info)
    - [`list_directory_files`](#list_directory_files)
    - [`find_latest`](#find_latest)
    - [`find_latest_created_folder`](#find_latest_created_folder)
- [四、增强进程模块 (`enhanced_process`)](#四增强进程模块-enhanced_process)
  - [4.1. ProcessLogger (子进程日志记录器)](#41-processlogger-子进程日志记录器)
  - [4.2. EnhancedProcess (增强型进程)](#42-enhancedprocess-增强型进程)
- [五、高级多进程管理框架 (`manager_process3`)](#五高级多进程管理框架-manager_process3)
  - [5.1. 框架概述](#51-框架概述)
  - [5.2. SharedDataManager (共享数据管理器)](#52-shareddatamanager-共享数据管理器)
  - [5.3. ProcessEventManager (进程事件管理器)](#53-processeventmanager-进程事件管理器)
  - [5.4. ManagedMultiProcess (多进程任务管理器)](#54-managedmultiprocess-多进程任务管理器)
  - [5.5. 综合使用示例](#55-综合使用示例)

---

<a name="一基础工具类"></a>
## 一、基础工具类

本章节介绍 `utils` 模块中提供的一些基础但非常实用的工具类。

<a name="11-colors-颜色常量"></a>
### 1.1. Colors (颜色常量)

`Colors` 类是一个静态常量集合，提供了大量预定义的16进制颜色字符串，方便在终端输出、日志、UI设计等场景中统一和快捷地使用颜色。

#### 功能与作用
- **标准化颜色定义**: 提供一个集中的地方管理颜色值，避免在代码中散落硬编码的颜色字符串。
- **易于使用**: 通过直观的名称（如 `Colors.RED`, `Colors.SUCCESS`）来引用颜色。

#### 属性
该类不包含常规方法，其所有公开接口主要为类级别的字符串属性，每个属性代表一个颜色。颜色常量按以下类别组织：

**基础颜色:**
- `Colors.BLACK`: `"#000000"` (黑色)
- `Colors.WHITE`: `"#FFFFFF"` (白色)
- `Colors.RED`: `"#FF0000"` (红色)
- `Colors.GREEN`: `"#00FF00"` (绿色)
- `Colors.BLUE`: `"#0000FF"` (蓝色)
- `Colors.YELLOW`: `"#FFFF00"` (黄色)
- `Colors.PURPLE`: `"#800080"` (紫色)
- `Colors.CYAN`: `"#00FFFF"` (青色)
- `Colors.MAGENTA`: `"#FF00FF"` (品红)
- `Colors.GRAY`: `"#808080"` (灰色)

**暖色系:**
- `Colors.ORANGE`: `"#FFA500"` (橙色)
- `Colors.CORAL`: `"#FF7F50"` (珊瑚色)
- `Colors.TOMATO`: `"#FF6347"` (番茄红)
- `Colors.GOLD`: `"#FFD700"` (金色)
- `Colors.PINK`: `"#FFC0CB"` (粉色)
- `Colors.SALMON`: `"#FA8072"` (鲑鱼色)
- `Colors.PEACH`: `"#FFE5B4"` (桃色)

**冷色系:**
- `Colors.TEAL`: `"#008080"` (青蓝色)
- `Colors.TURQUOISE`: `"#40E0D0"` (绿宝石色)
- `Colors.SKYBLUE`: `"#87CEEB"` (天蓝色)
- `Colors.INDIGO`: `"#4B0082"` (靛蓝色)
- `Colors.NAVY`: `"#000080"` (海军蓝)
- `Colors.AZURE`: `"#F0FFFF"` (天蓝色)

**自然色系:**
- `Colors.BROWN`: `"#A52A2A"` (棕色)
- `Colors.BEIGE`: `"#F5F5DC"` (米色)
- `Colors.IVORY`: `"#FFFFF0"` (象牙色)
- `Colors.KHAKI`: `"#F0E68C"` (卡其色)
- `Colors.OLIVE`: `"#808000"` (橄榄色)
- `Colors.MAROON`: `"#800000"` (栗色)
- `Colors.FOREST_GREEN`: `"#228B22"` (森林绿)

**柔和色系:**
- `Colors.LAVENDER`: `"#E6E6FA"` (薰衣草色)
- `Colors.MINT`: `"#98FF98"` (薄荷色)
- `Colors.PEACH_PUFF`: `"#FFDAB9"` (粉扑桃色)
- `Colors.THISTLE`: `"#D8BFD8"` (蓟色)
- `Colors.POWDER_BLUE`: `"#B0E0E6"` (粉蓝色)
- `Colors.MISTY_ROSE`: `"#FFE4E1"` (浅玫瑰色)

**深色系:**
- `Colors.DARK_RED`: `"#8B0000"` (深红色)
- `Colors.DARK_GREEN`: `"#006400"` (深绿色)
- `Colors.DARK_BLUE`: `"#00008B"` (深蓝色)
- `Colors.DARK_CYAN`: `"#008B8B"` (深青色)
- `Colors.DARK_MAGENTA`: `"#8B008B"` (深品红)
- `Colors.DARK_ORANGE`: `"#FF8C00"` (深橙色)
- `Colors.DARK_VIOLET`: `"#9400D3"` (深紫罗兰)

**亮色系:**
- `Colors.LIGHT_PINK`: `"#FFB6C1"` (亮粉色)
- `Colors.LIGHT_BLUE`: `"#ADD8E6"` (亮蓝色)
- `Colors.LIGHT_GREEN`: `"#90EE90"` (亮绿色)
- `Colors.LIGHT_YELLOW`: `"#FFFFE0"` (亮黄色)
- `Colors.LIGHT_CYAN`: `"#E0FFFF"` (亮青色)
- `Colors.LIGHT_GRAY`: `"#D3D3D3"` (亮灰色)

**特殊用途:**
- `Colors.SUCCESS`: `"#28A745"` (成功提示色)
- `Colors.ERROR`: `"#DC3545"` (错误提示色)
- `Colors.WARNING`: `"#FFC107"` (警告提示色)
- `Colors.INFO`: `"#17A2B8"` (信息提示色)
- `Colors.PRIMARY`: `"#007BFF"` (主要提示色)
- `Colors.SECONDARY`: `"#6C757D"` (次要提示色)

**调试颜色系列:**
- `Colors.DEBUG`: `"#7B68EE"` (调试基础色，中等紫罗兰蓝)
- `Colors.DEBUG_HIGHLIGHT`: `"#FF69B4"` (调试高亮色，热粉红)
- `Colors.DEBUG_BACKGROUND`: `"#F0F8FF"` (调试背景色，爱丽丝蓝)
- `Colors.DEBUG_TRACE`: `"#20B2AA"` (调试跟踪色，浅海洋绿)
- `Colors.DEBUG_VARIABLE`: `"#FF7F50"` (调试变量色，珊瑚色)
- `Colors.DEBUG_FUNCTION`: `"#6A5ACD"` (调试函数色，板岩蓝)
- `Colors.DEBUG_TIMESTAMP`: `"#708090"` (调试时间戳色，板岩灰)
- `Colors.DEBUG_ASSERTION`: `"#FF4500"` (调试断言色，橙红色)
- `Colors.DEBUG_MEMORY`: `"#2E8B57"` (调试内存信息色，海洋绿)
- `Colors.DEBUG_NETWORK`: `"#4682B4"` (调试网络信息色，钢蓝)
- `Colors.DEBUG_IO`: `"#9932CC"` (调试输入输出色，暗兰花紫)
- `Colors.DEBUG_PERFORMANCE`: `"#DAA520"` (调试性能信息色，金菊黄)
- `Colors.VERBOSE`: `"#708090"` (详细日志色，板岩灰)

#### 公共类方法

##### `get_all_colors()`
- **作用**: 获取类中定义的所有颜色常量。
- **参数**: 无。
- **返回值**: `Dict[str, str]` - 一个字典，键是颜色名称（如 `"RED"`），值是对应的16进制颜色字符串（如 `"#FF0000"`）。

##### `print_all_colors()`
- **作用**: 在控制台打印出所有可用的颜色名称及其16进制值。
- **参数**: 无。
- **返回值**: 无。

#### 使用示例

```python
from global_tools.utils import Colors, Logger

# 假设有一个 Logger 实例
logger = Logger()

# 1. 在日志中使用预定义的颜色
logger.info("这是一条成功信息。", color=Colors.SUCCESS)
logger.error("发生了一个严重错误！", color=Colors.ERROR)
logger.warning("这是一个警告。", color=Colors.WARNING)
logger.debug("调试信息用特殊的调试颜色。", color=Colors.DEBUG_FUNCTION)

# 2. 获取并遍历所有颜色
all_colors = Colors.get_all_colors()
print(f"共定义了 {len(all_colors)} 种颜色。")
for name, hex_code in all_colors.items():
    if "BLUE" in name:
        print(f"找到一种蓝色: {name} -> {hex_code}")

# 3. 直接在控制台打印所有颜色以供查阅
Colors.print_all_colors()

# 预期输出 (部分):
# 可用的颜色:
# BLACK           = #000000
# WHITE           = #FFFFFF
# RED             = #FF0000
# ...
```

<a name="12-singleton-单例模式装饰器"></a>
### 1.2. Singleton (单例模式装饰器)

`@Singleton` 是一个类装饰器，用于将任何普通类转换为线程安全的单例模式。被装饰的类在整个应用程序生命周期中只会存在一个实例。

#### 功能与作用
- **确保唯一实例**: 保证一个类只有一个实例，并提供一个全局访问点。
- **线程安全**: 在多线程环境下创建实例时，通过锁机制保证实例的唯一性。
- **实例管理**: 提供了多个类方法来管理和查询由该装饰器创建的所有单例实例。

#### 使用方法
直接在类定义上方使用 `@Singleton` 即可。

#### 公共类方法 (通过`Singleton`装饰器访问)

##### `clear_instance(target_cls)`
- **作用**: 清除指定类的单例实例，允许下次调用时创建一个新实例。
- **参数**:
    - `target_cls` (`Type`): 需要清除实例的类本身。
- **返回值**: `bool` - 如果实例存在并被成功删除，返回 `True`；否则返回 `False`。

##### `clear_all_instances()`
- **作用**: 清除由 `Singleton` 装饰器管理的所有单例实例。
- **参数**: 无。
- **返回值**: 无。

##### `get_instance(target_cls)`
- **作用**: 获取指定类的当前单例实例。
- **参数**:
    - `target_cls` (`Type`): 目标类。
- **返回值**: `Optional[Any]` - 如果实例已创建，则返回该实例；否则返回 `None`。

##### `has_instance(target_cls)`
- **作用**: 检查指定类是否已经创建了单例实例。
- **参数**:
    - `target_cls` (`Type`): 目标类。
- **返回值**: `bool` - 如果实例存在，返回 `True`；否则返回 `False`。

##### `get_all_instances()`
- **作用**: 获取所有当前存在的单例实例。
- **参数**: 无。
- **返回值**: `Dict[Type, Any]` - 一个字典，键是类本身，值是对应的单例实例。

##### `get_instance_count()`
- **作用**: 获取当前由 `Singleton` 管理的单例实例总数。
- **参数**: 无。
- **返回值**: `int` - 实例的数量。

#### 使用示例

```python
import threading
from global_tools.utils import Singleton

@Singleton
class DatabaseConnection:
    def __init__(self, dsn: str):
        # 模拟耗时的初始化操作
        print(f"正在初始化数据库连接，DSN: {dsn}, ID: {id(self)}")
        self.dsn = dsn
        self.connection_id = id(self)

    def query(self, sql: str):
        print(f"使用连接 {self.connection_id} 执行查询: {sql}")

# 1. 基本的单例实例化
# 第一次调用会创建实例
db_conn1 = DatabaseConnection("********************************/db")
# 第二次调用会返回现有实例，不会执行 __init__
db_conn2 = DatabaseConnection("another_dsn_is_ignored")

print(f"实例1的ID: {id(db_conn1)}")
print(f"实例2的ID: {id(db_conn2)}")
assert db_conn1 is db_conn2  # 它们是同一个对象

db_conn1.query("SELECT * FROM users")
db_conn2.query("SELECT * FROM products")

# 2. 管理单例实例
# 检查实例是否存在
if Singleton.has_instance(DatabaseConnection):
    print("DatabaseConnection 的单例实例存在。")

# 获取实例数量
print(f"当前单例实例数量: {Singleton.get_instance_count()}")

# 清除单例
Singleton.clear_instance(DatabaseConnection)
print(f"清除后，DatabaseConnection 的单例实例是否存在: {Singleton.has_instance(DatabaseConnection)}")

# 再次创建，会生成一个全新的实例
new_conn = DatabaseConnection("new_dsn_works_now")
print(f"新连接的ID: {id(new_conn)}")
assert new_conn is not db_conn1 # 这是一个全新的实例

```

<a name="13-classinstancemanager-类实例管理器"></a>
### 1.3. ClassInstanceManager (类实例管理器)

`ClassInstanceManager` 是一个静态工具类，充当一个全局的、线程安全的类实例注册表。它允许您通过一个唯一的键（key）来创建、存储、获取和管理任何类的实例。

#### 功能与作用
- **全局实例中心**: 提供一个统一的地方来管理应用程序中可能需要共享或方便访问的实例。
- **键值化管理**: 使用字符串键来标识和检索实例，比直接导入模块和访问全局变量更灵活。
- **解耦**: 消费方代码只需要知道实例的键，而不需要知道实例是如何创建的，有助于降低模块间的耦合。
- **线程安全**: 所有操作都是线程安全的。
- **动态控制**: 支持在运行时动态地创建、删除和替换实例。

#### 公共静态方法

##### `create_instance(class_obj, key, *args, **kwargs)`
- **作用**: 创建一个类的实例，并使用指定的键进行注册。如果键已存在，将引发 `ValueError`。
- **参数**:
    - `class_obj` (`Type`): 需要实例化的类。
    - `key` (`str`): 用于标识该实例的唯一字符串键。
    - `*args`: 传递给 `class_obj` 构造函数的位置参数。
    - `**kwargs`: 传递给 `class_obj` 构造函数的关键字参数。
- **返回值**: `Any` - 成功创建的实例。
- **异常**:
    - `ValueError`: 如果指定的 `key` 已经存在。

##### `get_instance(key)`
- **作用**: 根据键获取一个已注册的实例。
- **参数**:
    - `key` (`str`): 要检索的实例的键。
- **返回值**: `Optional[Any]` - 如果找到，返回对应的实例；否则返回 `None`。

##### `has_instance(key)`
- **作用**: 检查指定的键是否有对应的实例存在。
- **参数**:
    - `key` (`str`): 要检查的键。
- **返回值**: `bool` - 如果实例存在，返回 `True`；否则返回 `False`。

##### `remove_instance(key)`
- **作用**: 根据键删除一个已注册的实例。
- **参数**:
    - `key` (`str`): 要删除的实例的键。
- **返回值**: `bool` - 如果实例存在并被成功删除，返回 `True`；否则返回 `False`。

##### `clear_all_instances()`
- **作用**: 清除所有已注册的实例。
- **参数**: 无。
- **返回值**: 无。

##### `get_instance_count()`
- **作用**: 获取当前已注册的实例总数。
- **参数**: 无。
- **返回值**: `int` - 实例的数量。

##### `get_all_instance_keys()`
- **作用**: 获取所有已注册实例的键。
- **参数**: 无。
- **返回值**: `List[str]` - 包含所有键的列表。

##### `get_all_instances()`
- **作用**: 获取所有已注册的实例。
- **参数**: 无。
- **返回值**: `Dict[str, Any]` - 一个字典，键是注册时使用的 `key`，值是对应的实例。

##### `set_enable_log(enable: bool)`
- **作用**: 动态设置 `ClassInstanceManager` 内部操作的日志开关。
- **参数**:
    - `enable` (`bool`): `True` 表示开启日志, `False` 表示关闭。
- **返回值**: 无。

#### 使用示例

```python
from global_tools.utils import ClassInstanceManager, Logger

# --- 定义一些要被管理的类 ---
class ApiClient:
    def __init__(self, base_url: str, api_key: str):
        self.base_url = base_url
        self.api_key = api_key
        print(f"API客户端已创建，目标: {self.base_url}")

    def get(self, endpoint: str):
        return f"从 {self.base_url}{endpoint} 获取数据"

# --- 使用 ClassInstanceManager ---

# 0. (可选) 开启日志，方便观察内部操作
ClassInstanceManager.set_enable_log(True)

# 1. 创建并注册不同类的实例
print("--- 注册实例 ---")
try:
    # 注册一个 Logger 实例，方便全局使用
    ClassInstanceManager.create_instance(Logger, "app_logger", log_dir="app_logs", enable_file_logging=True)
    
    # 注册一个 API 客户端实例
    ClassInstanceManager.create_instance(ApiClient, "main_api", base_url="https://api.example.com", api_key="abc-123")
    
    # 尝试用同一个键注册，会失败
    ClassInstanceManager.create_instance(ApiClient, "main_api", base_url="https://api.another.com", api_key="def-456")

except ValueError as e:
    print(f"注册失败: {e}")

# 2. 在应用的不同地方获取和使用实例
print("\n--- 使用实例 ---")
# 获取 Logger 实例
global_logger = ClassInstanceManager.get_instance("app_logger")
if global_logger:
    global_logger.info("应用已启动。这是一个通过 ClassInstanceManager 获取的日志器。")

# 获取 API 客户端实例
api_client = ClassInstanceManager.get_instance("main_api")
if api_client:
    response = api_client.get("/users")
    print(f"API 响应: {response}")

# 3. 检查和管理实例
print("\n--- 管理实例 ---")
# 检查实例是否存在
print(f"是否存在 'main_api' 实例? {ClassInstanceManager.has_instance('main_api')}")
print(f"是否存在 'non_existent_api' 实例? {ClassInstanceManager.has_instance('non_existent_api')}")

# 查看所有实例信息
print(f"当前实例数量: {ClassInstanceManager.get_instance_count()}")
print(f"所有实例的键: {ClassInstanceManager.get_all_instance_keys()}")

# 4. 删除实例
print("\n--- 删除实例 ---")
was_removed = ClassInstanceManager.remove_instance("main_api")
print(f"'main_api' 实例是否被移除? {was_removed}")
print(f"删除后，'main_api' 实例是否存在? {ClassInstanceManager.has_instance('main_api')}")

# 5. 清理所有实例
print("\n--- 清理所有实例 ---")
ClassInstanceManager.clear_all_instances()
print(f"清理后，实例数量: {ClassInstanceManager.get_instance_count()}")
```

<a name="14-optimalsorter-优化排序器"></a>
### 1.4. OptimalSorter (优化排序器)

`OptimalSorter` 是一个包含静态排序方法的工具类。它目前作为标准排序功能的一个封装，未来可能扩展为根据数据特性自动选择最优排序算法。

#### 功能与作用
- **提供统一排序接口**: 封装排序逻辑，方便调用。

#### 公共静态方法

##### `sort(iterable, key=None)`
- **作用**: 对可迭代对象进行排序。内部实现目前直接调用 Python 的 `sorted()` 函数。
- **参数**:
    - `iterable` (`Iterable`): 任何可迭代的对象（如列表、元组）。
    - `key` (`Optional[Callable]`): 一个单参数的函数，用于从可迭代对象的每个元素中提取比较键。默认为 `None`，即直接比较元素。
- **返回值**: `list` - 一个新的、已排序的列表。

#### 使用示例

```python
from global_tools.utils import OptimalSorter

# 1. 对数字列表进行排序
numbers = [3, 1, 4, 1, 5, 9, 2, 6]
sorted_numbers = OptimalSorter.sort(numbers)
print(f"排序后的数字: {sorted_numbers}")

# 2. 对字典列表按特定键进行排序
users = [
    {'name': 'Alice', 'age': 30},
    {'name': 'Bob', 'age': 25},
    {'name': 'Charlie', 'age': 35}
]

# 按年龄排序
sorted_by_age = OptimalSorter.sort(users, key=lambda user: user['age'])
print("按年龄排序的用户:")
for user in sorted_by_age:
    print(user)

# 按姓名排序
sorted_by_name = OptimalSorter.sort(users, key=lambda user: user['name'])
print("\n按姓名排序的用户:")
for user in sorted_by_name:
    print(user)

```

---

<a name="二核心功能模块"></a>
## 二、核心功能模块

本章深入探讨 `utils` 模块提供的两大核心功能：`Logger` 和 `EventEmitter`。

<a name="21-logger-高级日志记录器"></a>
### 2.1. Logger (高级日志记录器)

`Logger` 是一个功能极其丰富的日志记录类，旨在提供远超标准 `logging` 模块的开箱即用体验。它集成了彩色控制台输出、文件持久化、日志轮转、IDE链接、灵活的配置层级等多种高级特性。

#### 功能与作用
- **多级日志**: 支持 `DEBUG`, `INFO`, `WARNING`, `ERROR`, `CRITICAL` 等标准日志级别。
- **彩色输出**: 自动为不同级别的日志在控制台应用不同的颜色，提高可读性。支持自定义颜色。
- **文件日志**: 可选地将日志写入文件，并支持按文件大小和文件数量自动进行日志轮转。
- **上下文信息**: 自动记录时间戳、调用日志的文件名和行号。
- **IDE集成**: 生成的日志包含可点击链接，支持在 PyCharm, VSCode 等主流IDE中直接跳转到代码位置。
- **灵活配置**: 支持全局级别 (`Logger.basicConfig`, `Logger.setLevel`) 和实例级别的配置，允许对不同模块或组件的日志进行精细化控制。
- **自定义格式**: 支持通过设置自定义格式化函数来完全控制日志的输出格式。
- **性能优化**: 使用日志缓存机制，批量写入文件，减少I/O操作。

#### 构造函数 `__init__`

##### `Logger(cache_size=None, enable_file_logging=None, log_dir=None, max_file_size_mb=None, max_file_count=None, truncate_length=None, ide_link_format=None)`

- **作用**: 创建一个 `Logger` 实例。构造函数中的所有参数都是可选的。如果某个参数未提供（为 `None`），则该配置项会继承自通过 `Logger.basicConfig()` 设置的全局配置。如果全局配置也未设置，则使用内部默认值。
- **参数**:
    - `cache_size` (`Optional[int]`): 缓存中存储的日志条数。当缓存满时，日志会自动写入文件。默认值来自全局配置或为 `100`。
    - `enable_file_logging` (`Optional[bool]`): 是否启用文件日志功能。默认值来自全局配置或为 `False`。
    - `log_dir` (`Optional[str]`): 日志文件存放的目录。默认值来自全局配置或为 `"logs"`。
    - `max_file_size_mb` (`Optional[float]`): 单个日志文件的最大体积（单位MB）。超过此大小会触发日志轮转。默认值来自全局配置或为 `10.0`。
    - `max_file_count` (`Optional[int]`): 最多保留的日志文件数量。超过此数量，最旧的日志文件将被删除。默认值来自全局配置或为 `10`。
    - `truncate_length` (`Optional[int]`): 在控制台输出时，单条日志消息的最大显示长度。`-1` 表示不截断。默认值来自全局配置或为 `1024`。
    - `ide_link_format` (`Optional[str]`): 为日志中的文件路径生成可点击链接的格式。支持 `"vscode"`, `"pycharm"`, 等。默认值来自全局配置或为 `"vscode"`。

#### 日志记录方法

每个日志记录方法都接受相似的参数。

##### `debug(message, color=Colors.DEBUG_HIGHLIGHT, truncate_length=-1)`
##### `info(message, color=None, truncate_length=None)`
##### `warning(message, color=Colors.YELLOW, truncate_length=None)`
##### `error(message, color=Colors.ERROR, truncate_length=None)`
##### `critical(message, color=None, truncate_length=None)`

- **作用**: 记录一条相应级别的日志。
- **参数**:
    - `message` (`str`): 要记录的日志消息内容。
    - `color` (`Optional[str]`): 自定义这条消息在控制台的颜色。可以是 `colorama` 的颜色常量（如 `Fore.BLUE`）或16进制颜色字符串（如 `"#FF5733"`）。如果为 `None`，则使用级别的默认颜色。
    - `truncate_length` (`Optional[int]`): 单独为这条消息覆盖截断长度。如果为 `None`，则使用实例的配置。`-1` 表示不截断。

#### 实例配置方法

##### `flush()`
- **作用**: 将当前缓存中的所有日志立即写入文件，而无需等待缓存满。
- **参数**: 无。
- **返回值**: 无。

##### `set_instance_level(level: LogLevel)`
- **作用**: 为当前 `Logger` 实例设置独立的日志级别。此设置将覆盖全局日志级别。
- **参数**:
    - `level` (`LogLevel`): `LogLevel` 枚举中的一个值（如 `LogLevel.INFO`, `LogLevel.OFF`）。
- **返回值**: 无。

##### `set_instance_formatter(formatter)`
- **作用**: 为当前 `Logger` 实例设置一个自定义的日志格式化函数。此设置将覆盖全局格式化器。
- **参数**:
    - `formatter` (`Optional[Callable]`): 一个函数，签名应为 `def my_formatter(record, color, truncate_length)`。它应返回一个元组 `(console_str, file_str)`。传入 `None` 可恢复使用全局格式化器。
- **返回值**: 无。

#### 全局配置静态方法

##### `Logger.setLevel(level: LogLevel)`
- **作用**: 设置全局日志级别。所有未设置实例级别的 `Logger` 都会遵循此设置。
- **参数**:
    - `level` (`LogLevel`): `LogLevel` 枚举中的一个值。
- **返回值**: 无。

##### `Logger.setFormatter(formatter)`
- **作用**: 设置全局的自定义日志格式化函数。
- **参数**:
    - `formatter` (`Optional[Callable]`): 格式化函数，或传入 `None` 恢复默认格式。
- **返回值**: 无。

##### `Logger.basicConfig(**kwargs)`
- **作用**: 一次性配置所有全局日志参数，类似于标准库 `logging.basicConfig`。这会影响之后创建的所有 `Logger` 实例。
- **参数**: `**kwargs` - 接受与 `Logger` 构造函数相同的关键字参数，外加一个 `level` 参数用于设置全局级别。
    - `level` (`LogLevel`): 全局日志级别。
    - `filename` (`str`): 如果提供此参数，`enable_file_logging` 会被自动设为 `True`，并且日志会写入指定的 `log_dir/filename`。
    - ... 其他参数同 `__init__`。
- **返回值**: 无。

#### 使用示例

##### 示例1: 基本用法和彩色输出
```python
from global_tools.utils import Logger, Colors, LogLevel

# 创建一个默认的 Logger
logger = Logger()

logger.debug("这是一条调试信息。")
logger.info("这是一条普通信息。")
logger.warning("这是一条警告信息。")
logger.error("这是一条错误信息。")

# 使用自定义颜色
logger.info("自定义成功消息", color=Colors.SUCCESS)
logger.info("使用十六进制颜色", color="#8A2BE2") # 蓝紫色
```

##### 示例2: 文件日志和日志轮转
```python
from global_tools.utils import Logger

# 配置一个将日志写入文件的 Logger
file_logger = Logger(
    enable_file_logging=True,
    log_dir="app_critical_logs",
    max_file_size_mb=0.01,  # 设置一个很小的值 (10KB) 来快速触发轮转
    max_file_count=3,       # 最多保留3个文件
    cache_size=5            # 每5条消息就写入文件
)

for i in range(20):
    file_logger.critical(f"关键日志条目 #{i+1}")

# 执行后，检查 'app_critical_logs' 目录，会看到类似 app.log, app.1.log 等文件
```

##### 示例3: 级别控制 (全局 vs 实例)
```python
from global_tools.utils import Logger, LogLevel

# 设置全局级别为 WARNING，只有 WARNING 及以上级别的日志会显示
Logger.setLevel(LogLevel.WARNING)

print("--- 使用全局级别 (WARNING) ---")
logger1 = Logger()
logger1.info("这条 info 消息不会显示。")
logger1.warning("这条 warning 消息会显示。")

print("\n--- 使用实例级别 (DEBUG) ---")
# 为 logger2 单独设置更低的级别
logger2 = Logger()
logger2.set_instance_level(LogLevel.DEBUG)
logger2.info("logger2 的 info 消息会显示，因为它受实例级别控制。")
logger2.debug("logger2 的 debug 消息也会显示。")

print("\n--- 全局关闭日志 ---")
Logger.setLevel(LogLevel.OFF)
logger1.critical("这条 critical 消息现在不会显示了。")
logger2.critical("但 logger2 仍然会显示，因为它的实例级别是 DEBUG。")
```

##### 示例4: 使用 `basicConfig` 和自定义格式化
```python
from global_tools.utils import Logger, LogLevel

# 定义一个自定义的日志格式化函数
def simple_formatter(record, color, truncate_length):
    time_str = record["time"].strftime("%H:%M:%S")
    # 只返回控制台格式，文件格式将使用默认
    console_output = f"[{record['levelname']}] @ {time_str}: {record['message']}"
    return console_output, None # 文件格式传 None 会使用默认格式

# 全局配置
Logger.basicConfig(
    level=LogLevel.INFO,
    enable_file_logging=True,
    log_dir="formatted_logs",
    formatter=simple_formatter  # 应用全局格式化器
)

logger = Logger()
logger.info("这条日志将使用自定义格式。")
logger.error("这条错误日志也使用自定义格式。")
```

<a name="22-eventemitter-事件发射器"></a>
### 2.2. EventEmitter (事件发射器)

`EventEmitter` 是一个功能强大的事件发射器类，它实现了发布-订阅（Pub/Sub）设计模式。该类允许应用程序的不同部分在不直接相互引用的情况下进行通信，从而实现高度解耦和可扩展的架构。它支持同步/异步事件处理、事件优先级、一次性监听器以及高级的速率控制机制（节流与防抖）。

`EventEmitter` 被实现为单例模式，意味着在整个应用程序中，`EventEmitter()` 或 `EventEmitter.get_instance()` 将始终返回同一个实例，方便在任何地方进行事件的注册和触发。

#### 功能与作用
- **事件驱动架构**: 作为应用程序事件驱动模型的核心。
- **解耦**: 允许模块间通过事件进行通信，而无需直接依赖。
- **异步支持**: 与 `asyncio` 深度集成，能够无缝处理和触发异步事件处理函数。
- **灵活性**: 支持事件优先级、一次性事件、通配符事件订阅 (`*`)。
- **速率控制**: 内置节流（Throttle）和防抖（Debounce）功能，轻松处理高频事件（如UI事件、数据流）。

#### 构造函数 `__init__`
`EventEmitter` 是一个单例，直接调用 `EventEmitter()` 即可获取全局唯一实例。

#### 主要方法

##### `on(event, callback, priority=EventPriority.NORMAL, once=False, context=None, throttle=None, debounce=None)`
- **作用**: 注册一个事件监听器（回调函数）。这是最核心的订阅方法。
- **参数**:
    - `event` (`str`): 事件名称。可以使用 `*`作为通配符来监听所有事件。
    - `callback` (`Callable`): 当事件被触发时要执行的回调函数。可以是同步函数或异步 (`async def`) 函数。
    - `priority` (`EventPriority`, optional): 事件处理的优先级，类型为 `EventPriority` 枚举。数值越低，优先级越高。默认为 `EventPriority.NORMAL`。
    - `once` (`bool`, optional): 如果为 `True`，该回调函数在执行一次后会自动移除。默认为 `False`。
    - `context` (`Any`, optional): 附加到事件处理器上的任意上下文数据。默认为 `None`。
    - `throttle` (`Optional[ThrottleConfig]`, optional): 节流配置。用于限制回调函数在一定时间间隔内最多执行一次。`ThrottleConfig` 需要从 `global_tools.utils.event` 导入。
    - `debounce` (`Optional[DebounceConfig]`, optional): 防抖配置。用于在事件频繁触发时，只在最后一次触发后的指定时间内执行回调。`DebounceConfig` 需要从 `global_tools.utils.event` 导入。
- **返回值**: `Callable` - 返回传入的 `callback` 函数，方便链式调用或装饰器用法。

##### `once(event, callback, priority=EventPriority.NORMAL, context=None)`
- **作用**: 注册一个一次性的事件监听器。等同于 `on(event, callback, once=True)`。
- **参数**: 同 `on` 方法，但不包括 `once`, `throttle`, `debounce`。
- **返回值**: `Callable` - 返回传入的 `callback` 函数。

##### `off(event, callback=None)`
- **作用**: 移除事件监听器。
- **参数**:
    - `event` (`str`): 事件名称。
    - `callback` (`Optional[Callable]`, optional): 如果提供了具体的回调函数，则只移除该函数。如果为 `None`，则移除该事件的所有监听器。
- **返回值**: `bool` - 如果成功移除了至少一个监听器，则返回 `True`，否则返回 `False`。

##### `emit(event, *args, **kwargs)`
- **作用**: 同步地触发一个事件。它会按照优先级顺序依次执行所有匹配的监听器。
- **参数**:
    - `event` (`str`): 要触发的事件名称。
    - `*args`, `**kwargs`: 要传递给回调函数的位置参数和关键字参数。
- **返回值**: `list` - 包含所有同步回调函数返回值的列表。异步回调的返回值不会在此处出现。

##### `emit_async(event, *args, **kwargs)`
- **作用**: 异步地触发一个事件。此方法立即返回一个 `Future` 对象，不会阻塞当前线程。
- **参数**: 同 `emit` 方法。
- **返回值**: `asyncio.Future` - 一个 `Future` 对象，当所有异步回调都执行完毕后，该 `Future` 会被解析，其结果是所有回调返回值的列表。

##### `wait_for(event, timeout=None)`
- **作用**: 等待一个事件被触发，并返回其触发时的数据。
- **参数**:
    - `event` (`str`): 要等待的事件名称。
    - `timeout` (`Optional[float]`, optional): 等待的超时时间（秒）。如果超时，将引发 `asyncio.TimeoutError`。
- **返回值**: `asyncio.Future` - 一个 `Future` 对象，当事件被触发时，它会解析为传递给 `emit` 的 `(*args, **kwargs)`。

#### 使用示例

##### 示例1: 基本用法与优先级
```python
from global_tools.utils import EventEmitter, EventPriority

emitter = EventEmitter.get_instance()

def low_priority_handler(data):
    print(f"低优先级处理器收到: {data}")

def high_priority_handler(data):
    print(f"高优先级处理器收到: {data}")

# 注册监听器，指定不同优先级
emitter.on("data_received", low_priority_handler, priority=EventPriority.LOW)
emitter.on("data_received", high_priority_handler, priority=EventPriority.HIGH)

print("--- 触发 'data_received' 事件 ---")
emitter.emit("data_received", "核心数据")

# 预期输出 (高优先级先执行):
# 高优先级处理器收到: 核心数据
# 低优先级处理器收到: 核心数据
```

##### 示例2: 异步事件处理
```python
import asyncio
from global_tools.utils import EventEmitter

emitter = EventEmitter()

async def async_task_handler(duration, task_name):
    print(f"异步任务 '{task_name}' 开始，将运行 {duration} 秒...")
    await asyncio.sleep(duration)
    print(f"异步任务 '{task_name}' 完成。")
    return task_name

emitter.on("run_tasks", async_task_handler)

async def main():
    print("--- 异步触发 'run_tasks' 事件 ---")
    # 异步触发事件，不会阻塞
    future = emitter.emit_async("run_tasks", 1, task_name="任务A")
    emitter.emit("run_tasks", 0.5, task_name="任务B") # 也可以同步触发异步函数

    print("事件已触发，主程序可以继续做其他事...")
    
    # 等待异步事件完成
    results = await future
    print(f"emit_async 的结果: {results}")

# 在 asyncio 事件循环中运行
asyncio.run(main())

# 预期输出:
# --- 异步触发 'run_tasks' 事件 ---
# 异步任务 '任务A' 开始，将运行 1 秒...
# 异步任务 '任务B' 开始，将运行 0.5 秒...
# 事件已触发，主程序可以继续做其他事...
# 异步任务 '任务B' 完成。
# 异步任务 '任务A' 完成。
# emit_async 的结果: ['任务A']
```

##### 示例3: 使用 `wait_for` 等待事件
```python
import asyncio
import time
from global_tools.utils import EventEmitter

emitter = EventEmitter()

async def waiter():
    print("正在等待 'user_login' 事件...")
    try:
        # 等待事件，设置2秒超时
        args, kwargs = await asyncio.wait_for(emitter.wait_for("user_login"), timeout=2.0)
        print(f"事件已捕获! 用户: {kwargs.get('user')}, 时间: {args[0]}")
    except asyncio.TimeoutError:
        print("等待超时！")

def trigger():
    # 延迟1秒后触发事件
    time.sleep(1)
    print("触发 'user_login' 事件...")
    emitter.emit("user_login", time.time(), user="Alice")

async def main():
    # 并发运行等待者和触发者
    await asyncio.gather(waiter(), asyncio.to_thread(trigger))

asyncio.run(main())

# 预期输出:
# 正在等待 'user_login' 事件...
# 触发 'user_login' 事件...
# 事件已捕获! 用户: Alice, 时间: <一个时间戳>
```

##### 示例4: 防抖 (Debounce) 和节流 (Throttle)
```python
import asyncio
from global_tools.utils import EventEmitter, DebounceConfig, ThrottleConfig

emitter = EventEmitter()

# 防抖：在连续输入停止后才进行搜索
def search_handler(query):
    print(f"[{time.time():.2f}] 正在搜索: {query}")

# 配置防抖，等待0.5秒
debounce_config = DebounceConfig(enabled=True, wait=0.5)
emitter.on("search_input", search_handler, debounce=debounce_config)

# 节流：每秒最多处理一次窗口大小调整事件
def resize_handler(width, height):
    print(f"[{time.time():.2f}] 处理窗口大小: {width}x{height}")

# 配置节流，间隔1秒
throttle_config = ThrottleConfig(enabled=True, interval=1.0)
emitter.on("window_resize", resize_handler, throttle=throttle_config)

async def simulate_events():
    print("--- 模拟防抖 (快速输入) ---")
    emitter.emit("search_input", "py")
    await asyncio.sleep(0.1)
    emitter.emit("search_input", "pyth")
    await asyncio.sleep(0.1)
    emitter.emit("search_input", "python")
    # 只有最后一次会执行

    await asyncio.sleep(1)

    print("\n--- 模拟节流 (快速调整窗口) ---")
    for i in range(5):
        emitter.emit("window_resize", 800 + i*10, 600)
        await asyncio.sleep(0.3)
    # 只有第一次和大约1秒后的某一次会执行

asyncio.run(simulate_events())

# 预期输出 (时间戳会变化):
# --- 模拟防抖 (快速输入) ---
# [167...2.50] 正在搜索: python
#
# --- 模拟节流 (快速调整窗口) ---
# [167...3.50] 处理窗口大小: 800x600
# [167...4.50] 处理窗口大小: 830x600
```

---

<a name="三独立工具函数"></a>
## 三、独立工具函数

本章节详细介绍 `utils` 模块中提供的、来自 `helper.py` 文件的所有独立工具函数。这些函数根据功能被分为"列表与容器操作"和"文件系统操作"两类。

<a name="31-列表与容器操作"></a>
### 3.1. 列表与容器操作

#### `find_same_elements_groups(iterable, comparator=None)`
- **作用**: 在一个可迭代对象中查找并组合连续的相同元素。
- **参数**:
    - `iterable` (`Iterable`): 输入的可迭代对象。
    - `comparator` (`Optional[Callable]`, optional): 一个可选的比较函数，接受两个参数 `(item1, item2)` 并返回 `True`（如果它们应被视为相同）或 `False`。如果为 `None`，则使用默认的 `==` 运算符进行比较。
- **返回值**: `List[List[Any]]` - 一个二维列表，其中每个子列表都是一组连续的相同元素。

##### 使用示例
```python
from global_tools.utils import find_same_elements_groups

data1 = [1, 1, 2, 2, 2, 3, 1, 1, 4]
print(f"处理 {data1}: {find_same_elements_groups(data1)}")
# 预期输出: 处理 [1, 1, 2, 2, 2, 3, 1, 1, 4]: [[1, 1], [2, 2, 2], [3], [1, 1], [4]]

# 使用自定义比较器（忽略大小写）
data2 = ["Apple", "apple", "Banana", "Orange", "orange", "orange"]
comparator = lambda a, b: a.lower() == b.lower()
print(f"处理 {data2}: {find_same_elements_groups(data2, comparator=comparator)}")
# 预期输出: 处理 ['Apple', 'apple', 'Banana', 'Orange', 'orange', 'orange']: [['Apple', 'apple'], ['Banana'], ['Orange', 'orange', 'orange']]
```

#### `flatten_preserve_types(nested_iterable)`
- **作用**: 递归地展平一个嵌套的可迭代对象（如列表、元组），同时保留原始数据类型，包括字典。
- **参数**:
    - `nested_iterable` (`Iterable`): 包含嵌套列表、元组或字典的可迭代对象。
- **返回值**: `Iterator[Any]` - 一个迭代器，产生所有展平后的元素。

##### 使用示例
```python
from global_tools.utils import flatten_preserve_types

data = [1, (2, 3), [4, [5, {"a": 6}]], 7]
flattened = list(flatten_preserve_types(data))
print(f"展平 {data}: {flattened}")
# 预期输出: 展平 [1, (2, 3), [4, [5, {'a': 6}]]], 7]: [1, 2, 3, 4, 5, {'a': 6}, 7]
```

#### `flatten_preserve_types_no_dict(nested_iterable)`
- **作用**: 与 `flatten_preserve_types` 类似，但它不会递归进入字典内部，而是将整个字典作为一个原子元素。
- **参数**:
    - `nested_iterable` (`Iterable`): 包含嵌套列表、元组或字典的可迭代对象。
- **返回值**: `Iterator[Any]` - 一个迭代器，产生所有展平后的元素。

##### 使用示例
```python
from global_tools.utils import flatten_preserve_types_no_dict

data = [1, (2, 3), [4, [5, {"a": 6}]], 7]
flattened = list(flatten_preserve_types_no_dict(data))
print(f"展平 (不处理字典) {data}: {flattened}")
# 预期输出: 展平 (不处理字典) [1, (2, 3), [4, [5, {'a': 6}]]], 7]: [1, 2, 3, 4, 5, {'a': 6}, 7]
# 注意：此函数的当前实现与 flatten_preserve_types 行为一致。如果需要区分，其内部逻辑需要修改。
```

#### `split_list_evenly(data_list, num_groups)`
- **作用**: 将一个列表尽可能均匀地分割成指定数量的子列表（组）。
- **参数**:
    - `data_list` (`List[Any]`): 要分割的列表。
    - `num_groups` (`int`): 要分割成的组数。
- **返回值**: `List[List[Any]]` - 一个包含 `num_groups` 个子列表的二维列表。

##### 使用示例
```python
from global_tools.utils import split_list_evenly

data1 = list(range(10)) # 10个元素
groups1 = split_list_evenly(data1, 3)
print(f"将 {data1} 分为 3 组: {groups1}")
# 预期输出: 将 [0, 1, 2, 3, 4, 5, 6, 7, 8, 9] 分为 3 组: [[0, 1, 2, 3], [4, 5, 6], [7, 8, 9]]

data2 = list(range(5)) # 5个元素
groups2 = split_list_evenly(data2, 4)
print(f"将 {data2} 分为 4 组: {groups2}")
# 预期输出: 将 [0, 1, 2, 3, 4] 分为 4 组: [[0, 1], [2], [3], [4]]
```

<a name="32-文件系统操作"></a>
### 3.2. 文件系统操作

#### `clean_directory(directory_path, ignore_patterns=None, recursive=True, ignore_errors=False, logger=None)`
- **作用**: 清空指定目录下的所有文件和子目录。
- **参数**:
    - `directory_path` (`str`): 要清空的目录路径。
    - `ignore_patterns` (`Optional[List[str]]`, optional): 一个包含glob模式的列表，匹配的文件或目录将被忽略。例如 `["*.log", ".git"]`。默认为 `None`。
    - `recursive` (`bool`, optional): 是否递归删除子目录。默认为 `True`。
    - `ignore_errors` (`bool`, optional): 是否忽略删除过程中发生的错误（如权限问题）。默认为 `False`。
    - `logger` (`Optional[Logger]`, optional): 用于记录操作日志的 `Logger` 实例。默认为 `None`。
- **返回值**: `tuple[int, int, List[str]]` - 一个元组，包含删除的文件数、删除的目录数以及遇到的错误信息列表。

#### `delete_files(directory_path=None, patterns=None, recursive=True, ignore_patterns=None, ignore_errors=False, dry_run=False, file_list=None, delete_same_basename=False, logger=None)`
- **作用**: 根据多种条件删除文件，功能非常强大。
- **参数**:
    - `directory_path` (`Optional[str]`, optional): 在哪个目录中查找要删除的文件。
    - `patterns` (`Union[str, List[str]]`, optional): 一个或多个glob模式，用于匹配要删除的文件名。
    - `recursive` (`bool`, optional): 是否在子目录中递归查找。默认为 `True`。
    - `ignore_patterns` (`Optional[List[str]]`, optional): 不应删除的文件或目录的glob模式。
    - `ignore_errors` (`bool`, optional): 是否忽略删除错误。默认为 `False`。
    - `dry_run` (`bool`, optional): 如果为 `True`，则只列出将要删除的文件，而不实际执行删除操作。默认为 `False`。
    - `file_list` (`Optional[List[str]]`, optional): 一个明确的文件路径列表，将从这些文件中进行删除，而不是在目录中搜索。
    - `delete_same_basename` (`bool`, optional): 如果为 `True`，当删除一个文件（如 `file.jpg`）时，会自动删除所有具有相同基本名但不同扩展名的文件（如 `file.txt`, `file.json`）。
    - `logger` (`Optional[Logger]`, optional): 用于记录日志的 `Logger` 实例。
- **返回值**: `tuple[List[str], List[str], List[str]]` - 一个元组，包含已删除文件列表、未删除（因为dry_run或错误）文件列表、错误信息列表。

#### `get_file_info(file_path, follow_symlinks=True)`
- **作用**: 获取指定文件的详细信息。
- **参数**:
    - `file_path` (`str`): 文件的路径。
    - `follow_symlinks` (`bool`, optional): 是否跟随符号链接。默认为 `True`。
- **返回值**: `Dict[str, Any]` - 一个包含文件信息的字典，如 `size`, `created_time`, `modified_time`, `is_dir`, `is_file`, `is_symlink` 等。

#### `list_directory_files(directory_path, ...)`
- **作用**: 列出目录中的文件和/或文件夹，支持非常丰富的过滤和排序选项。
- **参数**:
    - `directory_path` (`str`): 要列出内容的目录路径。
    - `include_extensions` (`Union[str, List[str]]`, optional): 只包含这些扩展名的文件。
    - `exclude_extensions` (`Union[str, List[str]]`, optional): 排除这些扩展名的文件。
    - `recursive` (`bool`, optional): 是否递归遍历子目录。
    - `include_hidden` (`bool`, optional): 是否包含隐藏文件/文件夹。
    - `return_metadata` (`bool`, optional): 返回结果是否包含每个文件的元数据（大小、时间等）。
    - `follow_symlinks` (`bool`, optional): 是否跟随符号链接。
    - `relative_paths` (`bool`, optional): 返回的路径是相对路径还是绝对路径。
    - `sort_by` (`Optional[str]`, optional): 排序依据，可选值: `'name'`, `'size'`, `'created'`, `'modified'`。
    - `reverse` (`bool`, optional): 是否反向排序。
    - ...以及更多用于分组的参数。
- **返回值**: `Union[List[Dict[str, Any]], Dict[str, List[Dict[str, Any]]]]` - 取决于是否分组，返回文件信息字典的列表，或按基本名称分组的字典。

#### `find_latest(directory_path, ...)`
- **作用**: 在指定目录中查找最新的文件或文件夹。
- **参数**:
    - `directory_path` (`str`): 搜索的根目录。
    - `file_extensions` (`Union[str, List[str], None]`, optional): 只在这些扩展名的文件中查找。
    - `recursive` (`bool`, optional): 是否递归搜索。
    - `include_folders` (`bool`, optional): 是否也将文件夹纳入比较范围。
    - `sorting_key` (`str`, optional): 使用哪个时间属性来判断"最新"，可选值: `'modified'`, `'created'`, `'accessed'`。
    - `metadata` (`bool`, optional): 返回结果是否包含元数据。
    - ...以及其他过滤参数。
- **返回值**: `Union[str, Dict[str, Any], None]` - 返回最新项的路径或元数据字典，如果找不到则返回 `None`。

#### `find_latest_created_folder(directory_path, logger=None)`
- **作用**: 在指定路径下查找最新创建的文件夹。这是一个 `find_latest` 的简化版。
- **参数**:
    - `directory_path` (`str`): 要搜索的目录。
    - `logger` (`Optional[Logger]`, optional): 日志记录器。
- **返回值**: `Optional[str]` - 最新创建的文件夹的路径，如果找不到则返回 `None`。

##### 文件系统操作函数使用示例
```python
import os
from global_tools.utils import (
    list_directory_files,
    find_latest,
    delete_files,
    get_file_info,
    clean_directory
)

# --- 准备一个测试环境 ---
test_dir = "temp_test_dir"
if not os.path.exists(test_dir):
    os.makedirs(test_dir)
os.makedirs(os.path.join(test_dir, "subdir"))

with open(os.path.join(test_dir, "a.txt"), "w") as f: f.write("a")
with open(os.path.join(test_dir, "b.log"), "w") as f: f.write("b")
with open(os.path.join(test_dir, "subdir", "c.txt"), "w") as f: f.write("c")

# 1. 列出文件
print("--- 列出所有 .txt 文件 (递归) ---")
txt_files = list_directory_files(
    test_dir, 
    include_extensions=[".txt"], 
    recursive=True, 
    return_metadata=False
)
print(txt_files)
# 预期输出: ['temp_test_dir/a.txt', 'temp_test_dir/subdir/c.txt'] (或类似，取决于操作系统)

# 2. 查找最新的文件
print("\n--- 查找最新的文件 ---")
latest_file_info = find_latest(test_dir, metadata=True)
if latest_file_info:
    print(f"找到最新文件: {latest_file_info['path']}")

# 3. 获取文件信息
print("\n--- 获取 a.txt 的信息 ---")
info = get_file_info(os.path.join(test_dir, "a.txt"))
print(f"大小: {info['size']} bytes, 修改时间: {info['modified_time']}")

# 4. 删除文件 (dry run)
print("\n--- 模拟删除所有 .log 文件 ---")
deleted, not_deleted, errors = delete_files(
    test_dir, 
    patterns="*.log", 
    dry_run=True
)
print(f"计划删除: {not_deleted}")
print(f"实际删除: {deleted}")

# 5. 清理目录
print("\n--- 清理测试目录 ---")
files_deleted, dirs_deleted, errors = clean_directory(test_dir)
print(f"删除了 {files_deleted} 个文件和 {dirs_deleted} 个目录。")
assert not os.path.exists(test_dir)
```

---

<a name="四增强进程模块-enhanced_process"></a>
## 四、增强进程模块 (`enhanced_process`)

本章节介绍 `utils` 模块中提供的增强进程相关的功能，它们极大地简化了Python的多进程编程。

<a name="41-processlogger-子进程日志记录器"></a>
### 4.1. ProcessLogger (子进程日志记录器)

`ProcessLogger` 是一个专为子进程设计的轻量级日志记录器。它的核心功能是作为一个代理，将子进程中产生的日志消息安全地发送到主进程进行统一处理。

#### 功能与作用
- **跨进程日志**: 解决了子进程日志难以在主进程中集中管理的问题。
- **简单接口**: 提供了与 `Logger` 类似的 `.log()` 方法，易于使用。
- **灵活的数据结构**: 支持直接发送字符串消息，或发送包含任意自定义字段的字典，为主进程处理日志提供了极大的灵活性。

#### 构造函数 `__init__`

##### `ProcessLogger(mp_queue)`
- **作用**: 创建一个 `ProcessLogger` 实例。
- **参数**:
    - `mp_queue` (`multiprocessing.Queue`): 一个进程间队列，用于将日志条目从子进程发送到主进程。这个队列通常由 `EnhancedProcess` 自动创建和管理。

#### 日志记录方法

##### `log(message_or_dict, level="INFO", color=None)`
- **作用**: 记录一条日志，并将其放入进程间队列发送给主进程。
- **参数**:
    - `message_or_dict` (`Union[str, dict]`):
        - 如果是 **字符串** (`str`)，它将被用作日志消息，并与 `level` 和 `color` 参数一起打包成一个标准的日志字典。
        - 如果是 **字典** (`dict`)，它将被视为一个完整的日志条目直接发送，忽略 `level` 和 `color` 参数。这允许传递任意自定义数据。
    - `level` (`str`, optional): 日志级别，默认为 `"INFO"`。仅在 `message_or_dict` 是字符串时生效。
    - `color` (`Optional[str]`, optional): 建议的日志颜色。仅在 `message_or_dict` 是字符串时生效。
- **返回值**: 无。

#### 使用示例
`ProcessLogger` 通常不直接实例化，而是作为参数传递给 `EnhancedProcess` 的目标函数。详见下面的 `EnhancedProcess` 示例。

<a name="42-enhancedprocess-增强型进程"></a>
### 4.2. EnhancedProcess (增强型进程)

`EnhancedProcess` 是对 Python 原生 `multiprocessing.Process` 的一个高度封装和增强的类。它旨在解决多进程编程中的常见痛点，如数据共享、跨进程通信、日志聚合和健壮的进程生命周期管理。

#### 功能与作用
- **自动数据共享**: 所有 `EnhancedProcess` 实例自动共享同一个由 `DataManagerManager` 管理的数据空间，无需手动设置 `Manager` 或代理。
- **跨进程日志**: 自动为目标函数注入 `ProcessLogger` 实例，子进程的日志可以方便地通过回调在主进程中处理。
- **跨进程数据通信**: 内置数据队列机制，允许子进程向主进程发送任意数据，并由主进程通过异步回调进行消费。
- **健壮的生命周期管理**: 提供了 `terminate_gracefully` 方法，该方法会尝试优雅地停止进程，如果失败则会强制终止，确保进程最终能够被清理。`wait_for_completion` 方法会智能地等待进程结束以及所有相关的日志和数据队列处理完毕。
- **分布式支持**: 支持通过配置使用 `SharedQueueManager`，允许在不同主机间进行数据通信。

#### 构造函数 `__init__`

##### `EnhancedProcess(target, child_log_also_to_console=False, use_shared_queue_manager=False, shared_queue_manager_config=None)`
- **作用**: 创建一个 `EnhancedProcess` 实例。
- **参数**:
    - `target` (`Callable`): 子进程要执行的目标函数。该函数将接收 `shared_data_proxy`, `data_queue`, `process_logger` 三个自动注入的参数，以及 `start()` 方法中传入的任何额外参数。
    - `child_log_also_to_console` (`bool`, optional): 已废弃，仅为兼容性保留。子进程的 `print` 语句现在总是能正常输出到控制台。
    - `use_shared_queue_manager` (`bool`, optional): 是否为业务数据队列启用分布式队列管理器。默认为 `False`，使用本地进程间队列。
    - `shared_queue_manager_config` (`Optional[dict]`, optional): 当 `use_shared_queue_manager` 为 `True` 时，用于配置分布式队列管理器的参数，如 `{'address': ('127.0.0.1', 50000), 'authkey': b'queue'}`。

#### 主要方法

##### `start(*args, **kwargs)`
- **作用**: 启动子进程。
- **参数**:
    - `*args`, `**kwargs`: 这些参数将被传递给 `__init__` 中指定的 `target` 函数。
- **返回值**: 无。

##### `set_log_callback(callback)`
- **作用**: 设置一个回调函数来处理从子进程接收到的日志。
- **参数**:
    - `callback` (`Callable`): 一个回调函数，它接收一个字典（日志条目）作为参数。可以是同步函数或异步 (`async def`) 函数。
- **返回值**: 无。

##### `set_data_consume_callback(callback)`
- **作用**: 设置一个异步回调函数来处理从子进程发送的业务数据。
- **参数**:
    - `callback` (`Callable`): 一个**必须是**异步 (`async def`) 的回调函数，它接收子进程发送的数据作为参数。
- **返回值**: 无。

##### `terminate_gracefully(timeout=None, wait_log_queue=True, callback=None)`
- **作用**: 以最快、最可靠的方式终止子进程。它会依次尝试优雅停止、`terminate`、`kill`，并确保进程最终退出。
- **参数**:
    - `timeout` (`Optional[float]`, optional): 整个终止过程的最大等待时间。
    - `wait_log_queue` (`bool`, optional): 是否在进程终止后继续等待日志队列清空。默认为 `True`。
    - `callback` (`Optional[Callable]`, optional): 进程完全终止后要执行的回调函数（可以是同步或异步）。
- **返回值**: `bool` - 如果进程成功终止，则返回 `True`。

##### `wait_for_completion(timeout=None)`
- **作用**: 阻塞当前线程，直到子进程执行完成，并且相关的日志队列和数据队列也都被消费完毕。
- **参数**:
    - `timeout` (`Optional[float]`, optional): 最大等待时间。
- **返回值**: `bool` - 如果在超时时间内全部完成，则返回 `True`。

##### `EnhancedProcess.get_shared_data_proxy()`
- **作用**: (静态方法) 获取全局共享的数据代理对象，可以在主进程中直接操作共享数据。
- **参数**: 无。
- **返回值**: `SharedDataManager` 的代理实例。

##### `EnhancedProcess.shutdown_manager()`
- **作用**: (静态方法) 关闭全局的数据管理器进程。通常在应用程序退出时调用。
- **参数**: 无。
- **返回值**: 无。

#### 综合使用示例

```python
import asyncio
import time
from global_tools.utils import EnhancedProcess, LogLevel, Logger

# 0. (可选) 获取一个Logger实例用于主进程日志
main_logger = Logger()
main_logger.set_instance_level(LogLevel.DEBUG)

# 1. 定义子进程要执行的目标函数
def worker_task(shared_data_proxy, data_queue, process_logger, task_id, duration):
    """
    这个函数在子进程中运行。
    - shared_data_proxy: 用于访问共享数据。
    - data_queue: 用于向主进程发送业务数据。
    - process_logger: 用于向主进程发送日志。
    """
    print(f"[子进程 {task_id}] 开始工作，将持续 {duration} 秒。")

    # 使用 process_logger 发送日志到主进程
    process_logger.log(f"任务 {task_id} 已启动", level="INFO")
    process_logger.log({
        "message": "自定义日志条目",
        "task_id": task_id,
        "status": "running"
    })

    # 通过 data_queue 发送业务数据到主进程
    if data_queue:
        for i in range(3):
            data_to_send = {"task_id": task_id, "progress": (i + 1) * 33}
            data_queue.put(data_to_send)
            process_logger.log(f"任务 {task_id} 发送了数据: {data_to_send}", level="DEBUG")
            time.sleep(0.2)

    # 操作共享数据
    # 所有对共享数据的操作都应该在锁内完成
    with shared_data_proxy.get_lock():
        shared_data_proxy.append_to_list("completed_tasks", task_id)
        shared_data_proxy.add_to_set("unique_workers", task_id)
    
    time.sleep(duration)
    
    process_logger.log(f"任务 {task_id} 完成", level="INFO")
    return f"任务 {task_id} 的结果"

# 2. 在主进程中定义回调函数
def handle_log(log_entry):
    # 这个函数处理从子进程发来的日志
    main_logger.info(f"[日志回调] 收到日志: {log_entry}", color="#17A2B8")

async def handle_data(data_entry):
    # 这个函数处理从子进程发来的业务数据
    main_logger.info(f"[数据回调] 收到数据: {data_entry}", color="#28A745")
    await asyncio.sleep(0.01) # 模拟异步处理

# 3. 创建和配置 EnhancedProcess 实例
process = EnhancedProcess(target=worker_task)
process.set_log_callback(handle_log)
process.set_data_consume_callback(handle_data)

# 4. 启动进程并等待完成
main_logger.info("--- 启动增强型进程 ---", color="#FFD700")
process.start(task_id="A", duration=1) # 传递参数给 worker_task

finished = process.wait_for_completion(timeout=5)
if finished:
    main_logger.info("--- 进程及所有队列已处理完成 ---", color="#FFD700")
else:
    main_logger.error("--- 等待进程完成超时 ---", color="#DC3545")
    process.terminate_gracefully()

# 5. 在主进程中访问共享数据
shared_data = EnhancedProcess.get_shared_data_proxy()
with shared_data.get_lock():
    completed = shared_data.get_list("completed_tasks")
    workers = shared_data.get_set("unique_workers")
    main_logger.info(f"主进程访问共享数据 - 已完成任务: {completed}", color="#9400D3")
    main_logger.info(f"主进程访问共享数据 - 参与的工人: {workers}", color="#9400D3")

# 6. 清理资源
EnhancedProcess.shutdown_manager()
main_logger.info("--- 共享数据管理器已关闭 ---", color="#FFD700")
```

---

<a name="五高级多进程管理框架-manager_process3"></a>
## 五、高级多进程管理框架 (`manager_process3`)

`manager_process3` 是一个高度集成和自动化的多进程任务处理框架。它旨在将开发者从繁琐的进程管理、数据同步和状态监控中解放出来，只需关注核心的业务逻辑。该框架自动处理任务分发、进程生命周期、数据共享和事件通知。

<a name="51-框架概述"></a>
### 5.1. 框架概述

该框架主要由以下三个核心组件协同工作：
1.  **`ManagedMultiProcess`**: 任务的协调器和主入口。它负责接收输入数据（任务），创建和管理一组工作进程，并将任务分发给它们。它也是与开发者交互的主要接口。
2.  **`SharedDataManager`**: 进程间的数据中心。由 `ManagedMultiProcess` 自动创建和管理，为所有工作进程提供一个统一、安全的共享数据读写空间。它支持字典、列表、集合等数据结构，并内置了锁机制来保证并发操作的原子性。
3.  **`ProcessEventManager`**: 进程生命周期事件中心。它负责监控和触发与进程池状态相关的事件，例如 `PROCESS_COMPLETED` (所有任务完成) 或 `PROCESS_STOPPED` (所有进程停止)。开发者可以监听这些事件来执行清理、通知或后续处理逻辑。

**工作流程**:
开发者将一个任务列表和一个处理任务的回调函数提供给 `ManagedMultiProcess`。`run()` 方法被调用后，框架会自动：
- 启动一个包含多个工作进程的进程池。
- 将任务列表中的任务逐一放入一个内部队列。
- 工作进程从队列中获取任务，并执行用户提供的回调函数来处理它。
- 在处理过程中，工作进程可以通过 `SharedDataManager` 安全地读写共享数据。
- `ManagedMultiProcess` 实例在主进程中通过 `ProcessEventManager` 监控所有进程的状态，并在特定条件下（如所有任务完成）触发相应的事件。

<a name="52-shareddatamanager-共享数据管理器"></a>
### 5.2. SharedDataManager (共享数据管理器)

`SharedDataManager` 通常由 `ManagedMultiProcess` 内部自动管理，并作为代理对象传递给用户定义的回调函数。它提供了多种方法来安全地操作共享数据。

#### 主要方法 (在回调函数中通过代理对象访问)

所有数据操作都应在 `with shared_data_proxy.get_lock():` 代码块内执行，以确保线程/进程安全。

-   `get_lock()`: 获取一个进程同步锁。
-   `add_value(key, value)` / `get_value(key, default=None)`: 读写共享字典中的键值对。
-   `append_to_list(key, value)` / `get_list(key)`: 向共享列表追加元素或获取其拷贝。
-   `add_to_set(key, value)` / `get_set(key)`: 向共享集合添加元素或获取其拷贝。
-   `record_error(error_info)`: 记录一个错误信息字典到内部的错误列表中。
-   `get_errors()`: 获取所有已记录的错误。
-   `register_change_callback(key, callback_func)`: 注册一个回调函数，当指定的 `key` 的值发生变化时触发。

<a name="53-processeventmanager-进程事件管理器"></a>
### 5.3. ProcessEventManager (进程事件管理器)

`ProcessEventManager` 由 `ManagedMultiProcess` 自动实例化和使用。开发者主要通过 `ManagedMultiProcess` 的 `listen_event` 方法与其交互。

#### 预定义事件常量
-   `PROCESS_CREATED`: 所有子进程创建完成后触发。
-   `PROCESS_COMPLETED`: 所有子进程执行完所有任务后立即触发。
-   `PROCESS_COMPLETED_WITH_DATA`: 所有子进程执行完成且数据更新队列为空后触发。
-   `PROCESS_STOPPED`: 所有子进程停止后立即触发。

#### 主要交互方法 (通过 `ManagedMultiProcess` 实例访问)

-   `listen_event(event_key, callback, *args, **kwargs)`: 监听一个进程事件。当事件触发时，执行 `callback`。
-   `unlisten_event(event_key, callback)`: 取消监听。

<a name="54-managedmultiprocess-多进程任务管理器"></a>
### 5.4. ManagedMultiProcess (多进程任务管理器)

这是与开发者交互的主要类。

#### 构造函数 `__init__`

##### `ManagedMultiProcess(input_data, callback_func, num_processes=3, *custom_args, **kwargs)`
- **作用**: 创建一个多进程任务管理器实例。
- **参数**:
    - `input_data` (`Iterable[Any]`): 需要被处理的任务数据，通常是一个列表。
    - `callback_func` (`Callable`): 用户定义的工作函数。该函数将在子进程中执行，并接收共享数据代理对象和单个任务项作为其前两个参数，后面跟着 `*custom_args` 和 `**kwargs`。函数签名通常为 `def my_worker(shared_manager, task_item, ...):`。
    - `num_processes` (`int`, optional): 要启动的工作进程数量。默认为 3。
    - `*custom_args`, `**kwargs`: 这些额外的参数将被原封不动地传递给每个 `callback_func` 的调用。

#### 主要方法

##### `run()`
- **作用**: 启动整个多进程任务处理流程。它会创建进程池，分发任务，并开始处理。此方法是非阻塞的。
- **参数**: 无。
- **返回值**: 无。

##### `wait_all(timeout=None)`
- **作用**: 阻塞当前线程，直到所有任务都被处理完毕。
- **参数**:
    - `timeout` (`Optional[float]`, optional): 最大等待时间（秒）。
- **返回值**: `bool` - 如果在超时时间内所有任务都完成了，则返回 `True`。

##### `stop_all(immediate=False, ...)`
- **作用**: 停止所有工作进程和管理器。
- **参数**:
    - `immediate` (`bool`, optional): 如果为 `True`，则立即终止进程，否则会等待当前任务完成。
- **返回值**: 无。

##### `get_results()`
- **作用**: 获取所有进程成功处理后，通过 `shared_manager.add_value()` 存入共享数据区的结果。
- **参数**: 无。
- **返回值**: `Dict[str, Any]` - 包含所有共享数据的字典。

##### `get_all_errors()`
- **作用**: 获取在处理过程中通过 `shared_manager.record_error()` 记录的所有错误。
- **参数**: 无。
- **返回值**: `List[Dict[str, Any]]` - 错误信息字典的列表。

<a name="55-综合使用示例"></a>
### 5.5. 综合使用示例

这个示例将演示如何使用 `ManagedMultiProcess` 来并行处理一组任务，同时利用共享数据和事件监听。

```python
import time
import os
from global_tools.utils import ManagedMultiProcess, Logger, LogLevel

# 1. 定义将在子进程中执行的工作函数
def process_web_task(shared_manager, url, timeout=5):
    """
    模拟一个网络请求任务。
    - shared_manager: 框架自动注入的共享数据代理。
    - url: 从任务列表中获取的单个任务项。
    - timeout: 从构造函数中传递的额外关键字参数。
    """
    pid = os.getpid()
    print(f"[PID: {pid}] 开始处理URL: {url}")
    
    try:
        # 模拟 I/O 密集型操作
        time.sleep(timeout * 0.1)
        if "fail" in url:
            raise ConnectionError("模拟连接失败")

        # 任务成功，将结果存入共享数据
        result = {"url": url, "status": "ok", "content_length": len(url) * 10}
        with shared_manager.get_lock():
            # 使用 url 作为 key 存储结果
            shared_manager.add_value(url, result)
            # 记录成功处理的URL
            shared_manager.append_to_list("successful_urls", url)

    except Exception as e:
        # 任务失败，记录错误信息
        error_info = {"pid": pid, "url": url, "error": str(e)}
        with shared_manager.get_lock():
            shared_manager.record_error(error_info)
            # 记录失败的URL
            shared_manager.append_to_list("failed_urls", url)

# 2. 定义事件回调函数
def on_all_tasks_completed(mp_instance: ManagedMultiProcess, start_time):
    """
    当所有任务完成时，此函数将被触发。
    - mp_instance: 触发该事件的 ManagedMultiProcess 实例。
    """
    print("\n" + "="*30)
    print(">>> 所有任务处理完成! <<<")
    
    # 从实例中获取最终结果
    errors = mp_instance.get_all_errors()
    results = mp_instance.get_results() # 获取所有通过 add_value 添加的数据
    
    successful_urls = results.get("successful_urls", [])
    failed_urls = results.get("failed_urls", [])
    
    print(f"处理成功 {len(successful_urls)} 个URL: {successful_urls}")
    print(f"处理失败 {len(errors)} 个URL: {failed_urls}")
    
    if errors:
        print("\n详细错误信息:")
        for err in errors:
            print(f"  - {err}")
            
    total_time = time.time() - start_time
    print(f"\n总耗时: {total_time:.2f} 秒")
    print("="*30 + "\n")


# 3. 主程序
if __name__ == "__main__":
    # 准备任务数据
    urls_to_process = [
        "http://example.com/page1",
        "http://example.com/page2",
        "http://example.com/fail_page3",
        "http://example.com/page4",
        "http://example.com/page5",
        "http://example.com/fail_page6",
    ]
    
    main_logger = Logger()
    main_logger.set_instance_level(LogLevel.INFO)
    main_logger.info("--- 开始多进程任务处理 ---")

    start_time = time.time()

    # 4. 创建并配置 ManagedMultiProcess 实例
    multi_process_manager = ManagedMultiProcess(
        input_data=urls_to_process,
        callback_func=process_web_task,
        num_processes=3,
        # 额外的关键字参数将传递给 process_web_task
        timeout=2 
    )

    # 5. 监听任务完成事件
    # 使用 PROCESS_COMPLETED_WITH_DATA 确保所有数据都已更新
    multi_process_manager.listen_event(
        multi_process_manager.get_process_event_manager().PROCESS_COMPLETED_WITH_DATA,
        on_all_tasks_completed,
        # 额外的参数将传递给 on_all_tasks_completed
        start_time=start_time 
    )

    # 6. 启动并等待任务完成
    multi_process_manager.run()
    multi_process_manager.wait_all()

    main_logger.info("--- 主程序执行完毕，等待事件回调完成 ---")
    
    # 7. 优雅地停止所有进程和服务
    multi_process_manager.stop_all()

    main_logger.info("--- 所有资源已释放 ---")
``` 