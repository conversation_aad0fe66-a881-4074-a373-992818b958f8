from global_tools.postgre_sql import PostgreSQLClient
import traceback
from global_tools.ui_tools import LineEditManager, QLabelManager, LogOutput
import os
from global_tools.utils import Colors

# 导入表结构定义
from .scheam import DETECTION_RESULTS_TABLE
from global_tools.utils import Logger

# 导入图像相似度处理器
from .image_similarity_processor import ImageSimilarityProcessor

# 导入坐标相似度处理器
from .coordinate_similarity_processor import CoordinateSimilarityProcessor

import json
import shutil
from pathlib import Path
from typing import Dict, List, Tuple, Any, Optional, Union
import threading  # 用于文件夹计数锁


# 删除多线程相关导入：ThreadPoolExecutor, as_completed


class DataProcessingManager:
	"""
	数据处理管理类，负责从UI获取参数、处理JSON数据、存储到数据库和保存图像

	属性:
		__line_edit_manager: 用于操作QLineEdit控件
		__checkbox_manager: 用于操作QCheckBox控件
		__label_manager: 用于操作QLabel控件
		__log_output: 用于在UI界面输出日志
		__logger: 用于在控制台输出日志
		__db_client: PostgreSQL数据库客户端
		__image_counter: 图像计数器，用于子文件夹分配
		__counter_lock: 线程锁，保证计数器线程安全

	使用示例:
		# 创建实例
		manager = DataProcessingManager(
			line_edit_manager,
			checkbox_manager,
			label_manager,
			log_output,
			logger
		)

		# 处理数据
		result = manager.process_data()

		# 输出处理结果
		print(f"处理结果: {result}")
	"""

	def __init__(
			self, line_edit_manager: LineEditManager, checkbox_manager, label_manager: QLabelManager, log_output, logger: Logger
	):
		"""
		初始化数据处理管理器

		参数:
			line_edit_manager: 操作QLineEdit的管理器实例
			checkbox_manager: 操作QCheckBox的管理器实例
			label_manager: 操作QLabel的管理器实例
			log_output: UI界面日志输出工具
			logger: 控制台日志输出工具
		"""
		self.__line_edit_manager = line_edit_manager
		self.__checkbox_manager = checkbox_manager
		self.__label_manager = label_manager
		self.__log_output = log_output
		self.__logger = logger
		self.__db_client = None  # 数据库连接
		self.__current_folder_index = None  # 当前使用的文件夹索引
		self.__current_folder_count = 0  # 当前文件夹已保存图像计数
		self.__counter_lock = threading.Lock()  # 恢复文件夹计数锁，确保文件夹管理的线程安全
		self.__folder_cache = { }  # 缓存子文件夹的索引与计数情况

		# 进度跟踪变量
		self.__current_progress = 0  # 当前进度
		self.__total_progress = 0  # 总进度
		self.__progress_label_name = "label_619"  # 进度显示标签名称
		self.__progress_label_name2 = "label_636"  # 进度显示标签名称

		# ==================== 相似度判断系统配置 ====================
		# 初始化图像相似度处理器
		try:
			self.__image_similarity_processor = ImageSimilarityProcessor(
				image_cache_dir="./cache/similarity_images",
				target_size=(256, 256),
				margin=20,
				logger=self.__logger,
				log_output=self.__log_output
			)
			self.__logger.info( "图像相似度处理器初始化成功" )
		except Exception as e:
			self.__logger.error( f"图像相似度处理器初始化失败: {str( e )}" )
			self.__image_similarity_processor = None

		# 初始化坐标相似度处理器
		try:
			self.__coordinate_similarity_processor = CoordinateSimilarityProcessor(
				logger=self.__logger,
				log_output=self.__log_output,
				mode='strict'  # 使用严格模式确保判断准确性
			)
			self.__logger.info( "坐标相似度处理器初始化成功" )
		except Exception as e:
			self.__logger.error( f"坐标相似度处理器初始化失败: {str( e )}" )
			self.__coordinate_similarity_processor = None

		self.__initialize_get_control_values()

		# PyQt5控件统一映射配置字典
		# 建立语义化的控件访问接口，便于后续修改和扩展
		self.__WIDGET_MAPPING = {
			# CheckBox控件 - 模型类型选择相关
			"checkboxes": {
				"segment_model_selection": {
					"widget_id": "rotate_146",
					"widget_type": "QCheckBox",
					"description": "分割模型选择复选框 - 控制是否启用分割模型推理",
					"function": "当选中时，系统将使用分割模型进行推理，生成分割掩码数据；未选中时，仅进行目标检测"
				},
				"obb_model_selection": {
					"widget_id": "rotate_147",
					"widget_type": "QCheckBox",
					"description": "OBB模型选择复选框 - 控制是否启用有向边界框模型推理",
					"function": "当选中时，系统将使用OBB模型进行推理，生成有向边界框数据；未选中时，使用标准边界框"
				}
			},

			# LineEdit控件 - 数据库配置和路径设置相关
			"line_edits": {
				"database_name": {
					"widget_id": "lineEdit_174",
					"widget_type": "QLineEdit",
					"description": "数据库名称输入框 - 用于指定PostgreSQL数据库名称",
					"function": "获取用户输入的数据库名称，系统将连接到该数据库进行数据存储，默认为'postgres'"
				},
				"table_name": {
					"widget_id": "lineEdit_175",
					"widget_type": "QLineEdit",
					"description": "表名输入框 - 用于指定数据库中的表名",
					"function": "获取用户输入的表名，系统将在该表中存储检测结果数据，默认为'detection_results'"
				},
				"data_source": {
					"widget_id": "lineEdit_176",
					"widget_type": "QLineEdit",
					"description": "数据源标识输入框 - 用于标识数据来源",
					"function": "获取用户输入的数据源标识，用于区分不同来源的数据，便于数据管理和追踪，默认为'default'"
				},
				"split_ratio": {
					"widget_id": "lineEdit_177",
					"widget_type": "QLineEdit",
					"description": "数据划分比例输入框 - 用于指定训练集和验证集的划分比例",
					"function": "获取用户输入的划分比例（格式如'80:20'），系统将按此比例划分数据为训练集和验证集，默认为'80:20'"
				},
				"pretrain_ratio": {
					"widget_id": "lineEdit_77",
					"widget_type": "QLineEdit",
					"description": "预训练数据比例输入框 - 用于指定预训练数据的比例",
					"function": "获取用户输入的预训练数据比例（0-1之间的小数），控制预训练数据在总数据中的占比，默认为'0.3'"
				},
				"input_folder_path": {
					"widget_id": "lineEdit_48",
					"widget_type": "QLineEdit",
					"description": "JSON文件输入文件夹路径输入框 - 用于指定包含JSON标注文件的文件夹路径",
					"function": "获取用户输入的JSON文件输入文件夹路径，系统将从该路径读取所有JSON标注文件进行处理"
				},
				"output_folder_path": {
					"widget_id": "lineEdit_180",
					"widget_type": "QLineEdit",
					"description": "图像输出文件夹路径输入框 - 用于指定图像文件的输出目录路径",
					"function": "获取用户输入的图像输出文件夹路径，系统将把处理后的图像文件保存到该路径下的子文件夹中"
				}
			},

			# Label控件 - 进度显示相关
			"labels": {
				"progress_display_primary": {
					"widget_id": "label_619",
					"widget_type": "QLabel",
					"description": "主进度显示标签 - 实时显示数据处理的主要进度信息",
					"function": "显示数据处理的主要进度，格式为'当前进度：X/Y 百分比：Z%'，为用户提供实时的处理状态反馈"
				},
				"progress_display_secondary": {
					"widget_id": "label_636",
					"widget_type": "QLabel",
					"description": "辅助进度显示标签 - 实时显示数据处理的辅助进度信息",
					"function": "显示数据处理的辅助进度，格式为'当前进度：X/Y 百分比：Z%'，为用户提供额外的处理状态反馈"
				}
			}
		}

	def __get_widget_value(self, semantic_name: str) -> str:
		"""
		根据语义化名称获取控件的文本值。

		Args:
			semantic_name (str): 控件的语义化名称（在__WIDGET_MAPPING中定义）

		Returns:
			str: 控件的文本值，如果控件不存在则返回空字符串

		Usage Example:
			```python
			# 获取数据库名称
			db_name = self.__get_widget_value("database_name")

			# 获取输入文件夹路径
			input_path = self.__get_widget_value("input_folder_path")

			# 获取数据划分比例
			split_ratio = self.__get_widget_value("split_ratio")
			```
		"""
		try:
			# 查找控件配置
			widget_config = None
			for category, widgets in self.__WIDGET_MAPPING.items():
				if semantic_name in widgets:
					widget_config = widgets[semantic_name]
					break

			if not widget_config:
				if self.__logger:
					self.__logger.warning(f"未找到语义化名称 '{semantic_name}' 对应的控件映射")
				return ""

			widget_id = widget_config["widget_id"]
			widget_type = widget_config["widget_type"]

			# 根据控件类型获取值
			if widget_type == "QLineEdit":
				text_value = self.__line_edit_manager.get_text(widget_id)
				return text_value if text_value is not None else ""
			else:
				if self.__logger:
					self.__logger.warning(f"控件类型 '{widget_type}' 不支持获取文本值")
				return ""

		except Exception as e:
			if self.__logger:
				self.__logger.error(f"获取控件 '{semantic_name}' 的值时发生错误: {e}")
			return ""

	def __get_checkbox_state(self, semantic_name: str) -> bool:
		"""
		根据语义化名称获取复选框的选中状态。

		Args:
			semantic_name (str): 控件的语义化名称（在__WIDGET_MAPPING中定义）

		Returns:
			bool: 复选框选中返回True，未选中或获取失败返回False

		Usage Example:
			```python
			# 获取分割模型选择状态
			is_segment = self.__get_checkbox_state("segment_model_selection")

			# 获取OBB模型选择状态
			is_obb = self.__get_checkbox_state("obb_model_selection")
			```
		"""
		try:
			# 查找控件配置
			widget_config = None
			for category, widgets in self.__WIDGET_MAPPING.items():
				if semantic_name in widgets:
					widget_config = widgets[semantic_name]
					break

			if not widget_config:
				if self.__logger:
					self.__logger.warning(f"未找到语义化名称 '{semantic_name}' 对应的控件映射")
				return False

			widget_id = widget_config["widget_id"]
			widget_type = widget_config["widget_type"]

			# 根据控件类型获取状态
			if widget_type == "QCheckBox":
				return self.__checkbox_manager.get_checked_state_by_object_name(widget_id)
			else:
				if self.__logger:
					self.__logger.warning(f"控件类型 '{widget_type}' 不是复选框")
				return False

		except Exception as e:
			if self.__logger:
				self.__logger.error(f"获取复选框 '{semantic_name}' 的状态时发生错误: {e}")
			return False

	def __update_label_progress(self, semantic_name: str, current_value: int, total_value: int) -> bool:
		"""
		根据语义化名称更新标签的进度显示。

		Args:
			semantic_name (str): 控件的语义化名称（在__WIDGET_MAPPING中定义）
			current_value (int): 当前进度值
			total_value (int): 总进度值

		Returns:
			bool: 更新成功返回True，失败返回False

		Usage Example:
			```python
			# 更新主进度显示
			success = self.__update_label_progress("progress_display_primary", 50, 100)

			# 更新辅助进度显示
			success = self.__update_label_progress("progress_display_secondary", 25, 50)
			```
		"""
		try:
			# 查找控件配置
			widget_config = None
			for category, widgets in self.__WIDGET_MAPPING.items():
				if semantic_name in widgets:
					widget_config = widgets[semantic_name]
					break

			if not widget_config:
				if self.__logger:
					self.__logger.warning(f"未找到语义化名称 '{semantic_name}' 对应的控件映射")
				return False

			widget_id = widget_config["widget_id"]
			widget_type = widget_config["widget_type"]

			# 根据控件类型更新进度
			if widget_type == "QLabel":
				if self.__label_manager and self.__label_manager.has_label(widget_id):
					progress_text = self.__format_progress_text(current_value, total_value)
					self.__label_manager.set_text(widget_id, progress_text)

					# 添加淡入淡出动画效果，提升用户体验
					self.__label_manager.start_fade_animation(widget_id, duration_ms=15)

					self.__logger.debug(f"进度更新: {progress_text}")
					return True
				else:
					if self.__logger:
						self.__logger.warning(f"标签管理器不可用或标签 '{widget_id}' 不存在")
					return False
			else:
				if self.__logger:
					self.__logger.warning(f"控件类型 '{widget_type}' 不是标签")
				return False

		except Exception as e:
			if self.__logger:
				self.__logger.error(f"更新标签 '{semantic_name}' 的进度时发生错误: {e}")
			return False

	def __get_widget_info(self, semantic_name: str) -> Optional[dict]:
		"""
		获取控件的详细配置信息。

		Args:
			semantic_name (str): 控件的语义化名称

		Returns:
			Optional[dict]: 控件的配置信息字典，如果不存在则返回None

		Usage Example:
			```python
			# 获取控件配置信息
			info = self.__get_widget_info("database_name")
			if info:
				print(f"控件ID: {info['widget_id']}")
				print(f"控件类型: {info['widget_type']}")
				print(f"功能描述: {info['description']}")
			```
		"""
		try:
			# 查找控件配置
			for category, widgets in self.__WIDGET_MAPPING.items():
				if semantic_name in widgets:
					return widgets[semantic_name].copy()
			return None

		except Exception as e:
			if self.__logger:
				self.__logger.error(f"获取控件 '{semantic_name}' 的信息时发生错误: {e}")
			return None

	def configure_image_similarity(
			self,
			ssim_weight: float = 0.30,
			histogram_weight: float = 0.25,
			feature_weight: float = 0.20,
			contour_weight: float = 0.15,
			pixel_weight: float = 0.10,
			extreme_threshold: float = 0.95,
			high_threshold: float = 0.85,
			low_threshold: float = 0.50
	):
		"""
		配置图像相似度判断系统

		参数:
			ssim_weight: SSIM权重，默认30%
			histogram_weight: 直方图权重，默认25%
			feature_weight: 特征点权重，默认20%
			contour_weight: 轮廓权重，默认15%
			pixel_weight: 像素级权重，默认10%
			extreme_threshold: 极高相似度阈值，默认95%
			high_threshold: 高相似度阈值，默认85%
			low_threshold: 低相似度阈值，默认50%

		使用示例:
			# 设置更严格的阈值
			manager.configure_image_similarity(
				extreme_threshold=0.98,  # 98%以上极高相似度跳过
				high_threshold=0.90,     # 90%以上高相似度跳过
				low_threshold=0.40       # 40%以下低相似度追加
			)
		"""
		if self.__image_similarity_processor:
			# 配置相似度权重
			self.__image_similarity_processor.configure_similarity_weights(
				ssim_weight=ssim_weight,
				histogram_weight=histogram_weight,
				feature_weight=feature_weight,
				contour_weight=contour_weight,
				pixel_weight=pixel_weight
			)

			# 配置决策阈值
			self.__image_similarity_processor.configure_decision_thresholds(
				extreme_similarity_threshold=extreme_threshold,
				high_similarity_threshold=high_threshold,
				low_similarity_threshold=low_threshold
			)

			self.__log_output.append(
				f"图像相似度配置已更新: 极高≥{extreme_threshold:.0%}, 高≥{high_threshold:.0%}, 低<{low_threshold:.0%}",
				Colors.GREEN
			)
		else:
			self.__log_output.append( "图像相似度处理器未初始化，无法配置", Colors.RED )

	def __format_progress_text( self, current: int, total: int ) -> str:
		"""
		格式化进度显示文本

		参数:
			current: 当前进度值
			total: 总进度值

		返回:
			格式化的进度文本，格式为 "当前/总数 百分比%"

		使用示例:
			progress_text = self.__format_progress_text(5, 100)
			# 返回: "5/100 5.0%"
		"""
		if total <= 0:
			return "0/0 0.0%"

		percentage = (current / total) * 100
		return f"{current}/{total} {percentage:.1f}%"

	def __update_progress_display( self, current: int, total: int, label_name: str = None ) -> None:
		"""
		更新进度显示到UI标签

		参数:
			current: 当前进度值
			total: 总进度值
			label_name: 标签名称，默认使用self.__progress_label_name

		使用示例:
			# 更新进度到默认标签
			self.__update_progress_display(5, 100)

			# 更新进度到指定标签
			self.__update_progress_display(5, 100, "custom_label")
		"""
		try:
			if label_name is None:
				# 使用统一的控件映射接口更新主进度显示
				self.__update_label_progress("progress_display_primary", current, total)
			else:
				# 兼容原有的直接标签名调用方式
				if self.__label_manager and self.__label_manager.has_label( label_name ):
					progress_text = self.__format_progress_text( current, total )
					self.__label_manager.set_text( label_name, progress_text )

					# 添加淡入淡出动画效果，提升用户体验
					self.__label_manager.start_fade_animation( label_name, duration_ms=15 )

					self.__logger.debug( f"进度更新: {progress_text}" )
				else:
					self.__logger.warning( f"标签 {label_name} 不存在或不可用" )
		except Exception as e:
			self.__logger.error( f"更新进度显示时出错: {str( e )}" )

		try:
			# 使用统一的控件映射接口更新辅助进度显示
			self.__update_label_progress("progress_display_secondary", current, total)
		except Exception as e:
			self.__logger.error( f"更新辅助进度显示时出错: {str( e )}" )

	# 不抛出异常，确保主流程继续

	def __initialize_progress( self, total: int ) -> None:
		"""
		初始化进度跟踪

		参数:
			total: 总进度值

		使用示例:
			self.__initialize_progress(100)  # 设置总进度为100
		"""
		self.__current_progress = 0
		self.__total_progress = total
		self.__update_progress_display( 0, total )
		self.__logger.info( f"进度初始化: 总计 {total} 项" )
	
	def __initialize_get_control_values(self):
		""" 初始化控件的值 """
		try:
			self.__similarity_threshold = float(self.__line_edit_manager.get_text(name="lineEdit_181"))
		except ValueError:
			self.__similarity_threshold = 0.45

	def __increment_progress( self, increment: int = 1 ) -> None:
		"""
		增加当前进度并更新显示

		参数:
			increment: 进度增量，默认为1

		使用示例:
			self.__increment_progress()     # 进度+1
			self.__increment_progress(5)    # 进度+5
		"""
		self.__current_progress += increment
		# 确保进度不超过总进度
		if self.__current_progress > self.__total_progress:
			self.__current_progress = self.__total_progress

		self.__update_progress_display( self.__current_progress, self.__total_progress )

	def __complete_progress( self ) -> None:
		"""
		完成进度显示（设置为100%）

		使用示例:
			self.__complete_progress()  # 显示完成状态
		"""
		self.__current_progress = self.__total_progress
		self.__update_progress_display( self.__current_progress, self.__total_progress )
		self.__logger.info( f"进度完成: {self.__current_progress}/{self.__total_progress}" )

	def get_current_progress( self ) -> tuple[ int, int ]:
		"""
		获取当前进度信息

		返回:
			(当前进度, 总进度) 的元组

		使用示例:
			current, total = self.get_current_progress()
			print(f"当前进度: {current}/{total}")
		"""
		return self.__current_progress, self.__total_progress

	def __get_ui_parameters( self ) -> Dict[ str, Any ]:
		"""
		从UI控件获取所有必要的参数

		返回:
			包含所有UI参数的字典，具体字段如下：
			- is_segment: 是否为分割模型 (布尔值)
			- is_obb: 是否为OBB（有向边界框）模型 (布尔值)
			- db_name: 数据库名称 (字符串)
			- table_name: 表名 (字符串)
			- data_source: 数据源标识 (字符串)
			- train_ratio: 训练集比例，取值0-1之间 (浮点数)
			- pretrain_ratio: 预训练数据比例，取值0-1之间 (浮点数)
			- input_folder: 输入文件夹路径 (字符串)
			- output_folder: 输出文件夹路径 (字符串)
		"""
		# 使用统一的控件映射接口获取模型推理类型
		is_segment = self.__get_checkbox_state("segment_model_selection")  # 分割模型选择框
		is_obb = self.__get_checkbox_state("obb_model_selection")  # OBB模型选择框

		# 使用统一的控件映射接口获取数据库参数
		db_name = self.__get_widget_value("database_name") or "postgres"  # 数据库名称，默认为postgres
		table_name = self.__get_widget_value("table_name") or "detection_results"  # 表名，默认为detection_results
		data_source = self.__get_widget_value("data_source") or "default"  # 数据源标识，默认为default

		# 使用统一的控件映射接口获取数据划分参数，格式为"训练比例:验证比例"，如"80:20"
		split_ratio_text = self.__get_widget_value("split_ratio") or "80:20"
		try:
			train_ratio, val_ratio = map( int, split_ratio_text.split( ':' ) )
			train_ratio = train_ratio / (train_ratio + val_ratio)  # 计算训练集占比
		except (ValueError, ZeroDivisionError):
			self.__log_output.append( f"无效的划分比例: {split_ratio_text}，使用默认值 0.8", Colors.ORANGE )
			self.__logger.warning( f"无效的划分比例: {split_ratio_text}，使用默认值 0.8" )
			train_ratio = 0.8  # 默认训练集占比为80%

		# 使用统一的控件映射接口获取预训练数据比例
		pretrain_ratio_text = self.__get_widget_value("pretrain_ratio") or "0.3"  # 预训练数据比例
		try:
			pretrain_ratio = float( pretrain_ratio_text )
		except ValueError:
			self.__log_output.append( f"无效的预训练比例: {pretrain_ratio_text}，使用默认值 0.3", Colors.ORANGE )
			self.__logger.warning( f"无效的预训练比例: {pretrain_ratio_text}，使用默认值 0.3" )
			pretrain_ratio = 0.3  # 默认预训练数据比例为30%

		# 使用统一的控件映射接口获取输入和输出路径
		input_folder = self.__get_widget_value("input_folder_path") or ""  # JSON文件输入文件夹路径
		output_folder = self.__get_widget_value("output_folder_path") or ""  # 图像输出文件夹路径

		# 返回包含所有参数的字典
		return {
			"is_segment": is_segment,  # 是否为分割模型
			"is_obb": is_obb,  # 是否为OBB模型
			"db_name": db_name,  # 数据库名称
			"table_name": table_name,  # 表名
			"data_source": data_source,  # 数据源标识
			"train_ratio": train_ratio,  # 训练集比例
			"pretrain_ratio": pretrain_ratio,  # 预训练数据比例
			"input_folder": input_folder,  # 输入文件夹路径
			"output_folder": output_folder  # 输出文件夹路径
		}

	def __connect_database( self, db_name: str ) -> bool:
		"""
		连接或创建PostgreSQL数据库，并保存配置用于线程本地连接

		参数:
			db_name: 数据库名称

		返回:
			连接成功返回True，否则返回False
		"""
		try:
			# 创建数据库连接（单线程版本）
			self.__db_client = PostgreSQLClient(
				host="localhost",
				port=5432,
				database=db_name,
				user="postgres",
				password="123456",
				min_connections=1,
				max_connections=5,
				application_name="X-AnyLabeling"
			)
			self.__log_output.append( f"成功连接到数据库: {db_name}", Colors.GREEN )
			self.__logger.info( f"成功连接到数据库: {db_name}" )
			return True
		except Exception as e:
			self.__log_output.append( f"数据库连接失败: {str( e )}", Colors.RED )
			self.__logger.error( f"数据库连接失败: {str( e )}" )
			self.__logger.error( traceback.format_exc() )
			return False

	# 删除多线程相关方法：
	# - __get_thread_db_client()
	# - __cleanup_thread_connections()
	# - __log_connection_pool_status()

	def __table_exists( self, table_name: str ) -> bool:
		"""
		检查表是否存在

		参数:
			table_name: 表名

		返回:
			表存在返回True，否则返回False

		使用示例:
			exists = self.__table_exists("my_table")
			if exists:
				print("表已存在")
		"""
		if not self.__db_client:
			self.__log_output.append( "数据库未连接", Colors.RED )
			self.__logger.error( "数据库未连接" )
			return False

		try:
			# 使用PostgreSQL系统表查询表是否存在
			sql = """
				SELECT EXISTS (
					SELECT 1
					FROM information_schema.tables
					WHERE table_name = %s
					AND table_schema = 'public'
				)
			"""
			result = self.__db_client.execute_query( sql, (table_name,), fetch=True )

			if result and len( result ) > 0:
				exists = result[ 0 ][ 0 ]  # 获取EXISTS查询的结果
				# 只在控制台记录技术检查结果
				self.__logger.info( f"表 {table_name} 存在性检查结果: {exists}" )
				return exists
			else:
				self.__log_output.append( f"检查表 {table_name} 是否存在时查询无结果", Colors.RED )
				self.__logger.error( f"检查表 {table_name} 是否存在时查询无结果" )
				return False

		except Exception as e:
			self.__log_output.append( f"检查表 {table_name} 是否存在时出错: {str( e )}", Colors.RED )
			self.__logger.error( f"检查表 {table_name} 是否存在时出错: {str( e )}" )
			self.__logger.error( traceback.format_exc() )
			return False

	def __create_table( self, table_name: str ) -> bool:
		"""
		创建数据表，如果表不存在

		参数:
			table_name: 表名

		返回:
			创建成功返回True，否则返回False
		"""
		if not self.__db_client:
			self.__log_output.append( "数据库未连接", Colors.RED )
			self.__logger.error( "数据库未连接" )
			return False

		try:
			# 先检查表是否存在
			if self.__table_exists( table_name ):
				# UI显示简洁信息
				self.__log_output.append( f"数据表已存在", Colors.GREEN )
				self.__logger.info( f"表 {table_name} 已存在，跳过创建" )
				return True

			# 使用预定义的Schema创建表
			schema_dict = DETECTION_RESULTS_TABLE.copy()
			# schema_dict["table_name"] = table_name  # 使用用户指定的表名
			del schema_dict[ 'table_name' ]
			# 创建表
			result = self.__db_client.create_table( table_name, schema_dict )

			if result.get( "success", False ):
				# UI显示简洁信息
				self.__log_output.append( f"数据表创建成功", Colors.GREEN )
				self.__logger.info( f"成功创建表: {table_name}" )
				return True
			else:
				self.__log_output.append( f"创建表失败: {result.get( 'error', '未知错误' )}", Colors.RED )
				self.__logger.error( f"创建表失败: {result.get( 'error', '未知错误' )}" )
				return False

		except Exception as e:
			self.__log_output.append( f"创建表失败: {str( e )}", Colors.RED )
			self.__logger.error( f"创建表失败: {str( e )}" )
			self.__logger.error( traceback.format_exc() )
			return False

	def __read_json_files( self, folder_path: str ) -> List[ Dict[ str, Any ] ]:
		"""
		从指定文件夹读取所有JSON文件

		参数:
			folder_path: 包含JSON文件的文件夹路径

		返回:
			包含所有JSON数据的列表，每个元素是一个字典{数据:字典, 文件路径:字符串}
		"""
		json_data_list = [ ]
		folder_path = os.path.abspath( folder_path )

		if not os.path.exists( folder_path ):
			self.__log_output.append( f"文件夹不存在: {folder_path}", Colors.RED )
			self.__logger.error( f"文件夹不存在: {folder_path}" )
			return json_data_list

		# UI显示简洁信息
		self.__log_output.append( f"开始读取JSON文件", Colors.GREEN )
		self.__logger.info( f"开始从 {folder_path} 读取JSON文件" )

		try:
			# 获取所有JSON文件
			json_files = [ f for f in os.listdir( folder_path ) if f.endswith( '.json' ) ]
			total_files = len( json_files )

			if total_files == 0:
				# UI显示简洁警告
				self.__log_output.append( f"未找到JSON文件", Colors.ORANGE )
				self.__logger.warning( f"在 {folder_path} 中未找到JSON文件" )
				return json_data_list

			self.__log_output.append( f"找到 {total_files} 个JSON文件", Colors.GREEN )
			self.__logger.info( f"找到 {total_files} 个JSON文件" )

			# 读取所有JSON文件
			for i, json_file in enumerate( json_files ):
				if i % 100 == 0:  # 每处理100个文件更新一次进度
					# UI显示简洁进度
					self.__log_output.append( f"处理进度: {i}/{total_files}", Colors.GREEN )
					self.__logger.info( f"正在处理: {i}/{total_files} JSON文件" )

				file_path = os.path.join( folder_path, json_file )
				try:
					with open( file_path, 'r', encoding='utf-8' ) as f:
						data = json.load( f )
						json_data_list.append(
							{
								'data': data,
								'file_path': file_path
							}
						)
				except Exception as e:
					self.__log_output.append( f"读取JSON文件 {json_file} 失败: {str( e )}", Colors.ORANGE )
					self.__logger.warning( f"读取JSON文件 {json_file} 失败: {str( e )}" )
					continue

			# UI显示简洁结果
			self.__log_output.append( f"读取完成: {len( json_data_list )} 个文件", Colors.GREEN )
			self.__logger.info( f"成功读取 {len( json_data_list )} 个JSON文件" )
			return json_data_list
		except Exception as e:
			self.__log_output.append( f"读取JSON文件失败: {str( e )}", Colors.RED )
			self.__logger.error( f"读取JSON文件失败: {str( e )}" )
			self.__logger.error( traceback.format_exc() )
			return json_data_list



	def __split_data( self, json_data_list: List[ Dict[ str, Any ] ], train_ratio: float, pretrain_ratio: float ) -> \
			Dict[ str, Any ]:
		"""
		根据比例划分训练数据和预训练数据，为每条数据添加标识

		新的逻辑：
		1. 为每条数据添加标识字段，避免数据重复
		2. 标识字段：pretrain_train, pretrain_val, train, val
		3. 每条数据只保存一次，通过标识区分用途
		4. 消除数据重复，确保数据库中每条记录只保存一次

		参数:
			json_data_list: JSON数据列表
			train_ratio: 训练集比例(0-1之间的浮点数)
			pretrain_ratio: 预训练数据比例(0-1之间的浮点数)

		返回:
			包含标识数据和统计信息的字典：
			{
				"all_data": [带标识的数据列表],
				"statistics": {
					"total": 总数据量,
					"train": 训练数据量,
					"val": 验证数据量,
					"pretrain_train": 预训练训练数据量,
					"pretrain_val": 预训练验证数据量
				}
			}
		"""
		import random
		random.seed( 42 )  # 固定随机种子，确保结果可复现

		# 打乱数据
		shuffled_data = json_data_list.copy()
		random.shuffle( shuffled_data )

		total_count = len( shuffled_data )
		if total_count == 0:
			return {
				"all_data": [ ],
				"statistics": {
					"total": 0, "train": 0, "val": 0,
					"pretrain_train": 0, "pretrain_val": 0
				}
			}

		# 特殊处理：当JSON文件数量小于等于20个时
		if total_count <= 20:
			# UI显示简洁信息
			self.__log_output.append( f"小数据集模式: {total_count} 个文件", Colors.GREEN )
			self.__logger.info( f"检测到JSON文件数量为 {total_count} 个（≤20），所有数据将同时作为预训练和真实训练数据" )

			# 根据训练比例划分训练集和验证集
			train_count = int( total_count * train_ratio )

			# 为每条数据添加标识
			for i, data in enumerate( shuffled_data ):
				# 初始化所有标识为false
				data[ "pretrain_train" ] = "false"
				data[ "pretrain_val" ] = "false"
				data[ "train" ] = "false"
				data[ "val" ] = "false"

				if i < train_count:
					# 训练数据：同时是预训练和真实训练数据
					data[ "pretrain_train" ] = "true"
					data[ "train" ] = "true"
				else:
					# 验证数据：同时是预训练和真实验证数据
					data[ "pretrain_val" ] = "true"
					data[ "val" ] = "true"

			# 统计信息
			val_count = total_count - train_count

			# UI显示简洁的划分结果
			self.__log_output.append( f"数据划分完成: 训练{train_count}条, 验证{val_count}条", Colors.GREEN )
			# 控制台记录详细信息
			self.__logger.info( f"数据划分完成:" )
			self.__logger.info( f"  - 总数据: {total_count} 条" )
			self.__logger.info( f"  - 预训练数据与真实训练数据相同: {total_count} 条" )
			self.__logger.info( f"  - 训练集: {train_count} 条, 验证集: {val_count} 条" )

			return {
				"all_data": shuffled_data,
				"statistics": {
					"total": total_count,
					"train": train_count,
					"val": val_count,
					"pretrain_train": train_count,
					"pretrain_val": val_count
				}
			}

		# 改进后的处理逻辑（数据量>20时）
		# 步骤1: 计算预训练数据数量
		pretrain_count = int( total_count * pretrain_ratio )

		# 步骤2: 计算各类数据的数量
		pretrain_train_count = int( pretrain_count * train_ratio )
		pretrain_val_count = pretrain_count - pretrain_train_count

		additional_count = total_count - pretrain_count
		additional_train_count = int( additional_count * train_ratio )
		additional_val_count = additional_count - additional_train_count

		# 总的训练和验证数据量
		total_train_count = pretrain_train_count + additional_train_count
		total_val_count = pretrain_val_count + additional_val_count

		# 步骤3: 为每条数据添加标识
		for i, data in enumerate( shuffled_data ):
			# 初始化所有标识为false
			data[ "pretrain_train" ] = "false"
			data[ "pretrain_val" ] = "false"
			data[ "train" ] = "false"
			data[ "val" ] = "false"

			if i < pretrain_train_count:
				# 预训练训练数据：同时是预训练和真实训练数据
				data[ "pretrain_train" ] = "true"
				data[ "train" ] = "true"
			elif i < pretrain_count:
				# 预训练验证数据：同时是预训练和真实验证数据
				data[ "pretrain_val" ] = "true"
				data[ "val" ] = "true"
			elif i < pretrain_count + additional_train_count:
				# 额外训练数据：只是真实训练数据
				data[ "train" ] = "true"
			else:
				# 额外验证数据：只是真实验证数据
				data[ "val" ] = "true"

		# UI显示简洁的划分结果
		self.__log_output.append(
			f"数据划分完成: 总{total_count}条, 训练{total_train_count}条, 验证{total_val_count}条", Colors.GREEN
		)
		# 控制台记录详细的划分信息
		self.__logger.info( f"数据划分完成:" )
		self.__logger.info( f"  - 总数据: {total_count} 条" )
		self.__logger.info(
			f"  - 预训练数据: {pretrain_count} 条 (训练: {pretrain_train_count}, 验证: {pretrain_val_count})"
		)
		self.__logger.info(
			f"  - 额外训练数据: {additional_count} 条 (训练: {additional_train_count}, 验证: {additional_val_count})"
		)
		self.__logger.info( f"  - 真实训练数据: {total_count} 条 (训练: {total_train_count}, 验证: {total_val_count})" )
		self.__logger.info( f"  - 数据无重复: 每条数据只保存一次，通过标识区分用途" )

		return {
			"all_data": shuffled_data,
			"statistics": {
				"total": total_count,
				"train": total_train_count,
				"val": total_val_count,
				"pretrain_train": pretrain_train_count,
				"pretrain_val": pretrain_val_count
			}
		}

	def __get_folder_for_image( self, output_folder: str ) -> str:
		"""
		获取图像应该保存的子文件夹路径，采用性能最优的方案

		参数:
			output_folder: 输出文件夹的根路径

		返回:
			子文件夹的完整路径
		"""
		with self.__counter_lock:
			# 初始化文件夹状态
			if self.__current_folder_index is None:
				self.__initialize_folder_state( output_folder )

			# 此时self.__current_folder_index一定不为None
			assert self.__current_folder_index is not None

			# 检查当前文件夹是否已达到10000张图像
			if self.__current_folder_count >= 10000:
				# 需要创建新文件夹
				self.__current_folder_index = self.__current_folder_index + 1  # 避免使用+=运算符
				self.__current_folder_count = 0

				# 更新文件夹缓存
				folder_start = self.__current_folder_index * 10000
				folder_end = folder_start + 9999
				subfolder_name = f"{folder_start}-{folder_end}"
				self.__folder_cache[ self.__current_folder_index ] = 0  # 新文件夹计数为0

			# 当前文件夹路径
			folder_start = self.__current_folder_index * 10000
			folder_end = folder_start + 9999
			subfolder_name = f"{folder_start}-{folder_end}"
			subfolder_path = os.path.join( output_folder, subfolder_name )

			# 如果子文件夹不存在，创建它
			os.makedirs( subfolder_path, exist_ok=True )

			# 增加当前文件夹计数
			self.__current_folder_count += 1
			self.__folder_cache[ self.__current_folder_index ] = self.__current_folder_count

			return subfolder_path

	def __initialize_folder_state( self, output_folder: str ) -> None:
		"""
		初始化文件夹状态，找出最后一个子文件夹并计算其中的文件数量

		参数:
			output_folder: 输出文件夹的根路径
		"""
		try:
			# 确保输出文件夹存在
			os.makedirs( output_folder, exist_ok=True )

			# 先找出所有符合命名规则的子文件夹
			valid_folders = [ ]
			for item in os.listdir( output_folder ):
				folder_path = os.path.join( output_folder, item )
				if os.path.isdir( folder_path ) and '-' in item:
					try:
						start, _ = item.split( '-' )  # 只需要start部分
						folder_index = int( start ) // 10000
						valid_folders.append( (folder_index, folder_path) )
					except (ValueError, TypeError):
						continue

			if not valid_folders:
				# 没有符合规则的子文件夹，创建第一个
				self.__current_folder_index = 0
				self.__current_folder_count = 0
				self.__folder_cache[ 0 ] = 0
				# 只在控制台记录技术细节
				self.__logger.info( "未找到现有子文件夹，从0开始" )
				return

			# 找出索引最大的子文件夹
			valid_folders.sort( key=lambda x: x[ 0 ], reverse=True )
			max_index, last_folder = valid_folders[ 0 ]

			# 计算最后一个子文件夹中的文件数量
			image_extensions = ('.jpg', '.jpeg', '.png', '.bmp', '.tif', '.tiff', '.gif')
			file_count = sum(
				1 for f in os.listdir( last_folder )
				if os.path.isfile( os.path.join( last_folder, f ) )
				and f.lower().endswith( image_extensions )
			)

			# 设置当前状态
			self.__current_folder_index = max_index
			self.__current_folder_count = file_count
			self.__folder_cache[ max_index ] = file_count

			# 检查是否需要创建新文件夹
			if file_count >= 10000:
				self.__current_folder_index += 1
				self.__current_folder_count = 0
				self.__folder_cache[ self.__current_folder_index ] = 0

			# 只在控制台记录技术细节
			self.__logger.info(
				f"从现有子文件夹恢复，当前文件夹索引: {self.__current_folder_index}, 已有文件数: {self.__current_folder_count}"
			)

		except Exception as e:
			# 出错时使用默认值
			self.__log_output.append( f"初始化文件夹状态时出错: {str( e )}", Colors.RED )
			self.__logger.error( f"初始化文件夹状态时出错: {str( e )}" )
			self.__log_output.append( traceback.format_exc(), Colors.RED )
			self.__logger.error( traceback.format_exc() )
			self.__current_folder_index = 0
			self.__current_folder_count = 0
			self.__folder_cache[ 0 ] = 0

	def __save_image( self, json_data: Dict[ str, Any ], json_file_path: str, output_folder: str ) -> Optional[ str ]:
		"""
		将图像文件保存到输出文件夹

		改进后的逻辑：
		1. 统一处理绝对路径和相对路径
		2. 在保存前详细检查原始图像是否存在
		3. 提供详细的日志信息用于调试

		参数:
			json_data: JSON数据
			json_file_path: JSON文件路径
			output_folder: 输出文件夹路径

		返回:
			成功时返回保存后的图像路径，失败时返回None
		"""
		try:
			# 步骤1: 获取和验证图像路径
			image_path = json_data.get( 'imagePath', '' )
			if not image_path:
				self.__log_output.append( f"图像路径获取失败", Colors.RED )
				self.__logger.error( f"图像路径获取失败" )
				self.__log_output.append( f"  - JSON文件: {json_file_path}", Colors.RED )
				self.__logger.error( f"  - JSON文件: {json_file_path}" )
				self.__log_output.append( f"  - 错误原因: JSON文件中未找到imagePath字段", Colors.RED )
				self.__logger.error( f"  - 错误原因: JSON文件中未找到imagePath字段" )
				self.__log_output.append( f"  - 建议: 请检查JSON文件格式是否正确", Colors.RED )
				self.__logger.error( f"  - 建议: 请检查JSON文件格式是否正确" )
				return None

			# 获取JSON文件相关信息
			json_dir = os.path.dirname( json_file_path )
			json_basename = os.path.splitext( os.path.basename( json_file_path ) )[ 0 ]
			image_basename = os.path.splitext( os.path.basename( image_path ) )[ 0 ]

			# UI只显示关键信息
			self.__log_output.append( f"处理图像保存", Colors.GREEN )
			# 控制台记录详细信息
			self.__logger.info( f"开始处理图像保存" )
			self.__logger.info( f"  - JSON文件: {json_file_path}" )
			self.__logger.info( f"  - 原始图像路径: {image_path}" )
			self.__logger.info( f"  - JSON目录: {json_dir}" )

			# 步骤2: 检查JSON文件名与图像文件名是否匹配
			if json_basename != image_basename:
				self.__log_output.append(
					f"文件名不匹配: JSON({json_basename}) vs 图像({image_basename})", Colors.ORANGE
				)
				self.__logger.warning( f"文件名不匹配: JSON({json_basename}) vs 图像({image_basename})" )
				# 尝试在同目录下查找与JSON同名的图像文件
				found_matching_image = False
				for ext in [ '.jpg', '.jpeg', '.png', '.bmp', '.tif', '.tiff' ]:
					potential_image_path = os.path.join( json_dir, json_basename + ext )
					if os.path.exists( potential_image_path ):
						# 只在控制台记录详细路径信息
						self.__logger.info( f"找到匹配的图像文件: {potential_image_path}" )
						image_path = json_basename + ext
						found_matching_image = True
						break

				if not found_matching_image:
					self.__log_output.append(
						f"未找到与JSON文件名匹配的图像文件，继续使用原始路径: {image_path}", Colors.ORANGE
					)
					self.__logger.warning( f"未找到与JSON文件名匹配的图像文件，继续使用原始路径: {image_path}" )

			# 步骤3: 构建完整的图像路径
			if not os.path.isabs( image_path ):
				# 处理相对路径
				if os.path.dirname( image_path ) == '':
					# image_path只是文件名，直接与JSON目录拼接
					image_full_path = os.path.join( json_dir, image_path )
					# 只在控制台记录路径处理详情
					self.__logger.info( f"相对路径处理: {image_path} → {image_full_path}" )
				else:
					# image_path含有路径部分，正常拼接
					image_full_path = os.path.join( json_dir, image_path )
					# 只在控制台记录路径拼接详情
					self.__logger.info( f"相对路径拼接: {json_dir} + {image_path} → {image_full_path}" )
			else:
				# 处理绝对路径
				image_full_path = image_path
				# 只在控制台记录路径信息
				self.__logger.info( f"使用绝对路径: {image_full_path}" )

			# 步骤4: 详细的图像存在性检查（核心改进）
			if not os.path.exists( image_full_path ):
				self.__log_output.append( f"图像文件不存在，无法保存", Colors.RED )
				self.__logger.error( f"图像文件不存在，无法保存" )
				self.__log_output.append( f"  - JSON文件: {json_file_path}", Colors.RED )
				self.__logger.error( f"  - JSON文件: {json_file_path}" )
				self.__log_output.append( f"  - 原始图像路径: {image_path}", Colors.RED )
				self.__logger.error( f"  - 原始图像路径: {image_path}" )
				self.__log_output.append( f"  - 查找的完整路径: {image_full_path}", Colors.RED )
				self.__logger.error( f"  - 查找的完整路径: {image_full_path}" )
				self.__log_output.append( f"  - JSON目录: {json_dir}", Colors.RED )
				self.__logger.error( f"  - JSON目录: {json_dir}" )
				self.__log_output.append(
					f"  - 路径类型: {'绝对路径' if os.path.isabs( image_path ) else '相对路径'}", Colors.RED
				)
				self.__logger.error( f"  - 路径类型: {'绝对路径' if os.path.isabs( image_path ) else '相对路径'}" )
				self.__log_output.append( f"  - 建议: 请检查图像文件是否存在于指定位置", Colors.RED )
				self.__logger.error( f"  - 建议: 请检查图像文件是否存在于指定位置" )
				return None

			# 图像存在性检查通过，只在控制台记录详细路径
			self.__logger.info( f"图像文件检查通过: {image_full_path}" )

			# 步骤5: 处理文件扩展名
			image_extension = os.path.splitext( image_path )[ 1 ]
			if not image_extension:
				# 如果没有扩展名，尝试从文件内容猜测
				import imghdr
				image_type = imghdr.what( image_full_path )
				if image_type:
					image_extension = f".{image_type}"
					# 只在控制台记录技术细节
					self.__logger.info( f"从文件内容检测到扩展名: {image_extension}" )
				else:
					image_extension = ".jpg"  # 默认使用jpg扩展名
					self.__log_output.append( f"无法检测文件类型，使用默认扩展名: {image_extension}", Colors.ORANGE )
					self.__logger.warning( f"无法检测文件类型，使用默认扩展名: {image_extension}" )

			# 构建标准化的图像文件名（与JSON文件名相同，但保留原扩展名）
			image_filename = json_basename + image_extension

			# 步骤6: 获取输出子文件夹并保存
			subfolder_path = self.__get_folder_for_image( output_folder )
			target_path = os.path.join( subfolder_path, image_filename )

			# 复制图像文件
			shutil.copy2( image_full_path, target_path )

			# UI显示简洁信息，控制台记录详细路径
			self.__log_output.append( f"图像保存成功", Colors.GREEN )
			self.__logger.info( f"图像保存成功: {target_path}" )
			return target_path

		except Exception as e:
			self.__log_output.append( f"保存图像时发生异常", Colors.RED )
			self.__logger.error( f"保存图像时发生异常" )
			self.__log_output.append( f"  - JSON文件: {json_file_path}", Colors.RED )
			self.__logger.error( f"  - JSON文件: {json_file_path}" )
			self.__log_output.append( f"  - 异常信息: {str( e )}", Colors.RED )
			self.__logger.error( f"  - 异常信息: {str( e )}" )
			self.__log_output.append( f"  - 建议: 请检查文件权限和磁盘空间", Colors.RED )
			self.__logger.error( f"  - 建议: 请检查文件权限和磁盘空间" )
			return None

	def __check_existing_record_by_image_id( self, table_name: str, image_id: str ) -> Optional[ Dict[ str, Any ] ]:
		"""
		根据 image_id 查询数据库中是否已存在相同记录

		参数:
			table_name: 表名
			image_id: 图像ID

		返回:
			如果存在返回记录字典，否则返回None
			记录包含: detection_id, obb_data, segmentation_data 等字段
		"""
		if not self.__db_client:
			self.__log_output.append( "数据库未连接", Colors.RED )
			self.__logger.error( "数据库未连接" )
			return None

		try:
			# 使用 PostgreSQLClient 的 fetch_data 方法
			condition_str = f"image_id == '{image_id}'"
			result = self.__db_client.fetch_data(
				table_name=table_name,
				condition_str=condition_str,
				columns=[ "detection_id", "obb_data", "segmentation_data", "model_output_type" ],
				limit=1
			)

			if result.get( "success", False ) and result.get( "data" ): # type: ignore
				# 找到了匹配的记录
				record = result[ "data" ][ 0 ]  # 取第一条记录 # type: ignore
				return {
					"detection_id": record[ "detection_id" ],
					"obb_data": record.get( "obb_data" ),
					"segmentation_data": record.get( "segmentation_data" ),
					"model_output_type": record.get( "model_output_type" )
				}
			else:
				# 没有找到匹配的记录
				return None

		except Exception as e:
			self.__log_output.append( f"查询数据库时出错: {str( e )}", Colors.RED )
			self.__logger.error( f"查询数据库时出错: {str( e )}" )
			return None

	def __update_database_annotation(
			self, table_name: str, image_id: str, original_shapes: List[ Dict ], existing_record: Dict[ str, Any ],
			is_segment: bool
	) -> Dict[ str, Any ]:
		"""
		更新数据库中的标注数据（追加模式，不覆盖）

		使用坐标相似度判断系统来决定是否追加标注数据，避免重复标注。
		基于四维度相似度计算（几何、位置、尺度、形状）和五层智能决策机制。

		参数:
			table_name: 表名
			image_id: 图像ID
			original_shapes: 原始标注数据
			existing_record: 现有数据库记录
			is_segment: 是否为分割数据

		返回:
			更新结果字典，包含成功状态、添加数量、跳过原因等信息
		"""
		import json
		try:
			if not original_shapes:
				return {
					"success": False,
					"error": "原始数据无标注信息",
					"action": "skip"
				}

			# 获取现有的标注数据
			if is_segment:
				existing_annotations = existing_record.get( "segmentation_data", [ ] )
				field_name = "segmentation_data"
			else:
				existing_annotations = existing_record.get( "obb_data", [ ] )
				field_name = "obb_data"

			# 确保现有数据是列表格式
			if not isinstance( existing_annotations, list ):
				existing_annotations = [ ]

			# 合并标注数据：现有数据 + 新数据（使用坐标相似度判断）
			merged_annotations = existing_annotations.copy()
			added_count = 0

			# 使用坐标相似度处理器进行相似度判断
			if not self.__coordinate_similarity_processor:
				# 坐标相似度处理器未初始化，直接追加所有标注
				self.__logger.warning( "坐标相似度处理器未初始化，直接追加所有标注" )
				for orig_shape in original_shapes:
					orig_points = orig_shape.get( "points", [ ] )
					if not orig_points:
						continue
					merged_annotations.append( orig_shape )
					added_count += 1
					self.__logger.debug( f"直接追加标注（坐标相似度处理器不可用）" )
			else:
				# 使用坐标相似度判断
				for orig_shape in original_shapes:
					orig_points = orig_shape.get( "points", [ ] )
					if not orig_points:
						continue

					# 检查与现有标注的相似度
					should_add = True
					max_similarity = 0.0
					best_reason = ""
					best_layer_decision = ""

					for existing_shape in existing_annotations:
						existing_points = existing_shape.get( "points", [ ] )
						if not existing_points:
							continue

						# 使用坐标相似度处理器比较
						try:
							similarity_result = self.__coordinate_similarity_processor.compare_coordinates(
								points1=orig_points,
								points2=existing_points,
								similarity_threshold=self.__similarity_threshold
							)

							current_similarity = similarity_result.get( 'comprehensive_similarity', 0.0 )
							if current_similarity > max_similarity:
								max_similarity = current_similarity
								best_reason = similarity_result.get( 'reason', '' )
								best_layer_decision = similarity_result.get( 'layer_decision', '' )

							# 如果决策是跳过，则不添加这个标注
							if similarity_result.get( 'decision' ) == 'skip':
								should_add = False
								break

						except Exception as e:
							self.__logger.warning( f"坐标相似度计算失败: {str( e )}" )
							# 计算失败时默认添加
							continue

					# 执行最终决策
					if should_add:
						orig_shape["is_appended_annotation"] = True
						merged_annotations.append( orig_shape )
						added_count += 1
						self.__logger.debug( f"追加标注: 最大相似度={max_similarity:.3f}" )
					else:
						self.__logger.debug( f"跳过标注: {best_reason} [{best_layer_decision}]" )

			# 检查是否有数据需要追加
			if added_count == 0:
				# 没有追加任何数据，无需更新数据库
				self.__logger.info( f"无需更新数据库: {image_id} - 没有符合条件的标注需要追加" )
				return {
					"success": True,
					"action": "no_update_needed",
					"reason": "没有符合条件的数据需要追加",
					"field_name": field_name,
					"original_count": len( existing_annotations ),
					"added_count": 0,
					"final_count": len( existing_annotations ),
					"skipped_reason": "所有原始标注都不符合重叠率条件"
				}

			# 有数据需要追加，准备更新数据库
			# 确保数据以正确的格式传递给PostgreSQL客户端
			# 对于jsonb字段，需要确保数据被正确序列化
			try:
				# 将merged_annotations序列化为JSON字符串，然后再传递给数据库客户端
				# 这样可以确保PostgreSQL客户端正确识别为jsonb类型而不是数组类型
				serialized_annotations = json.dumps( merged_annotations, ensure_ascii=False )
				update_data = { field_name: serialized_annotations,'is_appended_annotation': True }
				self.__logger.info(
					f"准备更新数据库: {image_id} - {field_name}字段追加{added_count}个标注，数据已序列化为JSON字符串"
				)
			except (TypeError, ValueError) as e:
				self.__logger.error( f"序列化标注数据失败: {str( e )}" )
				return {
					"success": False,
					"error": f"序列化标注数据失败: {str( e )}",
					"action": "error"
				}

			# 执行数据库更新
			condition_str = f"image_id == '{image_id}'"
			result = self.__db_client.update_data( # type: ignore
				table_name=table_name,
				condition_str=condition_str,
				data_json=update_data
			)

			if result.get( "success", False ):
				updated_count = result.get( "updated_count", 0 )
				self.__logger.info( f"数据库更新成功: {image_id} - 更新了{updated_count}条记录" )
				return {
					"success": True,
					"updated_count": updated_count,
					"field_name": field_name,
					"original_count": len( existing_annotations ),
					"added_count": added_count,
					"final_count": len( merged_annotations ),
					"action": "updated"
				}
			else:
				error_msg = result.get( "error", "更新失败" )
				self.__logger.error( f"数据库更新失败: {image_id} - {error_msg}" )
				return {
					"success": False,
					"error": error_msg,
					"action": "error"
				}

		except Exception as e:
			self.__logger.error( f"更新数据库标注时出错: {str( e )}" )
			return {
				"success": False,
				"error": f"更新数据库标注时出错: {str( e )}",
				"action": "error"
			}

	def __process_duplicate_check_and_update( self, table_name: str, item: Dict, is_segment: bool ) -> Dict[ str, Any ]:
		"""
		处理重复检查和更新逻辑

		参数:
			table_name: 表名
			item: 数据项
			is_segment: 是否为分割数据

		返回:
			处理结果字典
		"""
		# 获取 image_id 和图像路径
		data = item[ 'data' ]
		image_path = os.path.abspath(os.sep.join([os.path.dirname(item['file_path']), data.get( 'imagePath', '' )]))
		image_id = os.path.basename( image_path )

		if not image_id:
			return {
				"action": "insert",
				"reason": "无法获取image_id"
			}

		# 检查数据库中是否已存在该记录
		existing_record = self.__check_existing_record_by_image_id( table_name, image_id )

		if not existing_record:
			# 记录不存在，正常插入
			return {
				"action": "insert",
				"reason": "记录不存在"
			}

		# 记录存在，进行重叠率检查
		original_shapes = data.get( "shapes", [ ] )
		if not original_shapes:
			return {
				"action": "skip",
				"reason": "原始数据无标注信息"
			}

		# 获取数据库中的标注数据
		if is_segment:
			db_annotation_data = existing_record.get( "segmentation_data", [ ] )
		else:
			db_annotation_data = existing_record.get( "obb_data", [ ] )

		if not db_annotation_data:
			# 数据库中无标注数据，更新（此时existing_record中对应字段为空，直接添加新数据）
			update_result = self.__update_database_annotation(
				table_name, image_id, original_shapes, existing_record, is_segment
			)
			return {
				"action": "updated",
				"reason": "数据库中无标注数据",
				"update_result": update_result
			}

		# 直接进行数据更新（使用坐标相似度判断）
		update_result = self.__update_database_annotation(
			table_name, image_id, original_shapes, existing_record, is_segment
		)

		return {
			"action": "updated",
			"reason": f"数据已更新: {update_result.get( 'reason', '' )}",
			"update_result": update_result
		}

	def __save_to_database(
			self, data_list: List[ Dict[ str, Any ] ], training_type: str,
			is_segment: bool, is_obb: bool, table_name: str,
			data_source: str, output_folder: str
	) -> int:
		"""
		将数据保存到数据库

		改进后的逻辑：
		1. 使用线程本地数据库连接，避免多线程冲突
		2. 增强错误处理和连接管理
		3. 确保连接的正确释放

		参数:
			data_list: 数据列表
			training_type: 训练类型("train", "val", "pretrain_train", "pretrain_val")
			is_segment: 是否为分割模型
			is_obb: 是否为OBB模型
			table_name: 表名
			data_source: 数据源
			output_folder: 图像输出文件夹

		返回:
			成功保存的记录数量
		"""
		# 使用主线程数据库连接（单线程版本）
		if not self.__db_client:
			self.__log_output.append( "数据库未连接", Colors.RED )
			self.__logger.error( "数据库未连接" )
			return 0

		if not data_list:
			return 0

		# UI显示简洁信息
		self.__log_output.append( f"处理 {training_type} 数据: {len( data_list )} 条", Colors.GREEN )
		self.__logger.info( f"开始处理 {training_type} 数据: {len( data_list )} 条" )

		success_count = 0
		batch_size = 100
		batches = [ data_list[ i:i + batch_size ] for i in range( 0, len( data_list ), batch_size ) ]

		for batch_idx, batch in enumerate( batches ):
			try:
				records_to_insert = [ ]

				for item in batch:
					data = item[ 'data' ]
					file_path = item[ 'file_path' ]

					# 新增：重复检查和更新逻辑
					duplicate_check_result = self.__process_duplicate_check_and_update( table_name, item, is_segment )

					# 更新进度显示（每处理一个数据项）
					self.__increment_progress()

					if duplicate_check_result[ "action" ] == "skip":
						# 跳过处理
						self.__logger.info(
							f"跳过处理: {os.path.basename( data.get( 'imagePath', '' ) )} - {duplicate_check_result[ 'reason' ]}"
						)
						continue
					elif duplicate_check_result[ "action" ] == "updated":
						# 已更新，记录详细日志
						update_result = duplicate_check_result.get( "update_result", { } )
						action = update_result.get( "action", "" )

						if update_result.get( "success", False ):
							if action == "no_update_needed":
								# 无需更新数据库的情况
								field_name = update_result.get( "field_name", "" )
								reason = update_result.get( "reason", "" )
								self.__log_output.append(
									f"无需更新: {os.path.basename( data.get( 'imagePath', '' ) )} - {reason}",
									Colors.ORANGE
								)
								self.__logger.info(
									f"无需更新数据库: {os.path.basename( data.get( 'imagePath', '' ) )} - {field_name}字段无符合条件的标注"
								)
							elif action == "updated":
								# 实际更新了数据库的情况
								added_count = update_result.get( "added_count", 0 )
								final_count = update_result.get( "final_count", 0 )
								field_name = update_result.get( "field_name", "" )
								self.__log_output.append(
									f"追加成功: {os.path.basename( data.get( 'imagePath', '' ) )} - 新增{added_count}个标注，总计{final_count}个",
									Colors.GREEN
								)
								self.__logger.info(
									f"追加标注数据: {os.path.basename( data.get( 'imagePath', '' ) )} - {field_name}字段新增{added_count}个标注，总计{final_count}个"
								)
								success_count += 1
						else:
							self.__log_output.append(
								f"追加失败: {os.path.basename( data.get( 'imagePath', '' ) )}", Colors.RED
							)
							self.__logger.error(
								f"追加失败: {os.path.basename( data.get( 'imagePath', '' ) )} - {update_result.get( 'error', '未知错误' )}"
							)
						continue
					# 如果 action == "insert"，继续正常的插入流程

					# 获取图像路径、宽度和高度
					image_path = self.__save_image( data, file_path, output_folder )
					if not image_path:
						continue

					image_width = data.get( 'imageWidth', 0 )
					image_height = data.get( 'imageHeight', 0 )
					image_id = os.path.basename( data.get( 'imagePath', '' ) )

					# 准备模型特定数据
					shapes = data.get( 'shapes', [ ] )
					segmentation_data = None
					obb_data = None

					if is_segment:
						segmentation_data = json.dumps( shapes )
					if is_obb:
						obb_data = json.dumps( shapes )

					# 确定训练类型（修复的标识逻辑）
					# 从item中获取标识字段，而不是从item['data']中获取
					training_type_json = { }

					# 检查并添加各种标识（从item中获取，不是从data中）
					if item.get( "pretrain_train" ) == "true":
						training_type_json[ "pretrain_train" ] = "true"
					if item.get( "pretrain_val" ) == "true":
						training_type_json[ "pretrain_val" ] = "true"
					if item.get( "train" ) == "true":
						training_type_json[ "train" ] = "true"
					if item.get( "val" ) == "true":
						training_type_json[ "val" ] = "true"

					# 如果没有任何标识，使用默认值
					if not training_type_json:
						training_type_json = { "type": "unknown" }
						self.__log_output.append( f"数据缺少标识字段，使用默认标识", Colors.ORANGE )
						self.__logger.warning( f"数据缺少标识字段，使用默认标识" )

					# 添加到记录列表
					model_output_type = "Seg" if is_segment else "OBB" if is_obb else "Detection"
					record = {
						"image_id": image_id,
						"image_path": image_path,
						"image_width": image_width,
						"image_height": image_height,
						"data_source": data_source,
						"segmentation_data": segmentation_data,
						"obb_data": obb_data,
						"model_output_type": model_output_type,
						"training_type": training_type_json,  # PostgreSQLClient会自动处理JSON序列化
						"file_hash": None,  # 可为null，暂不计算哈希值
						"inference_count": 0  # 初始推理次数为0
					}
					records_to_insert.append( record )

				# 批量插入数据
				if records_to_insert:
					try:
						# 使用主线程数据库连接进行插入
						result = self.__db_client.insert_data( table_name, records_to_insert )
						if result.get( "success", False ):
							success_count += len( records_to_insert )
							# 只在控制台记录详细的批次信息
							self.__logger.info( f"批次 {batch_idx + 1} 插入成功: {len( records_to_insert )} 条" )
						else:
							self.__log_output.append( f"批量插入失败: {result.get( 'error', '未知错误' )}", Colors.RED )
							self.__logger.error( f"批量插入失败: {result.get( 'error', '未知错误' )}" )
					except Exception as db_e:
						self.__log_output.append( f"数据库操作异常: {str( db_e )}", Colors.RED )
						self.__logger.error( f"数据库操作异常: {str( db_e )}" )
						self.__logger.error( traceback.format_exc() )

			except Exception as e:
				self.__log_output.append( f"处理批次 {batch_idx + 1}/{len( batches )} 时出错: {str( e )}", Colors.RED )
				self.__logger.error( f"处理批次 {batch_idx + 1}/{len( batches )} 时出错: {str( e )}" )
				self.__logger.error( traceback.format_exc() )

			# UI显示简洁进度，控制台记录详细信息
			self.__log_output.append( f"进度: {batch_idx + 1}/{len( batches )}, 成功: {success_count}", Colors.GREEN )
			self.__logger.info( f"已处理 {batch_idx + 1}/{len( batches )} 批数据，当前成功: {success_count}" )

		# UI显示简洁结果
		self.__log_output.append( f"{training_type} 数据处理完成: {success_count} 条", Colors.GREEN )
		self.__logger.info( f"完成 {training_type} 数据处理: 总计 {success_count} 条记录" )
		return success_count

	def __process_all_data( self, data_result: Dict[ str, Any ], params: Dict[ str, Any ] ) -> Dict[ str, int ]:
		"""
		处理所有数据并保存到数据库（单线程版本，无重复数据）

		参数:
			data_result: 包含all_data和statistics的数据结果
			params: 处理参数

		返回:
			处理结果统计
		"""
		all_data = data_result[ "all_data" ]
		statistics = data_result[ "statistics" ]

		# UI显示简洁信息
		self.__log_output.append( f"开始处理数据: {statistics[ 'total' ]} 条", Colors.GREEN )
		# 控制台记录详细信息
		self.__logger.info( "开始单线程处理数据（无重复保存）" )
		self.__logger.info( f"总数据量: {statistics[ 'total' ]} 条" )

		try:
			# 只处理一次数据列表，每条数据根据其标识保存
			count = self.__save_to_database(
				all_data,
				"mixed",  # 混合数据类型，通过标识区分
				params[ "is_segment" ],
				params[ "is_obb" ],
				params[ "table_name" ],
				params[ "data_source" ],
				params[ "output_folder" ]
			)

			# UI显示简洁结果
			self.__log_output.append( f"数据处理完成: {count} 条记录", Colors.GREEN )
			self.__logger.info( f"数据处理完成: 实际保存 {count} 条记录" )

			# 返回统计信息（用于显示，但实际只保存了count条）
			return {
				"processed": count,
				"statistics": statistics
			}

		except Exception as e:
			self.__log_output.append( f"处理数据时出错: {str( e )}", Colors.RED )
			self.__logger.error( f"处理数据时出错: {str( e )}" )
			self.__logger.error( traceback.format_exc() )
			return {
				"processed": 0,
				"statistics": statistics
			}

	def process_data( self ) -> Dict[ str, Any ]:
		"""
		处理数据的主函数，执行完整的数据处理流程

		返回:
			处理结果字典

		使用示例:
			manager = DataProcessingManager(line_edit_manager, checkbox_manager, label_manager, log_output, logger)
			result = manager.process_data()
			if result["success"]:
				print(f"成功处理了 {result['details']['train']} 条训练数据")
			else:
				print(f"处理失败: {result['message']}")
		"""
		# 获取UI参数
		params = self.__get_ui_parameters()

		# 验证必须的参数
		if not params[ "input_folder" ]:
			self.__log_output.append( "未指定输入文件夹", Colors.RED )
			self.__logger.error( "未指定输入文件夹" )
			return { "success": False, "message": "未指定输入文件夹" }

		if not params[ "output_folder" ]:
			self.__log_output.append( "未指定输出文件夹", Colors.RED )
			self.__logger.error( "未指定输出文件夹" )
			return { "success": False, "message": "未指定输出文件夹" }

		if not params[ "is_segment" ] and not params[ "is_obb" ]:
			self.__log_output.append( "未选择模型类型（Segment或OBB）", Colors.RED )
			self.__logger.error( "未选择模型类型（Segment或OBB）" )
			return { "success": False, "message": "未选择模型类型" }

		self.__log_output.append( "开始处理数据", Colors.GREEN )
		self.__logger.info( "开始处理数据" )

		# 连接数据库
		if not self.__connect_database( params[ "db_name" ] ):
			return { "success": False, "message": "数据库连接失败" }

		# 创建表
		if not self.__create_table( params[ "table_name" ] ):
			return { "success": False, "message": "创建表失败" }

		# 读取JSON文件
		json_data_list = self.__read_json_files( params[ "input_folder" ] )
		if not json_data_list:
			return { "success": False, "message": "未找到有效的JSON文件" }

		# 初始化进度显示，总进度为JSON文件数量
		self.__initialize_progress( len( json_data_list ) )

		# 创建输出文件夹
		os.makedirs( params[ "output_folder" ], exist_ok=True )

		# 划分数据
		data_result = self.__split_data(
			json_data_list,
			params[ "train_ratio" ],
			params[ "pretrain_ratio" ]
		)

		# 处理并保存所有数据
		results = self.__process_all_data( data_result, params )

		# 获取实际处理的数量和统计信息
		actual_processed = results.get( "processed", 0 )
		statistics = results.get( "statistics", { } )

		# 完成进度显示
		self.__complete_progress()

		# UI显示简洁的最终结果
		self.__log_output.append( f"处理完成: {actual_processed} 条记录", Colors.GREEN )
		self.__logger.info( f"数据处理完成，实际保存 {actual_processed} 条记录（无重复）" )
		return {
			"success": True,
			"message": f"成功处理 {actual_processed} 条记录",
			"details": statistics  # 返回统计信息而不是重复的数据量
		}
